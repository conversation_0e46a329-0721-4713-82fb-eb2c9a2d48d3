[2025-07-17T21:34:31.037+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:34:31.038+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:34:31.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.041+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:34:31.124+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.124+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:31.137+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:34:31.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.398+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:34:31.420+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.390 seconds
[2025-07-17T21:35:02.594+0000] {processor.py:186} INFO - Started process (PID=399) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:02.595+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:35:02.597+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.597+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:02.861+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.861+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:02.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:02.974+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.974+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:02.985+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.984+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:35:03.006+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.418 seconds
[2025-07-17T21:35:33.072+0000] {processor.py:186} INFO - Started process (PID=535) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:33.073+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:35:33.076+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.076+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:33.163+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.162+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:33.171+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:33.283+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.283+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:33.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.292+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:35:33.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.246 seconds
[2025-07-17T21:36:03.407+0000] {processor.py:186} INFO - Started process (PID=669) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:03.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:36:03.410+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:03.491+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.491+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:03.502+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:03.605+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.605+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.616+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.616+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:36:03.637+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.236 seconds
[2025-07-17T21:36:34.400+0000] {processor.py:186} INFO - Started process (PID=805) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:34.401+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:36:34.403+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.403+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:34.477+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.477+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.484+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:34.573+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.573+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.585+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.585+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:36:34.604+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.210 seconds
[2025-07-17T21:37:04.911+0000] {processor.py:186} INFO - Started process (PID=941) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:04.912+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:37:04.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.914+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:04.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.995+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:05.003+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:05.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.117+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:05.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.127+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:37:05.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.242 seconds
[2025-07-17T21:37:35.234+0000] {processor.py:186} INFO - Started process (PID=1077) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:35.235+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:37:35.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.236+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:35.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.322+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.332+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:35.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.453+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:35.467+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.467+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:37:35.490+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.262 seconds
[2025-07-17T21:38:05.753+0000] {processor.py:186} INFO - Started process (PID=1213) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:05.754+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:38:05.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.756+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:05.851+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.850+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:05.863+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:05.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.987+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.002+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.002+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:38:06.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.281 seconds
[2025-07-17T21:38:36.669+0000] {processor.py:186} INFO - Started process (PID=1351) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:36.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:38:36.674+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:36.767+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.767+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:36.790+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:36.902+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.902+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:36.912+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.911+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:38:36.932+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.271 seconds
[2025-07-17T21:39:07.130+0000] {processor.py:186} INFO - Started process (PID=1485) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:07.131+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:39:07.133+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:07.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.216+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:07.224+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:07.324+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.324+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:39:07.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.240 seconds
[2025-07-17T21:39:37.681+0000] {processor.py:186} INFO - Started process (PID=1621) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:37.689+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:39:37.697+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.697+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:37.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.851+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:37.862+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:38.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.014+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:38.027+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.027+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:39:38.049+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.385 seconds
[2025-07-17T21:40:08.721+0000] {processor.py:186} INFO - Started process (PID=1757) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:08.722+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:40:08.725+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:08.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.818+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:08.827+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:08.945+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.945+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:08.958+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.957+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:40:08.977+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.264 seconds
[2025-07-17T21:40:39.814+0000] {processor.py:186} INFO - Started process (PID=1893) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:39.815+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:40:39.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.819+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:39.919+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.919+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:39.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:40.040+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.039+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:40.051+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.051+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:40:40.072+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.265 seconds
[2025-07-17T21:42:57.843+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:42:57.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:42:57.848+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:42:57.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.933+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:57.946+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:42:58.223+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.223+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:58.233+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:42:58.254+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.418 seconds
[2025-07-17T21:43:28.969+0000] {processor.py:186} INFO - Started process (PID=403) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:28.971+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:43:28.973+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.973+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:29.205+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.205+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:29.212+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:29.312+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.311+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:29.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.322+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:43:29.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.380 seconds
[2025-07-17T21:43:59.589+0000] {processor.py:186} INFO - Started process (PID=539) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:59.590+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:43:59.593+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.592+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:59.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.684+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:59.691+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:59.801+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.800+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.815+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.815+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:43:59.836+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.257 seconds
[2025-07-17T21:44:30.388+0000] {processor.py:186} INFO - Started process (PID=675) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:44:30.389+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:44:30.391+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.391+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:44:30.473+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.473+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.483+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:44:30.600+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.599+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:30.611+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.611+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:44:30.631+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.249 seconds
[2025-07-17T21:45:01.553+0000] {processor.py:186} INFO - Started process (PID=811) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:45:01.555+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:45:01.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.558+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:45:01.640+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.640+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:01.649+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:45:01.755+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.754+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:01.767+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.767+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:45:01.789+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.242 seconds
[2025-07-17T21:55:25.705+0000] {processor.py:186} INFO - Started process (PID=262) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:55:25.706+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:55:25.709+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.708+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:55:25.788+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.788+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:25.797+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:55:26.062+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:26.071+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.071+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:55:26.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.389 seconds
[2025-07-17T21:55:56.598+0000] {processor.py:186} INFO - Started process (PID=398) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:55:56.599+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:55:56.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.601+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:55:56.820+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.819+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:56.828+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:55:56.920+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.920+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:56.929+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.928+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:55:56.946+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.353 seconds
[2025-07-17T21:56:27.255+0000] {processor.py:186} INFO - Started process (PID=543) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:56:27.256+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:56:27.259+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.258+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:56:27.345+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.344+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:27.352+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:56:27.466+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.466+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:27.479+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.479+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:56:27.499+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.250 seconds
[2025-07-17T21:56:57.670+0000] {processor.py:186} INFO - Started process (PID=679) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:56:57.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:56:57.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.672+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:56:57.738+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.738+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:57.747+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:56:57.862+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.862+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:57.871+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.871+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:56:57.889+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.225 seconds
[2025-07-17T21:57:28.455+0000] {processor.py:186} INFO - Started process (PID=813) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:57:28.456+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:57:28.458+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.458+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:57:28.532+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.532+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:28.539+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:57:28.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.630+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:28.641+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.641+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:57:28.659+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.211 seconds
[2025-07-17T21:57:59.067+0000] {processor.py:186} INFO - Started process (PID=949) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:57:59.067+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:57:59.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.069+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:57:59.145+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.145+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:59.154+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:57:59.262+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.262+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:59.272+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.272+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:57:59.291+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.230 seconds
[2025-07-17T21:58:29.457+0000] {processor.py:186} INFO - Started process (PID=1085) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:58:29.458+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:58:29.460+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.459+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:58:29.533+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.533+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:29.540+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:58:29.627+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.627+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:29.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.637+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:58:29.653+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.202 seconds
[2025-07-17T22:00:37.205+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:00:37.206+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:00:37.208+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.208+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:00:37.285+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.285+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:37.298+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:00:37.560+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.559+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:37.568+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.567+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:00:37.586+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.388 seconds
[2025-07-17T22:01:08.290+0000] {processor.py:186} INFO - Started process (PID=397) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:01:08.291+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:01:08.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.293+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:01:08.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.541+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:08.548+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:01:08.675+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.675+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:08.687+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.687+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:01:08.710+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.426 seconds
[2025-07-17T22:01:39.198+0000] {processor.py:186} INFO - Started process (PID=533) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:01:39.199+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:01:39.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.201+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:01:39.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.274+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:39.282+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:01:39.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.383+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:39.394+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.394+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:01:39.412+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.219 seconds
[2025-07-17T22:02:09.692+0000] {processor.py:186} INFO - Started process (PID=669) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:02:09.693+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:02:09.696+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:02:09.766+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.765+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:09.772+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:02:09.863+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.863+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:09.873+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.873+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:02:09.890+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.203 seconds
[2025-07-17T22:02:40.085+0000] {processor.py:186} INFO - Started process (PID=805) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:02:40.086+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:02:40.088+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.088+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:02:40.155+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.154+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:40.162+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:02:40.249+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.249+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:40.259+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.259+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:02:40.275+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.195 seconds
[2025-07-17T22:03:10.408+0000] {processor.py:186} INFO - Started process (PID=941) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:03:10.409+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:03:10.412+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.412+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:03:10.488+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.488+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:10.498+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:03:10.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.606+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:10.617+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.617+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:03:10.636+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.234 seconds
[2025-07-17T22:03:40.723+0000] {processor.py:186} INFO - Started process (PID=1074) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:03:40.724+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:03:40.727+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:40.727+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:03:40.795+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:40.794+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:40.806+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:03:40.897+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:40.897+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:40.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:40.908+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:03:40.926+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.209 seconds
[2025-07-17T22:04:11.716+0000] {processor.py:186} INFO - Started process (PID=1213) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:04:11.717+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:04:11.720+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.720+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:04:11.800+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.800+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:11.807+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:04:11.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.906+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:11.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.918+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:04:11.935+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.225 seconds
[2025-07-17T22:04:42.318+0000] {processor.py:186} INFO - Started process (PID=1349) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:04:42.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:04:42.322+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:04:42.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.394+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:42.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:04:42.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.493+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:42.503+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.502+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:04:42.521+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.211 seconds
[2025-07-17T22:05:12.599+0000] {processor.py:186} INFO - Started process (PID=1485) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:05:12.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:05:12.602+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:12.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:05:12.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:12.679+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:12.689+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:05:12.795+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:12.795+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:12.807+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:12.807+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:05:12.826+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.234 seconds
[2025-07-17T22:05:43.314+0000] {processor.py:186} INFO - Started process (PID=1623) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:05:43.315+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:05:43.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:43.317+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:05:43.399+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:43.399+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:43.407+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:05:43.505+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:43.504+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:43.516+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:43.516+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:05:43.536+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.229 seconds
[2025-07-17T22:06:13.792+0000] {processor.py:186} INFO - Started process (PID=1759) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:06:13.793+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:06:13.796+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:13.795+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:06:13.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:13.859+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:13.869+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:06:13.961+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:13.961+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:13.970+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:13.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:06:13.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.202 seconds
[2025-07-17T22:07:28.800+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:07:28.801+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:07:28.803+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.803+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:07:28.868+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.868+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:28.879+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:07:29.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.122+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:29.135+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.135+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:07:29.155+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.361 seconds
[2025-07-17T22:08:00.493+0000] {processor.py:186} INFO - Started process (PID=397) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:08:00.494+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:08:00.497+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.496+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:08:00.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.731+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:00.738+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:08:00.826+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.826+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:00.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.834+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:08:00.853+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.365 seconds
[2025-07-17T22:08:31.079+0000] {processor.py:186} INFO - Started process (PID=533) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:08:31.080+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:08:31.083+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.083+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:08:31.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.176+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:31.184+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:08:31.285+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.284+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:31.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.294+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:08:31.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.242 seconds
[2025-07-17T22:09:01.622+0000] {processor.py:186} INFO - Started process (PID=671) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:09:01.623+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:09:01.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.626+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:09:01.696+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.696+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:01.704+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:09:01.796+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.795+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:01.806+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.805+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:09:01.824+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.207 seconds
