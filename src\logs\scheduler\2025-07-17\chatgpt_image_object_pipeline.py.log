[2025-07-17T22:41:26.806+0000] {processor.py:186} INFO - Started process (PID=182) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:26.807+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:41:26.810+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:26.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:26.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:26.918+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:26.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:27.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:27.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:41:27.210+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.412 seconds
[2025-07-17T22:41:58.069+0000] {processor.py:186} INFO - Started process (PID=319) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:58.071+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:41:58.074+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.073+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:58.152+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.152+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:58.161+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:58.478+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.477+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:58.498+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.498+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:41:58.521+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.460 seconds
[2025-07-17T22:42:28.708+0000] {processor.py:186} INFO - Started process (PID=450) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:42:28.709+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:42:28.711+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:28.710+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:42:28.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:28.962+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:28.969+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:42:29.059+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.059+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:29.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.070+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:42:29.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.388 seconds
[2025-07-17T22:42:59.808+0000] {processor.py:186} INFO - Started process (PID=581) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:42:59.809+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:42:59.810+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:59.810+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:42:59.894+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:59.893+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:59.903+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:43:00.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.015+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:00.031+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.030+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:43:00.051+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.251 seconds
[2025-07-17T22:43:30.189+0000] {processor.py:186} INFO - Started process (PID=712) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:43:30.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:43:30.191+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.191+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:43:30.266+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.266+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:30.276+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:43:30.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:30.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.397+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:43:30.419+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.236 seconds
[2025-07-17T22:44:01.315+0000] {processor.py:186} INFO - Started process (PID=843) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:01.316+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:44:01.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.317+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:01.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.394+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:01.405+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:01.516+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.515+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:01.530+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.530+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:44:01.554+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.245 seconds
[2025-07-17T22:44:32.346+0000] {processor.py:186} INFO - Started process (PID=974) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:32.347+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:44:32.349+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.348+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:32.427+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.427+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:32.437+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:32.538+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.538+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:32.549+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.549+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:44:32.570+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.230 seconds
[2025-07-17T22:45:03.241+0000] {processor.py:186} INFO - Started process (PID=1105) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:45:03.241+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:45:03.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.243+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:45:03.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.317+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:03.326+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:45:03.427+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:03.439+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.439+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:45:03.460+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.226 seconds
[2025-07-17T22:45:33.902+0000] {processor.py:186} INFO - Started process (PID=1236) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:45:33.903+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:45:33.905+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:33.905+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:45:33.981+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:33.981+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:33.991+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:45:34.099+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.099+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:34.111+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.111+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:45:34.142+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.246 seconds
[2025-07-17T22:46:04.875+0000] {processor.py:186} INFO - Started process (PID=1367) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:46:04.876+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:46:04.878+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:04.877+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:46:04.950+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:04.950+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:04.961+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:46:05.067+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.067+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:05.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.078+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:46:05.097+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.227 seconds
[2025-07-17T22:46:35.197+0000] {processor.py:186} INFO - Started process (PID=1498) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:46:35.198+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:46:35.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:46:35.273+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.273+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:35.283+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:46:35.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.386+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:35.399+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.399+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:46:35.419+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.228 seconds
[2025-07-17T22:47:06.289+0000] {processor.py:186} INFO - Started process (PID=1635) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:47:06.291+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:47:06.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.292+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:47:06.372+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.371+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:06.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:47:06.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.484+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:06.498+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.497+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:47:06.516+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.233 seconds
[2025-07-17T22:47:37.375+0000] {processor.py:186} INFO - Started process (PID=1772) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:47:37.377+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:47:37.378+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.378+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:47:37.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.458+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:37.469+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:47:37.580+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.580+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:37.595+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.594+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:47:37.616+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.248 seconds
[2025-07-17T22:48:08.269+0000] {processor.py:186} INFO - Started process (PID=1903) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:48:08.270+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:48:08.272+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.271+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:48:08.344+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.343+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:08.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:48:08.451+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.450+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:08.464+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.463+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:48:08.482+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.220 seconds
[2025-07-17T22:48:39.221+0000] {processor.py:186} INFO - Started process (PID=2034) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:48:39.222+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:48:39.225+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:48:39.308+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.308+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:39.320+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:48:39.428+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.427+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:39.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.443+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:48:39.465+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.250 seconds
[2025-07-17T22:49:09.620+0000] {processor.py:186} INFO - Started process (PID=2165) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:49:09.621+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:49:09.622+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:09.622+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:49:09.702+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:09.701+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:09.713+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:49:09.839+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:09.839+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:09.853+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:09.853+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:49:09.878+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.265 seconds
[2025-07-17T22:49:40.160+0000] {processor.py:186} INFO - Started process (PID=2296) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:49:40.160+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:49:40.162+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.162+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:49:40.231+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.231+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:40.241+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:49:40.352+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.352+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:40.364+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.364+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:49:40.382+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.229 seconds
[2025-07-17T22:50:10.749+0000] {processor.py:186} INFO - Started process (PID=2427) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:50:10.750+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:50:10.751+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:10.751+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:50:10.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:10.818+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:10.828+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:50:10.934+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:10.934+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:10.945+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:10.945+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:50:10.964+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.221 seconds
[2025-07-17T22:50:41.358+0000] {processor.py:186} INFO - Started process (PID=2558) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:50:41.359+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:50:41.361+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:41.360+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:50:41.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:41.442+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:41.453+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:50:41.552+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:41.552+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:41.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:41.563+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:50:41.585+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.236 seconds
[2025-07-17T22:51:12.082+0000] {processor.py:186} INFO - Started process (PID=2689) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:51:12.083+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:51:12.085+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.084+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:51:12.161+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.160+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:12.172+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:51:12.285+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.285+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:12.297+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.297+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:51:12.318+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.242 seconds
[2025-07-17T22:51:42.723+0000] {processor.py:186} INFO - Started process (PID=2820) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:51:42.725+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:51:42.726+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:42.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:51:42.825+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:42.825+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:42.833+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:51:42.936+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:42.936+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:42.947+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:42.947+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:51:42.968+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.251 seconds
[2025-07-17T22:52:13.492+0000] {processor.py:186} INFO - Started process (PID=2951) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:52:13.493+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:52:13.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:13.494+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:52:13.569+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:13.569+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:13.580+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:52:13.682+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:13.682+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:13.693+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:13.693+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:52:13.713+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.229 seconds
[2025-07-17T22:52:44.150+0000] {processor.py:186} INFO - Started process (PID=3082) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:52:44.151+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:52:44.152+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:44.151+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:52:44.230+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:44.230+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:44.241+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:52:44.354+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:44.354+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:44.366+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:44.366+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:52:44.387+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.243 seconds
[2025-07-17T22:53:14.626+0000] {processor.py:186} INFO - Started process (PID=3213) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:53:14.626+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:53:14.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:14.627+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:53:14.706+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:14.705+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:14.715+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:53:14.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:14.818+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:14.831+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:14.831+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:53:14.852+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.232 seconds
[2025-07-17T22:53:45.527+0000] {processor.py:186} INFO - Started process (PID=3350) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:53:45.529+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:53:45.530+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:45.530+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:53:45.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:45.605+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:45.615+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:53:45.722+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:45.721+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:45.738+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:45.738+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:53:45.763+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.242 seconds
[2025-07-17T22:54:16.480+0000] {processor.py:186} INFO - Started process (PID=3487) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:54:16.481+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:54:16.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:16.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:54:16.582+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:16.582+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:54:16.592+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:54:16.711+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:16.710+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:54:16.725+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:16.725+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:54:16.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.275 seconds
