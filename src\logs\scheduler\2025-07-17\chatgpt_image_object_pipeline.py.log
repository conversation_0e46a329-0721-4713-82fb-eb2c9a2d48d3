[2025-07-17T21:34:31.037+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:34:31.038+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:34:31.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.041+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:34:31.124+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.124+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:31.137+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:34:31.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.398+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:34:31.420+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.390 seconds
[2025-07-17T21:35:02.594+0000] {processor.py:186} INFO - Started process (PID=399) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:02.595+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:35:02.597+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.597+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:02.861+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.861+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:02.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:02.974+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.974+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:02.985+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.984+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:35:03.006+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.418 seconds
[2025-07-17T21:35:33.072+0000] {processor.py:186} INFO - Started process (PID=535) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:33.073+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:35:33.076+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.076+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:33.163+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.162+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:33.171+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:35:33.283+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.283+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:33.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.292+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:35:33.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.246 seconds
[2025-07-17T21:36:03.407+0000] {processor.py:186} INFO - Started process (PID=669) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:03.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:36:03.410+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:03.491+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.491+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:03.502+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:03.605+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.605+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.616+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.616+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:36:03.637+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.236 seconds
[2025-07-17T21:36:34.400+0000] {processor.py:186} INFO - Started process (PID=805) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:34.401+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:36:34.403+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.403+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:34.477+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.477+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.484+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:36:34.573+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.573+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.585+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.585+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:36:34.604+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.210 seconds
[2025-07-17T21:37:04.911+0000] {processor.py:186} INFO - Started process (PID=941) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:04.912+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:37:04.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.914+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:04.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.995+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:05.003+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:05.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.117+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:05.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.127+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:37:05.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.242 seconds
[2025-07-17T21:37:35.234+0000] {processor.py:186} INFO - Started process (PID=1077) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:35.235+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:37:35.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.236+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:35.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.322+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.332+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:37:35.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.453+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:35.467+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.467+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:37:35.490+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.262 seconds
[2025-07-17T21:38:05.753+0000] {processor.py:186} INFO - Started process (PID=1213) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:05.754+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:38:05.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.756+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:05.851+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.850+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:05.863+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:05.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.987+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.002+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.002+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:38:06.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.281 seconds
[2025-07-17T21:38:36.669+0000] {processor.py:186} INFO - Started process (PID=1351) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:36.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:38:36.674+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:36.767+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.767+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:36.790+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:38:36.902+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.902+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:36.912+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.911+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:38:36.932+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.271 seconds
[2025-07-17T21:39:07.130+0000] {processor.py:186} INFO - Started process (PID=1485) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:07.131+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:39:07.133+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:07.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.216+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:07.224+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:07.324+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.324+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:39:07.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.240 seconds
[2025-07-17T21:39:37.681+0000] {processor.py:186} INFO - Started process (PID=1621) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:37.689+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:39:37.697+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.697+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:37.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.851+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:37.862+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:39:38.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.014+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:38.027+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.027+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:39:38.049+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.385 seconds
[2025-07-17T21:40:08.721+0000] {processor.py:186} INFO - Started process (PID=1757) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:08.722+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:40:08.725+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:08.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.818+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:08.827+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:08.945+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.945+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:08.958+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.957+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:40:08.977+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.264 seconds
[2025-07-17T21:40:39.814+0000] {processor.py:186} INFO - Started process (PID=1893) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:39.815+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:40:39.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.819+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:39.919+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.919+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:39.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:40:40.040+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.039+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:40.051+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.051+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:40:40.072+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.265 seconds
[2025-07-17T21:42:57.843+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:42:57.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:42:57.848+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:42:57.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.933+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:57.946+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:42:58.223+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.223+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:58.233+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:42:58.254+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.418 seconds
[2025-07-17T21:43:28.969+0000] {processor.py:186} INFO - Started process (PID=403) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:28.971+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:43:28.973+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.973+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:29.205+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.205+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:29.212+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:29.312+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.311+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:29.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.322+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:43:29.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.380 seconds
[2025-07-17T21:43:59.589+0000] {processor.py:186} INFO - Started process (PID=539) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:59.590+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:43:59.593+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.592+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:59.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.684+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:59.691+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:43:59.801+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.800+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.815+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.815+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:43:59.836+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.257 seconds
[2025-07-17T21:44:30.388+0000] {processor.py:186} INFO - Started process (PID=675) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:44:30.389+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:44:30.391+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.391+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:44:30.473+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.473+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.483+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:44:30.600+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.599+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:30.611+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.611+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:44:30.631+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.249 seconds
[2025-07-17T21:45:01.553+0000] {processor.py:186} INFO - Started process (PID=811) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:45:01.555+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T21:45:01.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.558+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:45:01.640+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.640+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:01.649+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T21:45:01.755+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.754+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:01.767+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.767+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T21:45:01.789+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.242 seconds
