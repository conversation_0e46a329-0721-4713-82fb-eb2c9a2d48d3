[2025-07-17T22:41:26.806+0000] {processor.py:186} INFO - Started process (PID=182) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:26.807+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:41:26.810+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:26.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:26.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:26.918+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:26.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:27.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:27.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:41:27.210+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.412 seconds
[2025-07-17T22:41:58.069+0000] {processor.py:186} INFO - Started process (PID=319) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:58.071+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:41:58.074+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.073+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:58.152+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.152+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:58.161+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:41:58.478+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.477+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:58.498+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.498+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:41:58.521+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.460 seconds
[2025-07-17T22:42:28.708+0000] {processor.py:186} INFO - Started process (PID=450) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:42:28.709+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:42:28.711+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:28.710+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:42:28.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:28.962+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:28.969+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:42:29.059+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.059+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:29.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.070+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:42:29.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.388 seconds
[2025-07-17T22:42:59.808+0000] {processor.py:186} INFO - Started process (PID=581) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:42:59.809+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:42:59.810+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:59.810+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:42:59.894+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:59.893+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:59.903+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:43:00.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.015+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:00.031+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.030+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:43:00.051+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.251 seconds
[2025-07-17T22:43:30.189+0000] {processor.py:186} INFO - Started process (PID=712) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:43:30.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:43:30.191+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.191+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:43:30.266+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.266+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:30.276+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:43:30.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:30.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.397+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:43:30.419+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.236 seconds
[2025-07-17T22:44:01.315+0000] {processor.py:186} INFO - Started process (PID=843) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:01.316+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:44:01.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.317+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:01.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.394+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:01.405+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:01.516+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.515+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:01.530+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.530+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:44:01.554+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.245 seconds
[2025-07-17T22:44:32.346+0000] {processor.py:186} INFO - Started process (PID=974) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:32.347+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:44:32.349+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.348+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:32.427+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.427+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:32.437+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:44:32.538+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.538+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:32.549+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.549+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:44:32.570+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.230 seconds
