[2025-07-17T22:21:48.951+0000] {processor.py:186} INFO - Started process (PID=3966) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:21:48.952+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-17T22:21:48.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.953+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:21:49.018+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:49.018+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:21:49.028+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-17T22:21:49.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:49.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:21:49.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:49.123+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-17T22:21:49.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.194 seconds
