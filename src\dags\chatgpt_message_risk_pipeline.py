from datetime import datetime
import configparser
import requests
from airflow.decorators import dag, task
import tiktoken

# Читаем конфиг и извлекаем токен
config = configparser.ConfigParser()
config.read("/opt/airflow/pipe/config.ini")
API_TOKEN_GPT = config.get('TOKENS', 'API_TOKEN_GPT')

default_args = {
    'owner': 'Nikita Litvinov',
    'retries': 0,
}

@dag(dag_id='chatgpt_message_risk_pipeline',
     default_args=default_args,
     start_date=datetime.now(),
     catchup=False,
     schedule_interval=None)
def chatgpt():
    @task()
    def generate_openai_response(**kwargs):
        def analyze_token_usage(input_prompt: str, output_response: str, cost_rate_input: float = 0.03,
                                cost_rate_output: float = 0.06, tokens_unit: int = 1000, model: str = "gpt-4o"):
            """
            Анализирует использование токенов для входного промпта и выходного ответа с использованием tiktoken.
            Вычисляет:
              - Точное количество токенов для входного промпта
              - Точное количество токенов для выходного ответа
              - Стоимость токенов на основе заданных тарифов
            """
            encoder = tiktoken.encoding_for_model(model)
            tokens_input = len(encoder.encode(input_prompt))
            tokens_output = len(encoder.encode(output_response))

            cost_input = (tokens_input / tokens_unit) * cost_rate_input
            cost_output = (tokens_output / tokens_unit) * cost_rate_output

            print(f"Input tokens: {tokens_input}, Output tokens: {tokens_output}")
            print(f"Cost for input tokens: ${cost_input:.4f}, Cost for output tokens: ${cost_output:.4f}")

            return {
                "tokens_input": tokens_input,
                "tokens_output": tokens_output,
                "cost_input": cost_input,
                "cost_output": cost_output
            }

        conf = kwargs.get('dag_run').conf

        if not conf or "usr_msg" not in conf:
            raise ValueError("Не найдено сообщение пользователя в конфигурации DAG.")
        msg = conf["usr_msg"]

        if not conf or "prompt" not in conf:
            raise ValueError("Не найден промпт в конфигурации DAG.")
        prompt = conf["prompt"]

        history = conf.get("history", [])
        if not history:
            history = [
                {"role": "system", "content": prompt},
                {"role": "user", "content": msg},
            ]

        belgium_proxies = {
            "http": "http://77.221.142.174:8888",
            "https": "http://77.221.142.174:8888"
        }
        germany_proxies = {
            "http": "http://77.221.142.174:8888",
            "https": "http://77.221.142.174:8888"
        }

        headers = {
            "Authorization": f"Bearer {API_TOKEN_GPT}",
            "Content-Type": "application/json"
        }

        data = {
            "model": "gpt-4o",
            "temperature": 0,
            "messages": history
        }

        url = "https://api.openai.com/v1/chat/completions"
        max_retries = 2
        response = None
        for attempt in range(max_retries):
            try:
                response = requests.post(url, json=data, headers=headers, proxies=germany_proxies, timeout=90)
                response.raise_for_status()
                break
            except requests.exceptions.Timeout:
                if attempt == max_retries - 1:
                    raise
        if response is None:
            raise Exception("No response received after retries")
        # response = requests.post(url, json=data, headers=headers)

        content = response.json()["choices"][0]["message"]["content"]
        # stats = analyze_token_usage(prompt, content)
        # print(stats)
        return content.strip('"')

    generate_openai_response(provide_context=True)

dag_chatgpt = chatgpt()