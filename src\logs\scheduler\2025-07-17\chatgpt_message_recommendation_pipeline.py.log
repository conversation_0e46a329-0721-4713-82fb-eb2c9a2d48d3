[2025-07-17T22:41:29.151+0000] {processor.py:186} INFO - Started process (PID=231) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:41:29.152+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:41:29.154+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.154+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:41:29.233+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.233+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:29.241+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:41:29.354+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.354+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.519+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.519+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:41:29.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.401 seconds
[2025-07-17T22:41:59.848+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:41:59.849+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:41:59.851+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.851+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:41:59.920+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.920+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:42:00.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.175+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:00.189+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.188+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:42:00.206+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.365 seconds
[2025-07-17T22:42:30.647+0000] {processor.py:186} INFO - Started process (PID=493) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:42:30.648+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:42:30.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.649+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:42:30.858+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.858+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.866+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:42:30.951+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.951+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.960+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.959+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:42:30.977+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.337 seconds
[2025-07-17T22:43:01.390+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:43:01.391+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:43:01.392+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.392+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:43:01.476+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.475+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:01.484+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:43:01.580+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.580+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:01.590+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.590+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:43:01.609+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.225 seconds
[2025-07-17T22:43:31.838+0000] {processor.py:186} INFO - Started process (PID=757) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:43:31.839+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:43:31.841+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.841+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:43:31.914+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.914+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:31.922+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:43:32.017+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.017+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:32.029+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.029+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:43:32.046+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.214 seconds
[2025-07-17T22:44:02.683+0000] {processor.py:186} INFO - Started process (PID=886) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:44:02.684+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:44:02.685+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.685+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:44:02.768+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.767+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:02.776+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:44:02.879+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.879+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.891+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.891+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:44:02.908+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.232 seconds
[2025-07-17T22:44:33.648+0000] {processor.py:186} INFO - Started process (PID=1017) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:44:33.649+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:44:33.651+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:44:33.723+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.723+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:33.732+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:44:33.850+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.850+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.863+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.863+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:44:33.885+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.242 seconds
[2025-07-17T22:45:04.810+0000] {processor.py:186} INFO - Started process (PID=1150) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:45:04.811+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:45:04.813+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.813+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:45:04.883+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.883+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:04.890+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:45:04.984+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.984+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:04.996+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.996+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:45:05.015+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.212 seconds
[2025-07-17T22:45:35.625+0000] {processor.py:186} INFO - Started process (PID=1281) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:45:35.627+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:45:35.629+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.628+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:45:35.709+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.709+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:35.717+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:45:35.815+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.815+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:35.827+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.826+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:45:35.846+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.230 seconds
[2025-07-17T22:46:06.446+0000] {processor.py:186} INFO - Started process (PID=1412) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:46:06.447+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:46:06.448+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.448+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:46:06.521+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.521+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:06.531+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:46:06.633+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.633+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:06.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.645+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:46:06.668+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.228 seconds
[2025-07-17T22:46:36.730+0000] {processor.py:186} INFO - Started process (PID=1543) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:46:36.731+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:46:36.732+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.732+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:46:36.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.804+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:36.813+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:46:36.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.905+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:36.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.915+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:46:36.934+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.209 seconds
[2025-07-17T22:47:07.756+0000] {processor.py:186} INFO - Started process (PID=1680) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:47:07.757+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:47:07.758+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.758+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:47:07.824+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.823+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:07.831+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:47:07.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.923+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:07.935+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.935+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:47:07.954+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.204 seconds
[2025-07-17T22:47:38.767+0000] {processor.py:186} INFO - Started process (PID=1815) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:47:38.768+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:47:38.769+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.769+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:47:38.842+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.841+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:38.851+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:47:38.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.962+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:38.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.977+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:47:39.002+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.241 seconds
[2025-07-17T22:48:09.761+0000] {processor.py:186} INFO - Started process (PID=1948) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:48:09.762+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:48:09.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.763+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:48:09.828+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.827+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:09.835+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:48:09.934+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.933+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:09.945+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.945+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:48:09.965+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.209 seconds
[2025-07-17T22:48:40.579+0000] {processor.py:186} INFO - Started process (PID=2077) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:48:40.580+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:48:40.582+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.582+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:48:40.657+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.656+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:40.663+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:48:40.768+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.767+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:40.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.779+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:48:40.801+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.228 seconds
[2025-07-17T22:49:11.465+0000] {processor.py:186} INFO - Started process (PID=2210) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:49:11.467+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:49:11.469+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.468+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:49:11.564+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.564+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:11.572+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:49:11.686+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.686+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:11.697+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.697+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:49:11.718+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.259 seconds
[2025-07-17T22:49:42.373+0000] {processor.py:186} INFO - Started process (PID=2341) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:49:42.374+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T22:49:42.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.375+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:49:42.441+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.441+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:42.449+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T22:49:42.557+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.557+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:42.568+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.568+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T22:49:42.583+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.215 seconds
