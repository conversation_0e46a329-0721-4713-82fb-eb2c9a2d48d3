[2025-07-17T21:34:28.887+0000] {processor.py:186} INFO - Started process (PID=206) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:34:28.888+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:34:28.890+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.890+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:34:28.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.974+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:28.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:34:29.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:29.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.274+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:34:29.293+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.413 seconds
[2025-07-17T21:34:59.766+0000] {processor.py:186} INFO - Started process (PID=342) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:34:59.767+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:34:59.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.769+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:34:59.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.844+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:59.854+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:35:00.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.104+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:00.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.114+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:35:00.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.373 seconds
[2025-07-17T21:35:30.843+0000] {processor.py:186} INFO - Started process (PID=478) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:35:30.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:35:30.847+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:35:30.927+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.927+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:30.935+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:35:31.029+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.029+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:31.042+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:35:31.062+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.225 seconds
[2025-07-17T21:36:01.196+0000] {processor.py:186} INFO - Started process (PID=614) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:01.197+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:36:01.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:01.273+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.272+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.281+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:01.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.387+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:01.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.397+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:36:01.418+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.227 seconds
[2025-07-17T21:36:31.499+0000] {processor.py:186} INFO - Started process (PID=750) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:31.500+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:36:31.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.502+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:31.577+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.577+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:31.586+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:31.688+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.688+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.702+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.702+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:36:31.724+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.230 seconds
[2025-07-17T21:37:01.887+0000] {processor.py:186} INFO - Started process (PID=886) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:01.888+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:37:01.890+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.890+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:01.972+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.972+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:01.981+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:02.095+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.094+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:02.109+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.109+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:37:02.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.253 seconds
[2025-07-17T21:37:32.569+0000] {processor.py:186} INFO - Started process (PID=1022) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:32.570+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:37:32.572+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.572+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:32.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.646+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:32.656+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:32.754+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.754+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:32.764+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.764+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:37:32.788+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.225 seconds
[2025-07-17T21:38:03.022+0000] {processor.py:186} INFO - Started process (PID=1158) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:03.024+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:38:03.028+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.028+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:03.128+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.127+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:03.136+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:03.229+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.229+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:03.240+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.240+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:38:03.262+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.247 seconds
[2025-07-17T21:38:33.565+0000] {processor.py:186} INFO - Started process (PID=1294) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:33.566+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:38:33.569+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.568+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:33.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.646+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:33.654+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:33.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:33.769+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.769+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:38:33.791+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.232 seconds
[2025-07-17T21:39:04.442+0000] {processor.py:186} INFO - Started process (PID=1430) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:04.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:39:04.445+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:04.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.521+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:04.532+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:04.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.635+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:04.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.646+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:39:04.666+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.230 seconds
[2025-07-17T21:39:35.044+0000] {processor.py:186} INFO - Started process (PID=1566) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:35.045+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:39:35.047+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.047+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:35.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.115+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.125+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:35.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.216+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:35.226+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.225+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:39:35.245+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.207 seconds
[2025-07-17T21:40:05.736+0000] {processor.py:186} INFO - Started process (PID=1702) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:40:05.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:40:05.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:40:05.831+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.830+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:05.841+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:40:05.949+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.949+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:05.961+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:40:05.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.258 seconds
