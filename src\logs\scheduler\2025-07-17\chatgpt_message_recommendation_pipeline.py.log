[2025-07-17T21:34:28.887+0000] {processor.py:186} INFO - Started process (PID=206) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:34:28.888+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:34:28.890+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.890+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:34:28.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.974+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:28.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:34:29.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:29.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.274+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:34:29.293+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.413 seconds
[2025-07-17T21:34:59.766+0000] {processor.py:186} INFO - Started process (PID=342) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:34:59.767+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:34:59.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.769+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:34:59.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.844+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:59.854+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:35:00.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.104+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:00.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.114+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:35:00.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.373 seconds
[2025-07-17T21:35:30.843+0000] {processor.py:186} INFO - Started process (PID=478) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:35:30.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:35:30.847+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:35:30.927+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.927+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:30.935+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:35:31.029+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.029+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:31.042+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:35:31.062+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.225 seconds
[2025-07-17T21:36:01.196+0000] {processor.py:186} INFO - Started process (PID=614) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:01.197+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:36:01.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:01.273+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.272+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.281+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:01.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.387+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:01.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.397+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:36:01.418+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.227 seconds
[2025-07-17T21:36:31.499+0000] {processor.py:186} INFO - Started process (PID=750) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:31.500+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:36:31.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.502+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:31.577+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.577+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:31.586+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:36:31.688+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.688+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.702+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.702+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:36:31.724+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.230 seconds
[2025-07-17T21:37:01.887+0000] {processor.py:186} INFO - Started process (PID=886) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:01.888+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:37:01.890+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.890+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:01.972+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.972+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:01.981+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:02.095+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.094+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:02.109+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.109+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:37:02.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.253 seconds
[2025-07-17T21:37:32.569+0000] {processor.py:186} INFO - Started process (PID=1022) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:32.570+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:37:32.572+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.572+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:32.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.646+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:32.656+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:37:32.754+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.754+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:32.764+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.764+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:37:32.788+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.225 seconds
[2025-07-17T21:38:03.022+0000] {processor.py:186} INFO - Started process (PID=1158) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:03.024+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:38:03.028+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.028+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:03.128+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.127+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:03.136+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:03.229+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.229+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:03.240+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.240+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:38:03.262+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.247 seconds
[2025-07-17T21:38:33.565+0000] {processor.py:186} INFO - Started process (PID=1294) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:33.566+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:38:33.569+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.568+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:33.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.646+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:33.654+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:38:33.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:33.769+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.769+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:38:33.791+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.232 seconds
[2025-07-17T21:39:04.442+0000] {processor.py:186} INFO - Started process (PID=1430) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:04.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:39:04.445+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:04.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.521+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:04.532+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:04.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.635+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:04.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.646+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:39:04.666+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.230 seconds
[2025-07-17T21:39:35.044+0000] {processor.py:186} INFO - Started process (PID=1566) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:35.045+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:39:35.047+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.047+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:35.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.115+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.125+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:39:35.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.216+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:35.226+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.225+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:39:35.245+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.207 seconds
[2025-07-17T21:40:05.736+0000] {processor.py:186} INFO - Started process (PID=1702) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:40:05.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:40:05.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:40:05.831+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.830+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:05.841+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:40:05.949+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.949+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:05.961+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:40:05.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.258 seconds
[2025-07-17T21:40:36.565+0000] {processor.py:186} INFO - Started process (PID=1838) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:40:36.566+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:40:36.569+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.569+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:40:36.657+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.657+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:36.667+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:40:36.798+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.798+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:36.809+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.809+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:40:36.829+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.272 seconds
[2025-07-17T21:41:08.148+0000] {processor.py:186} INFO - Started process (PID=2036) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:41:08.149+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:41:08.155+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:08.154+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:41:08.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:08.302+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:41:08.319+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:41:09.734+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:09.734+0000] {process_utils.py:266} INFO - Waiting up to 5 seconds for processes to exit...
[2025-07-17T21:42:55.704+0000] {processor.py:186} INFO - Started process (PID=206) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:42:55.705+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:42:55.708+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.708+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:42:55.794+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.794+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:55.801+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:42:55.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.917+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:56.077+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.077+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:42:56.098+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.400 seconds
[2025-07-17T21:43:26.687+0000] {processor.py:186} INFO - Started process (PID=344) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:43:26.688+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:43:26.690+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.690+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:43:26.772+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.772+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:26.780+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:43:27.049+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.049+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:27.062+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.062+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:43:27.082+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.401 seconds
[2025-07-17T21:43:57.915+0000] {processor.py:186} INFO - Started process (PID=480) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:43:57.916+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:43:57.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.918+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:43:58.008+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.007+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:58.016+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:43:58.140+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.140+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:58.153+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.153+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:43:58.174+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.265 seconds
[2025-07-17T21:44:28.436+0000] {processor.py:186} INFO - Started process (PID=616) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:44:28.437+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:44:28.439+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.439+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:44:28.515+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.515+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:28.524+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:44:28.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.646+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:28.659+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.658+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:44:28.681+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.250 seconds
[2025-07-17T21:44:58.794+0000] {processor.py:186} INFO - Started process (PID=750) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:44:58.796+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:44:58.799+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.799+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:44:58.897+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.897+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:58.906+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:44:59.036+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.036+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:59.052+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.051+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:44:59.077+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.295 seconds
[2025-07-17T21:55:23.663+0000] {processor.py:186} INFO - Started process (PID=207) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:55:23.664+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:55:23.667+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.667+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:55:23.755+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.755+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:23.762+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:55:23.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.860+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:23.996+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.995+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:55:24.015+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.363 seconds
[2025-07-17T21:55:54.173+0000] {processor.py:186} INFO - Started process (PID=343) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:55:54.174+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:55:54.177+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.176+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:55:54.240+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.239+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:54.248+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:55:54.490+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.489+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:54.499+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.499+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:55:54.519+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.351 seconds
[2025-07-17T21:56:24.924+0000] {processor.py:186} INFO - Started process (PID=479) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:56:24.925+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:56:24.927+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.927+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:56:24.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.994+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:25.004+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:56:25.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.113+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:25.126+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.125+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:56:25.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.229 seconds
[2025-07-17T21:56:55.236+0000] {processor.py:186} INFO - Started process (PID=616) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:56:55.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:56:55.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:56:55.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.317+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:55.326+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:56:55.430+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.430+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:55.440+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.440+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:56:55.459+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.230 seconds
[2025-07-17T21:57:25.667+0000] {processor.py:186} INFO - Started process (PID=752) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:57:25.668+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:57:25.671+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:57:25.754+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.754+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:25.764+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:57:25.869+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.869+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:25.880+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.880+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:57:25.901+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.240 seconds
[2025-07-17T21:57:56.087+0000] {processor.py:186} INFO - Started process (PID=888) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:57:56.088+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:57:56.090+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.090+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:57:56.155+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.155+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:56.162+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:57:56.263+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.263+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:56.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.274+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:57:56.291+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.214 seconds
[2025-07-17T21:58:26.567+0000] {processor.py:186} INFO - Started process (PID=1024) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:58:26.568+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-17T21:58:26.571+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.570+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:58:26.638+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.638+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:26.646+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-17T21:58:26.750+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.750+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:26.761+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.761+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-17T21:58:26.781+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.219 seconds
