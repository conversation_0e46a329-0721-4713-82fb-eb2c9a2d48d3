[2025-07-17T21:34:31.905+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:34:31.906+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:34:31.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:34:31.992+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.992+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.001+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:34:32.282+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.282+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.292+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.291+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:34:32.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.414 seconds
[2025-07-17T21:35:03.484+0000] {processor.py:186} INFO - Started process (PID=419) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:03.485+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:35:03.487+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.487+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:03.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.717+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:03.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:03.831+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.831+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.841+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.841+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:35:03.862+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.383 seconds
[2025-07-17T21:35:34.002+0000] {processor.py:186} INFO - Started process (PID=555) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:34.004+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:35:34.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:34.087+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.087+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:34.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:34.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.194+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:34.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.206+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:35:34.224+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.227 seconds
[2025-07-17T21:36:04.299+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:04.300+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:36:04.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.302+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:04.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.382+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:04.391+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:04.489+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.489+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:04.500+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:36:04.522+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.229 seconds
[2025-07-17T21:36:34.904+0000] {processor.py:186} INFO - Started process (PID=825) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:34.905+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:36:34.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:34.983+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.983+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.990+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:35.099+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:35.099+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:35.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:35.113+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:36:35.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.234 seconds
[2025-07-17T21:37:05.466+0000] {processor.py:186} INFO - Started process (PID=961) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:05.467+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:37:05.470+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.469+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:05.547+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.546+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:05.555+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:05.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.656+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:05.667+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.667+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:37:05.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.227 seconds
[2025-07-17T21:37:35.841+0000] {processor.py:186} INFO - Started process (PID=1097) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:35.842+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:37:35.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.845+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:35.935+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.935+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.947+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:36.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:36.043+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:36.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:36.057+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:37:36.076+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.241 seconds
[2025-07-17T21:38:06.479+0000] {processor.py:186} INFO - Started process (PID=1233) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:06.480+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:38:06.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:06.575+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.575+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:06.582+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:06.685+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.684+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.697+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.696+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:38:06.717+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.245 seconds
[2025-07-17T21:38:37.518+0000] {processor.py:186} INFO - Started process (PID=1371) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:37.520+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:38:37.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.522+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:37.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.603+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:37.612+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:37.716+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.716+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:37.726+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.726+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:38:37.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.235 seconds
[2025-07-17T21:39:07.969+0000] {processor.py:186} INFO - Started process (PID=1507) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:07.970+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:39:07.973+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.972+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:08.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:08.045+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:08.052+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:08.153+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:08.153+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:08.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:08.168+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:39:08.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.237 seconds
[2025-07-17T21:39:38.416+0000] {processor.py:186} INFO - Started process (PID=1641) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:38.417+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:39:38.419+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.419+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:38.515+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.514+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:38.523+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:38.647+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.647+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:38.663+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:39:38.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.276 seconds
[2025-07-17T21:40:09.315+0000] {processor.py:186} INFO - Started process (PID=1777) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:40:09.317+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:40:09.320+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.319+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:40:09.405+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.405+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:09.413+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:40:09.528+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.528+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:09.552+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.552+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:40:09.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.268 seconds
[2025-07-17T21:40:40.422+0000] {processor.py:186} INFO - Started process (PID=1913) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:40:40.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:40:40.426+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.425+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:40:40.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.513+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:40.522+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:40:40.651+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.650+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:40.663+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:40:40.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.270 seconds
[2025-07-17T21:42:58.762+0000] {processor.py:186} INFO - Started process (PID=287) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:42:58.763+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:42:58.766+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.766+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:42:58.848+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.847+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:58.856+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:42:59.093+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.093+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:59.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.107+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:42:59.128+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.374 seconds
[2025-07-17T21:43:29.844+0000] {processor.py:186} INFO - Started process (PID=423) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:43:29.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:43:29.848+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.848+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:43:30.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.079+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:30.085+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:43:30.196+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.196+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:30.207+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.207+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:43:30.227+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.389 seconds
[2025-07-17T21:44:00.344+0000] {processor.py:186} INFO - Started process (PID=561) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:44:00.346+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:44:00.349+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.348+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:44:00.425+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.425+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:00.435+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:44:00.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:00.557+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:44:00.581+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.243 seconds
[2025-07-17T21:44:30.971+0000] {processor.py:186} INFO - Started process (PID=695) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:44:30.972+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:44:30.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.974+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:44:31.058+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.058+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:31.066+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:44:31.181+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.181+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:31.191+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.191+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:44:31.211+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.249 seconds
[2025-07-17T21:45:02.116+0000] {processor.py:186} INFO - Started process (PID=831) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:45:02.117+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:45:02.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.119+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:45:02.198+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.198+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:02.207+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:45:02.313+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.312+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:02.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.322+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:45:02.342+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.232 seconds
[2025-07-17T21:55:26.520+0000] {processor.py:186} INFO - Started process (PID=282) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:55:26.521+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:55:26.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.523+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:55:26.614+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.613+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:26.623+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:55:26.866+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.866+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:26.875+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.875+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:55:26.892+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.378 seconds
[2025-07-17T21:55:57.382+0000] {processor.py:186} INFO - Started process (PID=424) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:55:57.383+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:55:57.385+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.385+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:55:57.575+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.575+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:57.581+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:55:57.678+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.678+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:57.689+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.689+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:55:57.705+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.329 seconds
[2025-07-17T21:56:28.091+0000] {processor.py:186} INFO - Started process (PID=563) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:56:28.092+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:56:28.094+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.094+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:56:28.184+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.184+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:28.195+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:56:28.304+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.304+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:28.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.317+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:56:28.339+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.254 seconds
[2025-07-17T21:56:58.451+0000] {processor.py:186} INFO - Started process (PID=699) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:56:58.452+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:56:58.455+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.454+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:56:58.527+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.527+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:58.536+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:56:58.645+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.645+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:58.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.656+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:56:58.679+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.235 seconds
[2025-07-17T21:57:28.943+0000] {processor.py:186} INFO - Started process (PID=833) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:57:28.945+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:57:28.947+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.947+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:57:29.018+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.018+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:29.025+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:57:29.118+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.117+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:29.128+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.128+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:57:29.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.209 seconds
[2025-07-17T21:57:59.621+0000] {processor.py:186} INFO - Started process (PID=969) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:57:59.622+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:57:59.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.624+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:57:59.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.715+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:59.726+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:57:59.861+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.861+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:59.872+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.872+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:57:59.890+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.277 seconds
[2025-07-17T21:58:29.997+0000] {processor.py:186} INFO - Started process (PID=1105) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:58:29.998+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:58:30.001+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.000+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:58:30.076+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.076+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:30.083+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:58:30.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.176+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:30.186+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:58:30.203+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.212 seconds
[2025-07-17T22:00:38.014+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:00:38.015+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:00:38.017+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.016+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:00:38.081+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.081+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:38.087+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:00:38.320+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.320+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:38.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:00:38.343+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.335 seconds
[2025-07-17T22:01:09.226+0000] {processor.py:186} INFO - Started process (PID=417) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:01:09.228+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:01:09.230+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.230+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:01:09.500+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.500+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:09.510+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:01:09.619+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.619+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:09.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.631+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:01:09.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.429 seconds
[2025-07-17T22:01:39.714+0000] {processor.py:186} INFO - Started process (PID=553) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:01:39.715+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:01:39.718+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.717+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:01:39.786+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.786+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:39.792+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:01:39.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.893+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:39.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.908+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:01:39.930+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.221 seconds
[2025-07-17T22:02:10.195+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:02:10.196+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:02:10.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:02:10.269+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.269+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:10.276+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:02:10.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.374+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:10.385+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.385+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:02:10.400+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.210 seconds
[2025-07-17T22:02:40.589+0000] {processor.py:186} INFO - Started process (PID=827) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:02:40.590+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:02:40.592+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.592+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:02:40.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.655+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:40.663+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:02:40.754+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:40.765+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.764+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:02:40.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.200 seconds
[2025-07-17T22:03:10.996+0000] {processor.py:186} INFO - Started process (PID=961) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:03:10.997+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:03:11.000+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.999+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:03:11.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:11.064+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:11.072+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:03:11.167+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:11.167+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:11.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:11.176+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:03:11.194+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.204 seconds
[2025-07-17T22:03:42.026+0000] {processor.py:186} INFO - Started process (PID=1097) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:03:42.036+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:03:42.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:42.055+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:03:42.634+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:42.634+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:42.738+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:03:43.090+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.089+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:43.111+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.110+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:03:43.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 1.130 seconds
[2025-07-17T22:04:13.252+0000] {processor.py:186} INFO - Started process (PID=1233) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:04:13.253+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:04:13.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.255+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:04:13.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.328+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:13.335+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:04:13.430+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.430+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:13.441+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.440+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:04:13.458+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.212 seconds
[2025-07-17T22:04:43.680+0000] {processor.py:186} INFO - Started process (PID=1369) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:04:43.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:04:43.683+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:43.683+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:04:43.751+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:43.750+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:43.760+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:04:43.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:43.860+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:43.870+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:43.870+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:04:43.889+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.214 seconds
[2025-07-17T22:05:14.175+0000] {processor.py:186} INFO - Started process (PID=1505) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:05:14.176+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:05:14.178+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.178+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:05:14.254+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.253+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:14.264+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:05:14.379+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.379+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:14.392+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.392+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:05:14.414+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.245 seconds
[2025-07-17T22:05:44.631+0000] {processor.py:186} INFO - Started process (PID=1641) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:05:44.632+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:05:44.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:44.634+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:05:44.710+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:44.710+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:44.719+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:05:44.846+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:44.846+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:44.870+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:44.870+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:05:44.898+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.272 seconds
[2025-07-17T22:06:15.085+0000] {processor.py:186} INFO - Started process (PID=1777) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:06:15.086+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:06:15.090+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.089+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:06:15.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.164+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:15.178+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:06:15.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.293+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:15.305+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.305+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:06:15.323+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.245 seconds
[2025-07-17T22:07:29.575+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:07:29.576+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:07:29.578+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.578+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:07:29.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.645+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:29.653+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:07:29.870+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.870+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:29.879+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.879+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:07:29.895+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.325 seconds
[2025-07-17T22:08:01.261+0000] {processor.py:186} INFO - Started process (PID=419) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:08:01.262+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:08:01.265+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.264+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:08:01.452+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.452+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:01.460+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:08:01.542+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.541+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:01.551+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.550+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:08:01.568+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.312 seconds
[2025-07-17T22:08:31.650+0000] {processor.py:186} INFO - Started process (PID=555) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:08:31.651+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:08:31.653+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.653+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:08:31.726+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.726+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:31.734+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:08:31.839+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.839+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:31.854+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.854+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:08:31.873+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.229 seconds
[2025-07-17T22:09:02.347+0000] {processor.py:186} INFO - Started process (PID=691) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:09:02.348+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:09:02.349+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:02.349+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:09:02.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:02.415+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:02.422+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:09:02.508+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:02.508+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:02.519+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:02.518+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:09:02.543+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.201 seconds
