[2025-07-17T22:41:30.782+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:41:30.782+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:41:30.785+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.784+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:41:30.866+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.866+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:30.872+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:41:31.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.131+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.140+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:41:31.158+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.382 seconds
[2025-07-17T22:42:01.842+0000] {processor.py:186} INFO - Started process (PID=397) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:01.843+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:42:01.846+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.846+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:01.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.917+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:01.926+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:02.198+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.198+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:02.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.209+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:42:02.225+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.390 seconds
[2025-07-17T22:42:32.949+0000] {processor.py:186} INFO - Started process (PID=530) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:32.950+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:42:32.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.952+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:33.185+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.185+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:33.192+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:33.288+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.288+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:33.298+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.297+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:42:33.315+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.372 seconds
