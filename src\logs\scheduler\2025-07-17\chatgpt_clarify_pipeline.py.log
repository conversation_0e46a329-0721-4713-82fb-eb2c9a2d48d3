[2025-07-17T22:21:50.001+0000] {processor.py:186} INFO - Started process (PID=3989) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:21:50.002+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:21:50.003+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:50.003+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:21:50.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:50.082+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:21:50.089+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:21:50.185+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:50.185+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:21:50.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:50.195+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:21:50.211+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.216 seconds
