[2025-07-17T22:41:30.782+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:41:30.782+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:41:30.785+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.784+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:41:30.866+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.866+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:30.872+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:41:31.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.131+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.140+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:41:31.158+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.382 seconds
[2025-07-17T22:42:01.842+0000] {processor.py:186} INFO - Started process (PID=397) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:01.843+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:42:01.846+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.846+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:01.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.917+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:01.926+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:02.198+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.198+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:02.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.209+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:42:02.225+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.390 seconds
[2025-07-17T22:42:32.949+0000] {processor.py:186} INFO - Started process (PID=530) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:32.950+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:42:32.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.952+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:33.185+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.185+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:33.192+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:42:33.288+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.288+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:33.298+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.297+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:42:33.315+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.372 seconds
[2025-07-17T22:43:03.497+0000] {processor.py:186} INFO - Started process (PID=661) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:43:03.498+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:43:03.499+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.499+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:43:03.567+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.567+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:03.575+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:43:03.667+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.666+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:03.677+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.677+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:43:03.696+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.205 seconds
[2025-07-17T22:43:34.696+0000] {processor.py:186} INFO - Started process (PID=792) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:43:34.697+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:43:34.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:34.698+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:43:34.789+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:34.789+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:34.797+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:43:34.899+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:34.899+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:34.911+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:34.911+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:43:34.932+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.243 seconds
[2025-07-17T22:44:05.745+0000] {processor.py:186} INFO - Started process (PID=923) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:44:05.746+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:44:05.748+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:05.748+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:44:05.824+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:05.824+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:05.833+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:44:05.940+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:05.939+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:05.954+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:05.953+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:44:05.975+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.235 seconds
[2025-07-17T22:44:36.733+0000] {processor.py:186} INFO - Started process (PID=1054) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:44:36.735+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:44:36.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:36.735+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:44:36.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:36.805+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:36.815+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:44:36.917+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:36.917+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:36.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:36.930+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:44:36.950+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.223 seconds
[2025-07-17T22:45:07.129+0000] {processor.py:186} INFO - Started process (PID=1185) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:45:07.130+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:45:07.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.131+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:45:07.219+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.219+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:07.229+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:45:07.359+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.358+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:07.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.374+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:45:07.399+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.277 seconds
[2025-07-17T22:45:37.719+0000] {processor.py:186} INFO - Started process (PID=1316) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:45:37.720+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:45:37.721+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:37.721+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:45:37.797+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:37.797+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:37.807+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:45:37.913+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:37.913+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:37.924+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:37.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:45:37.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.232 seconds
[2025-07-17T22:46:08.528+0000] {processor.py:186} INFO - Started process (PID=1447) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:46:08.530+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:46:08.531+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:08.531+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:46:08.622+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:08.622+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:08.632+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:46:08.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:08.735+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:08.747+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:08.747+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:46:08.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.245 seconds
[2025-07-17T22:46:38.825+0000] {processor.py:186} INFO - Started process (PID=1578) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:46:38.826+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T22:46:38.827+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:38.827+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:46:38.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:38.900+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:38.907+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T22:46:38.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:38.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:39.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:39.005+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T22:46:39.029+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.210 seconds
