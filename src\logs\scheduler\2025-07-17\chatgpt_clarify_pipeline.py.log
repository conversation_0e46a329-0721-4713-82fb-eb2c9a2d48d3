[2025-07-17T21:34:31.905+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:34:31.906+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:34:31.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:34:31.992+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.992+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.001+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:34:32.282+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.282+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.292+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.291+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:34:32.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.414 seconds
[2025-07-17T21:35:03.484+0000] {processor.py:186} INFO - Started process (PID=419) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:03.485+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:35:03.487+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.487+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:03.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.717+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:03.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:03.831+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.831+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.841+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.841+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:35:03.862+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.383 seconds
[2025-07-17T21:35:34.002+0000] {processor.py:186} INFO - Started process (PID=555) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:34.004+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:35:34.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:34.087+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.087+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:34.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:34.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.194+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:34.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.206+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:35:34.224+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.227 seconds
[2025-07-17T21:36:04.299+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:04.300+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:36:04.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.302+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:04.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.382+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:04.391+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:04.489+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.489+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:04.500+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:36:04.522+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.229 seconds
[2025-07-17T21:36:34.904+0000] {processor.py:186} INFO - Started process (PID=825) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:34.905+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:36:34.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:34.983+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.983+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.990+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:35.099+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:35.099+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:35.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:35.113+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:36:35.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.234 seconds
