[2025-07-17T21:34:31.905+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:34:31.906+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:34:31.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:34:31.992+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.992+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.001+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:34:32.282+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.282+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.292+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.291+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:34:32.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.414 seconds
[2025-07-17T21:35:03.484+0000] {processor.py:186} INFO - Started process (PID=419) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:03.485+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:35:03.487+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.487+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:03.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.717+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:03.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:03.831+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.831+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.841+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.841+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:35:03.862+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.383 seconds
[2025-07-17T21:35:34.002+0000] {processor.py:186} INFO - Started process (PID=555) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:34.004+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:35:34.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:34.087+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.087+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:34.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:35:34.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.194+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:34.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.206+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:35:34.224+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.227 seconds
[2025-07-17T21:36:04.299+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:04.300+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:36:04.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.302+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:04.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.382+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:04.391+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:04.489+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.489+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:04.500+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:36:04.522+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.229 seconds
[2025-07-17T21:36:34.904+0000] {processor.py:186} INFO - Started process (PID=825) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:34.905+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:36:34.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:34.983+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.983+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.990+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:36:35.099+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:35.099+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:35.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:35.113+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:36:35.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.234 seconds
[2025-07-17T21:37:05.466+0000] {processor.py:186} INFO - Started process (PID=961) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:05.467+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:37:05.470+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.469+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:05.547+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.546+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:05.555+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:05.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.656+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:05.667+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.667+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:37:05.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.227 seconds
[2025-07-17T21:37:35.841+0000] {processor.py:186} INFO - Started process (PID=1097) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:35.842+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:37:35.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.845+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:35.935+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.935+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.947+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:37:36.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:36.043+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:36.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:36.057+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:37:36.076+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.241 seconds
[2025-07-17T21:38:06.479+0000] {processor.py:186} INFO - Started process (PID=1233) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:06.480+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:38:06.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:06.575+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.575+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:06.582+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:06.685+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.684+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.697+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.696+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:38:06.717+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.245 seconds
[2025-07-17T21:38:37.518+0000] {processor.py:186} INFO - Started process (PID=1371) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:37.520+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:38:37.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.522+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:37.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.603+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:37.612+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:38:37.716+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.716+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:37.726+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.726+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:38:37.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.235 seconds
[2025-07-17T21:39:07.969+0000] {processor.py:186} INFO - Started process (PID=1507) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:07.970+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:39:07.973+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.972+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:08.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:08.045+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:08.052+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:08.153+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:08.153+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:08.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:08.168+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:39:08.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.237 seconds
[2025-07-17T21:39:38.416+0000] {processor.py:186} INFO - Started process (PID=1641) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:38.417+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:39:38.419+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.419+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:38.515+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.514+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:38.523+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:39:38.647+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.647+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:38.663+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:39:38.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.276 seconds
[2025-07-17T21:40:09.315+0000] {processor.py:186} INFO - Started process (PID=1777) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:40:09.317+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-17T21:40:09.320+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.319+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:40:09.405+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.405+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:09.413+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-17T21:40:09.528+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.528+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:09.552+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.552+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-17T21:40:09.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.268 seconds
