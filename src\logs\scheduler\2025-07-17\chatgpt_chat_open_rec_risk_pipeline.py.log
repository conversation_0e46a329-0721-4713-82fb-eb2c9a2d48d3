[2025-07-17T21:34:32.206+0000] {processor.py:186} INFO - Started process (PID=286) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:34:32.207+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:34:32.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.209+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:34:32.292+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.292+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.301+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:34:32.529+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.529+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.540+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:34:32.564+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.365 seconds
[2025-07-17T21:35:03.900+0000] {processor.py:186} INFO - Started process (PID=424) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:03.901+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:35:03.903+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.903+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:04.098+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.098+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:04.104+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:04.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:04.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.203+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:35:04.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.327 seconds
[2025-07-17T21:35:34.283+0000] {processor.py:186} INFO - Started process (PID=560) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:34.284+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:35:34.286+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.286+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:34.367+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.367+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:34.374+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:34.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.484+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:34.500+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.499+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:35:34.521+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.244 seconds
[2025-07-17T21:36:05.337+0000] {processor.py:186} INFO - Started process (PID=696) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:05.338+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:36:05.340+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.340+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:05.419+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.419+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:05.434+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:05.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.540+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:05.556+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.556+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:36:05.576+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.245 seconds
[2025-07-17T21:36:35.991+0000] {processor.py:186} INFO - Started process (PID=832) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:35.992+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:36:35.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:35.994+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:36.068+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.068+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.077+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:36.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.174+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:36:36.208+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.223 seconds
[2025-07-17T21:37:06.584+0000] {processor.py:186} INFO - Started process (PID=968) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:06.585+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:37:06.589+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.588+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:06.695+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.694+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:06.704+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:06.811+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.811+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:06.822+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.822+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:37:06.842+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.265 seconds
[2025-07-17T21:37:37.116+0000] {processor.py:186} INFO - Started process (PID=1104) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:37.117+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:37:37.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:37.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.198+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:37.207+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:37.315+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.315+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:37.329+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:37:37.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.242 seconds
[2025-07-17T21:38:07.729+0000] {processor.py:186} INFO - Started process (PID=1240) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:07.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:38:07.733+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.732+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:07.809+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.809+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:07.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:07.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.915+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:07.926+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.926+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:38:07.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.222 seconds
[2025-07-17T21:38:38.545+0000] {processor.py:186} INFO - Started process (PID=1376) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:38.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:38:38.549+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.549+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:38.622+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.622+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:38.632+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:38.735+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.735+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:38.746+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.745+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:38:38.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.230 seconds
[2025-07-17T21:39:09.001+0000] {processor.py:186} INFO - Started process (PID=1512) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:09.002+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:39:09.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.003+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:09.077+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.077+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:09.085+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:09.182+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.182+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:09.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.193+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:39:09.214+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.219 seconds
[2025-07-17T21:39:39.543+0000] {processor.py:186} INFO - Started process (PID=1648) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:39.544+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:39:39.547+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.546+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:39.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.634+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:39.643+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:39.765+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.765+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:39.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.778+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:39:39.803+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.267 seconds
[2025-07-17T21:40:10.460+0000] {processor.py:186} INFO - Started process (PID=1784) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:10.461+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:40:10.466+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.465+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:10.561+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.561+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:10.570+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:10.706+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.705+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:10.719+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.719+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:40:10.744+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.291 seconds
[2025-07-17T21:40:41.542+0000] {processor.py:186} INFO - Started process (PID=1920) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:41.544+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:40:41.548+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.548+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:41.648+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.648+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:41.656+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:41.772+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.772+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:41.786+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.786+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:40:41.808+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.274 seconds
[2025-07-17T21:42:59.057+0000] {processor.py:186} INFO - Started process (PID=292) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:42:59.058+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:42:59.061+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:42:59.148+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.147+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:59.154+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:42:59.424+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.424+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:59.434+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.434+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:42:59.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.407 seconds
[2025-07-17T21:43:29.958+0000] {processor.py:186} INFO - Started process (PID=428) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:43:29.959+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:43:29.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:43:30.210+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.210+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:30.217+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:43:30.319+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:30.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.330+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:43:30.350+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.399 seconds
[2025-07-17T21:44:00.647+0000] {processor.py:186} INFO - Started process (PID=566) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:00.648+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:44:00.651+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:00.744+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.743+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:00.753+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:00.869+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.868+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:00.885+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:44:00.909+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.270 seconds
[2025-07-17T21:44:31.155+0000] {processor.py:186} INFO - Started process (PID=700) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:31.156+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:44:31.159+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.159+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:31.242+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.242+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:31.250+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:31.354+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.354+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:31.365+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.365+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:44:31.384+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.235 seconds
[2025-07-17T21:45:02.300+0000] {processor.py:186} INFO - Started process (PID=836) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:45:02.301+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:45:02.304+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.304+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:45:02.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.386+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:02.395+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:45:02.510+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.510+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:02.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.524+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:45:02.546+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.251 seconds
[2025-07-17T21:55:26.786+0000] {processor.py:186} INFO - Started process (PID=293) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:26.787+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:55:26.789+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.789+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:26.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.865+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:26.874+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:27.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.122+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:27.137+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.136+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:55:27.162+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.383 seconds
[2025-07-17T21:55:57.489+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:57.490+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:55:57.493+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.492+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:57.683+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.683+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:57.689+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:57.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.770+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:57.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.780+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:55:57.795+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.312 seconds
[2025-07-17T21:56:28.100+0000] {processor.py:186} INFO - Started process (PID=566) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:28.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:56:28.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:28.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.179+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:28.187+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:28.319+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:28.334+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:56:28.356+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.262 seconds
[2025-07-17T21:56:58.459+0000] {processor.py:186} INFO - Started process (PID=702) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:58.460+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:56:58.462+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.462+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:58.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.545+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:58.554+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:58.652+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.652+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:58.664+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.664+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:56:58.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.233 seconds
[2025-07-17T21:57:29.019+0000] {processor.py:186} INFO - Started process (PID=838) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:57:29.020+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:57:29.022+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:57:29.090+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.090+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:29.098+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:57:29.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:29.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.202+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:57:29.217+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.204 seconds
[2025-07-17T21:57:59.677+0000] {processor.py:186} INFO - Started process (PID=974) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:57:59.679+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:57:59.682+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:57:59.796+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.795+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:59.806+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:57:59.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.908+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:59.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.918+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:57:59.939+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.270 seconds
[2025-07-17T21:58:30.066+0000] {processor.py:186} INFO - Started process (PID=1110) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:58:30.067+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:58:30.069+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.069+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:58:30.138+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.137+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:30.144+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:58:30.232+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.232+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:30.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.242+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:58:30.260+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.200 seconds
[2025-07-17T22:00:38.318+0000] {processor.py:186} INFO - Started process (PID=286) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:00:38.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:00:38.322+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:00:38.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.389+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:38.396+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:00:38.615+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.615+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:38.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.624+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:00:38.643+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.331 seconds
[2025-07-17T22:01:09.275+0000] {processor.py:186} INFO - Started process (PID=422) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:01:09.276+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:01:09.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:01:09.507+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.507+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:09.513+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:01:09.622+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.622+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:09.634+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.633+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:01:09.653+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.385 seconds
[2025-07-17T22:01:39.773+0000] {processor.py:186} INFO - Started process (PID=558) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:01:39.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:01:39.776+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.776+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:01:39.846+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.846+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:39.855+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:01:39.958+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.958+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:39.968+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.968+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:01:39.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.219 seconds
[2025-07-17T22:02:10.229+0000] {processor.py:186} INFO - Started process (PID=694) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:02:10.230+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:02:10.232+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.232+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:02:10.301+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.301+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:10.310+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:02:10.411+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.411+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:10.420+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.420+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:02:10.434+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.211 seconds
[2025-07-17T22:02:40.604+0000] {processor.py:186} INFO - Started process (PID=830) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:02:40.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:02:40.607+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.607+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:02:40.674+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.674+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:40.682+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:02:40.777+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.777+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:40.789+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.789+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:02:40.806+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.208 seconds
[2025-07-17T22:03:11.154+0000] {processor.py:186} INFO - Started process (PID=966) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:03:11.155+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:03:11.158+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:11.157+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:03:11.230+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:11.230+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:11.237+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:03:11.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:11.326+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:11.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:11.335+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:03:11.353+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.205 seconds
[2025-07-17T22:03:42.552+0000] {processor.py:186} INFO - Started process (PID=1102) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:03:42.557+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:03:42.568+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:42.568+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:03:43.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.015+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:43.038+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:03:43.256+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.256+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:43.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.277+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:03:43.311+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.782 seconds
[2025-07-17T22:04:13.506+0000] {processor.py:186} INFO - Started process (PID=1240) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:04:13.507+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:04:13.509+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.509+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:04:13.579+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.579+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:13.587+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:04:13.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.680+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:13.690+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.690+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:04:13.708+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.208 seconds
[2025-07-17T22:04:43.923+0000] {processor.py:186} INFO - Started process (PID=1376) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:04:43.924+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:04:43.926+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:43.926+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:04:43.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:43.995+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:44.003+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:04:44.090+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.090+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:44.099+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.098+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:04:44.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.198 seconds
[2025-07-17T22:05:14.438+0000] {processor.py:186} INFO - Started process (PID=1512) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:05:14.438+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:05:14.441+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.440+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:05:14.509+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.509+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:14.516+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:05:14.617+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.617+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:14.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.626+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:05:14.646+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.214 seconds
[2025-07-17T22:05:44.936+0000] {processor.py:186} INFO - Started process (PID=1648) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:05:44.937+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:05:44.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:44.939+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:05:45.013+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.012+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:45.021+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:05:45.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.127+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:45.137+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.137+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:05:45.153+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.224 seconds
[2025-07-17T22:06:15.356+0000] {processor.py:186} INFO - Started process (PID=1784) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:06:15.357+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:06:15.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.359+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:06:15.437+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.436+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:15.445+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:06:15.543+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.543+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:15.553+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.553+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:06:15.571+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.221 seconds
[2025-07-17T22:07:29.787+0000] {processor.py:186} INFO - Started process (PID=286) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:07:29.788+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:07:29.790+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.790+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:07:29.863+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.863+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:29.871+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:07:30.129+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.128+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:30.137+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.137+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:07:30.155+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.374 seconds
[2025-07-17T22:08:01.622+0000] {processor.py:186} INFO - Started process (PID=424) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:08:01.623+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:08:01.625+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.625+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:08:01.810+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.810+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:01.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:08:01.903+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.902+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:01.912+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.912+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:08:01.929+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.312 seconds
[2025-07-17T22:08:32.707+0000] {processor.py:186} INFO - Started process (PID=560) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:08:32.708+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:08:32.712+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:32.711+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:08:32.794+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:32.794+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:32.801+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:08:32.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:32.907+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:32.922+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:32.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:08:32.942+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.242 seconds
[2025-07-17T22:09:03.380+0000] {processor.py:186} INFO - Started process (PID=696) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:09:03.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:09:03.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:03.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:09:03.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:03.471+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:03.478+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:09:03.578+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:03.578+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:03.589+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:03.589+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:09:03.608+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.233 seconds
