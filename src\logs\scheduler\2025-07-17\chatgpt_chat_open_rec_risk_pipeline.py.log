[2025-07-17T22:41:30.776+0000] {processor.py:186} INFO - Started process (PID=263) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:41:30.777+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:41:30.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:41:30.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.865+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:30.872+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:41:31.133+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.133+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.143+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.142+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:41:31.159+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.389 seconds
[2025-07-17T22:42:01.477+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:42:01.478+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:42:01.480+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.480+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:42:01.566+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.565+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:01.574+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:42:01.806+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.805+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:01.815+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.815+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:42:01.834+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.363 seconds
[2025-07-17T22:42:32.557+0000] {processor.py:186} INFO - Started process (PID=525) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:42:32.558+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T22:42:32.559+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.559+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:42:32.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.756+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:32.763+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T22:42:32.866+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.866+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:32.876+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.876+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T22:42:32.895+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.344 seconds
