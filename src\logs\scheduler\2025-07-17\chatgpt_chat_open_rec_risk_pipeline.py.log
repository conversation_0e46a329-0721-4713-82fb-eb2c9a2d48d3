[2025-07-17T21:34:32.206+0000] {processor.py:186} INFO - Started process (PID=286) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:34:32.207+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:34:32.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.209+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:34:32.292+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.292+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.301+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:34:32.529+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.529+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.540+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:34:32.564+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.365 seconds
[2025-07-17T21:35:03.900+0000] {processor.py:186} INFO - Started process (PID=424) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:03.901+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:35:03.903+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.903+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:04.098+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.098+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:04.104+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:04.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:04.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.203+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:35:04.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.327 seconds
[2025-07-17T21:35:34.283+0000] {processor.py:186} INFO - Started process (PID=560) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:34.284+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:35:34.286+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.286+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:34.367+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.367+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:34.374+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:35:34.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.484+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:34.500+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:34.499+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:35:34.521+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.244 seconds
[2025-07-17T21:36:05.337+0000] {processor.py:186} INFO - Started process (PID=696) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:05.338+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:36:05.340+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.340+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:05.419+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.419+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:05.434+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:05.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.540+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:05.556+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.556+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:36:05.576+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.245 seconds
[2025-07-17T21:36:35.991+0000] {processor.py:186} INFO - Started process (PID=832) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:35.992+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:36:35.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:35.994+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:36.068+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.068+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.077+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:36:36.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.174+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:36:36.208+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.223 seconds
[2025-07-17T21:37:06.584+0000] {processor.py:186} INFO - Started process (PID=968) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:06.585+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:37:06.589+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.588+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:06.695+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.694+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:06.704+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:06.811+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.811+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:06.822+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.822+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:37:06.842+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.265 seconds
[2025-07-17T21:37:37.116+0000] {processor.py:186} INFO - Started process (PID=1104) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:37.117+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:37:37.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:37.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.198+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:37.207+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:37:37.315+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.315+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:37.329+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:37:37.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.242 seconds
[2025-07-17T21:38:07.729+0000] {processor.py:186} INFO - Started process (PID=1240) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:07.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:38:07.733+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.732+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:07.809+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.809+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:07.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:07.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.915+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:07.926+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.926+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:38:07.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.222 seconds
[2025-07-17T21:38:38.545+0000] {processor.py:186} INFO - Started process (PID=1376) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:38.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:38:38.549+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.549+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:38.622+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.622+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:38.632+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:38:38.735+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.735+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:38.746+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.745+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:38:38.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.230 seconds
[2025-07-17T21:39:09.001+0000] {processor.py:186} INFO - Started process (PID=1512) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:09.002+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:39:09.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.003+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:09.077+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.077+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:09.085+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:09.182+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.182+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:09.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.193+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:39:09.214+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.219 seconds
[2025-07-17T21:39:39.543+0000] {processor.py:186} INFO - Started process (PID=1648) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:39.544+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:39:39.547+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.546+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:39.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.634+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:39.643+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:39:39.765+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.765+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:39.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.778+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:39:39.803+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.267 seconds
[2025-07-17T21:40:10.460+0000] {processor.py:186} INFO - Started process (PID=1784) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:10.461+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:40:10.466+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.465+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:10.561+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.561+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:10.570+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:10.706+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.705+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:10.719+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.719+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:40:10.744+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.291 seconds
[2025-07-17T21:40:41.542+0000] {processor.py:186} INFO - Started process (PID=1920) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:41.544+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:40:41.548+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.548+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:41.648+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.648+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:41.656+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:40:41.772+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.772+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:41.786+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.786+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:40:41.808+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.274 seconds
[2025-07-17T21:42:59.057+0000] {processor.py:186} INFO - Started process (PID=292) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:42:59.058+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:42:59.061+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:42:59.148+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.147+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:59.154+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:42:59.424+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.424+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:59.434+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.434+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:42:59.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.407 seconds
[2025-07-17T21:43:29.958+0000] {processor.py:186} INFO - Started process (PID=428) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:43:29.959+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:43:29.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:43:30.210+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.210+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:30.217+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:43:30.319+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:30.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.330+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:43:30.350+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.399 seconds
[2025-07-17T21:44:00.647+0000] {processor.py:186} INFO - Started process (PID=566) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:00.648+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:44:00.651+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:00.744+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.743+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:00.753+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:00.869+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.868+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:00.885+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:44:00.909+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.270 seconds
[2025-07-17T21:44:31.155+0000] {processor.py:186} INFO - Started process (PID=700) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:31.156+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:44:31.159+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.159+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:31.242+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.242+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:31.250+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:44:31.354+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.354+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:31.365+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.365+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:44:31.384+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.235 seconds
[2025-07-17T21:45:02.300+0000] {processor.py:186} INFO - Started process (PID=836) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:45:02.301+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:45:02.304+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.304+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:45:02.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.386+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:02.395+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:45:02.510+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.510+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:02.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.524+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:45:02.546+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.251 seconds
[2025-07-17T21:55:26.786+0000] {processor.py:186} INFO - Started process (PID=293) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:26.787+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:55:26.789+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.789+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:26.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.865+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:26.874+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:27.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.122+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:27.137+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.136+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:55:27.162+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.383 seconds
[2025-07-17T21:55:57.489+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:57.490+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:55:57.493+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.492+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:57.683+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.683+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:57.689+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:55:57.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.770+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:57.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.780+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:55:57.795+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.312 seconds
[2025-07-17T21:56:28.100+0000] {processor.py:186} INFO - Started process (PID=566) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:28.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:56:28.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:28.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.179+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:28.187+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:28.319+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:28.334+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:56:28.356+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.262 seconds
[2025-07-17T21:56:58.459+0000] {processor.py:186} INFO - Started process (PID=702) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:58.460+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-17T21:56:58.462+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.462+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:58.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.545+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:58.554+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-17T21:56:58.652+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.652+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:58.664+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.664+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-17T21:56:58.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.233 seconds
