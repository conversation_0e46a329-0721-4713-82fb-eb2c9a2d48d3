#!/usr/bin/env python3
"""
Скрипт для проверки наличия мониторинга затрат во всех DAG файлах
"""

import os
import re
from pathlib import Path

def check_cost_tracking_in_file(file_path):
    """Проверяет наличие мониторинга затрат в файле"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Проверяем наличие ключевых элементов мониторинга
        has_import = 'from cost_tracking import track_openai_cost' in content
        has_tracking_call = 'track_openai_cost(' in content
        has_cost_display = '💰' in content or 'Total cost' in content or 'Стоимость операции' in content
        
        return {
            'has_import': has_import,
            'has_tracking_call': has_tracking_call,
            'has_cost_display': has_cost_display,
            'fully_integrated': has_import and has_tracking_call and has_cost_display
        }
    except Exception as e:
        return {
            'error': str(e),
            'has_import': False,
            'has_tracking_call': False,
            'has_cost_display': False,
            'fully_integrated': False
        }

def main():
    """Основная функция"""
    print("🔍 ПРОВЕРКА МОНИТОРИНГА ЗАТРАТ ВО ВСЕХ DAG ФАЙЛАХ")
    print("=" * 60)
    
    dags_dir = Path("dags")
    if not dags_dir.exists():
        print("❌ Папка dags не найдена")
        return
    
    # Находим все Python файлы с chatgpt в названии
    dag_files = []
    for file_path in dags_dir.glob("chatgpt_*.py"):
        dag_files.append(file_path)
    
    # Добавляем perplexity_pipeline.py если есть
    perplexity_file = dags_dir / "perplexity_pipeline.py"
    if perplexity_file.exists():
        dag_files.append(perplexity_file)
    
    if not dag_files:
        print("❌ DAG файлы не найдены")
        return
    
    print(f"📁 Найдено {len(dag_files)} DAG файлов для проверки\n")
    
    # Статистика
    fully_integrated = []
    partially_integrated = []
    not_integrated = []
    
    # Проверяем каждый файл
    for file_path in sorted(dag_files):
        result = check_cost_tracking_in_file(file_path)
        
        if 'error' in result:
            print(f"❌ {file_path.name}: Ошибка - {result['error']}")
            not_integrated.append(file_path.name)
            continue
        
        status_icon = "✅" if result['fully_integrated'] else "⚠️" if any([result['has_import'], result['has_tracking_call'], result['has_cost_display']]) else "❌"
        
        print(f"{status_icon} {file_path.name}")
        
        if not result['fully_integrated']:
            details = []
            if not result['has_import']:
                details.append("нет импорта")
            if not result['has_tracking_call']:
                details.append("нет вызова track_openai_cost")
            if not result['has_cost_display']:
                details.append("нет отображения стоимости")
            
            if details:
                print(f"   └─ Отсутствует: {', '.join(details)}")
        
        # Категоризируем
        if result['fully_integrated']:
            fully_integrated.append(file_path.name)
        elif any([result['has_import'], result['has_tracking_call'], result['has_cost_display']]):
            partially_integrated.append(file_path.name)
        else:
            not_integrated.append(file_path.name)
    
    # Итоговая статистика
    print("\n" + "=" * 60)
    print("📊 ИТОГОВАЯ СТАТИСТИКА:")
    print(f"✅ Полностью интегрировано: {len(fully_integrated)}")
    print(f"⚠️ Частично интегрировано: {len(partially_integrated)}")
    print(f"❌ Не интегрировано: {len(not_integrated)}")
    print(f"📈 Общий процент покрытия: {len(fully_integrated) / len(dag_files) * 100:.1f}%")
    
    if not_integrated:
        print(f"\n❌ Файлы БЕЗ мониторинга:")
        for file_name in not_integrated:
            print(f"   - {file_name}")
    
    if partially_integrated:
        print(f"\n⚠️ Файлы с ЧАСТИЧНЫМ мониторингом:")
        for file_name in partially_integrated:
            print(f"   - {file_name}")
    
    if len(fully_integrated) == len(dag_files):
        print("\n🎉 ВСЕ DAG ФАЙЛЫ ИМЕЮТ ПОЛНЫЙ МОНИТОРИНГ ЗАТРАТ!")
    else:
        print(f"\n🔧 Необходимо доработать {len(dag_files) - len(fully_integrated)} файлов")

if __name__ == "__main__":
    main()
