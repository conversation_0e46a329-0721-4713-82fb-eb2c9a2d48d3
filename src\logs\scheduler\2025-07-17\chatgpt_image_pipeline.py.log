[2025-07-17T21:34:28.012+0000] {processor.py:186} INFO - Started process (PID=191) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:34:28.013+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:34:28.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:34:28.118+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.118+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:28.126+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:34:28.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:28.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.375+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:34:28.561+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.554 seconds
[2025-07-17T21:34:58.897+0000] {processor.py:186} INFO - Started process (PID=327) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:34:58.898+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:34:58.901+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:58.900+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:34:58.985+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:58.984+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:58.996+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:34:59.285+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.285+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:59.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.295+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:34:59.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.422 seconds
[2025-07-17T21:35:29.850+0000] {processor.py:186} INFO - Started process (PID=463) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:35:29.851+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:35:29.854+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:29.854+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:35:30.112+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.112+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:30.119+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:35:30.256+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.256+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:30.269+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.269+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:35:30.291+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.450 seconds
[2025-07-17T21:36:00.627+0000] {processor.py:186} INFO - Started process (PID=599) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:36:00.629+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:36:00.633+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.633+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:36:00.744+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.744+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:00.753+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:36:00.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.845+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:00.855+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.855+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:36:00.874+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.257 seconds
[2025-07-17T21:36:30.979+0000] {processor.py:186} INFO - Started process (PID=735) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:36:30.980+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:36:30.983+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:30.982+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:36:31.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.056+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:31.066+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:36:31.167+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.167+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.176+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:36:31.195+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.221 seconds
[2025-07-17T21:37:01.313+0000] {processor.py:186} INFO - Started process (PID=871) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:37:01.314+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:37:01.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.317+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:37:01.403+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.403+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:01.411+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:37:01.515+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.515+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:01.527+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.527+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:37:01.545+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.239 seconds
[2025-07-17T21:37:32.004+0000] {processor.py:186} INFO - Started process (PID=1007) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:37:32.005+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:37:32.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:37:32.081+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.081+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:32.091+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:37:32.201+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.201+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:32.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:37:32.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.235 seconds
[2025-07-17T21:38:02.482+0000] {processor.py:186} INFO - Started process (PID=1143) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:38:02.483+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:38:02.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.485+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:38:02.566+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.566+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:02.573+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:38:02.675+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.675+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:02.688+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.688+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:38:02.707+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.232 seconds
[2025-07-17T21:38:33.018+0000] {processor.py:186} INFO - Started process (PID=1279) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:38:33.019+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:38:33.021+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:38:33.100+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.100+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:33.109+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:38:33.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.209+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:33.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.220+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:38:33.241+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.229 seconds
[2025-07-17T21:39:03.905+0000] {processor.py:186} INFO - Started process (PID=1415) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:39:03.905+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:39:03.908+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:03.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:39:03.989+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:03.989+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:03.998+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:39:04.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.100+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:04.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.113+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:39:04.136+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.237 seconds
[2025-07-17T21:39:34.525+0000] {processor.py:186} INFO - Started process (PID=1551) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:39:34.526+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:39:34.528+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.528+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:39:34.595+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.595+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:34.603+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:39:34.709+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.709+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:34.721+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.721+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:39:34.742+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.222 seconds
[2025-07-17T21:40:05.101+0000] {processor.py:186} INFO - Started process (PID=1687) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:40:05.103+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:40:05.105+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.105+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:40:05.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.193+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:05.206+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:40:05.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.336+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:05.353+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.353+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:40:05.377+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.282 seconds
[2025-07-17T21:40:35.960+0000] {processor.py:186} INFO - Started process (PID=1823) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:40:35.960+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:40:35.963+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:35.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:40:36.045+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.044+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:36.055+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:40:36.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.164+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:36.177+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.176+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:40:36.202+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.250 seconds
[2025-07-17T21:41:07.133+0000] {processor.py:186} INFO - Started process (PID=1959) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:41:07.134+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:41:07.138+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.137+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:41:07.228+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.228+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:41:07.239+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:41:07.372+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.372+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:41:07.388+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.388+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:41:07.423+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.297 seconds
[2025-07-17T21:42:54.889+0000] {processor.py:186} INFO - Started process (PID=191) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:42:54.889+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:42:54.891+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:54.891+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:42:54.972+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:54.972+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:54.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:42:55.194+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.194+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:55.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.204+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:42:55.383+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.500 seconds
[2025-07-17T21:43:25.781+0000] {processor.py:186} INFO - Started process (PID=327) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:43:25.782+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:43:25.785+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:25.784+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:43:25.868+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:25.868+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:25.877+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:43:26.166+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.165+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:26.177+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.177+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:43:26.197+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.425 seconds
[2025-07-17T21:43:56.777+0000] {processor.py:186} INFO - Started process (PID=463) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:43:56.778+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:43:56.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:56.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:43:57.011+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.011+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:57.026+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:43:57.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.141+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:57.153+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.153+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:43:57.173+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.402 seconds
[2025-07-17T21:44:27.594+0000] {processor.py:186} INFO - Started process (PID=601) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:44:27.595+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:44:27.597+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.597+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:44:27.671+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.670+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:27.680+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:44:27.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.780+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:27.791+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.791+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:44:27.811+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.223 seconds
[2025-07-17T21:44:58.131+0000] {processor.py:186} INFO - Started process (PID=737) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:44:58.132+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:44:58.134+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.134+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:44:58.225+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.225+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:58.235+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:44:58.343+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.342+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:58.355+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.355+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:44:58.378+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.254 seconds
[2025-07-17T21:55:22.754+0000] {processor.py:186} INFO - Started process (PID=192) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:55:22.755+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:55:22.758+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:22.757+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:55:22.866+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:22.866+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:22.876+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:55:23.120+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.120+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:23.133+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.133+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:55:23.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.566 seconds
[2025-07-17T21:55:53.380+0000] {processor.py:186} INFO - Started process (PID=328) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:55:53.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:55:53.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.383+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:55:53.453+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.453+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:53.462+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:55:53.718+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.718+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:53.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.728+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:55:53.745+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.371 seconds
[2025-07-17T21:56:24.130+0000] {processor.py:186} INFO - Started process (PID=464) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:56:24.131+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:56:24.134+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:56:24.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.368+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:24.374+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:56:24.464+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.464+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:24.473+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.473+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:56:24.494+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.370 seconds
[2025-07-17T21:56:54.715+0000] {processor.py:186} INFO - Started process (PID=601) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:56:54.716+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:56:54.719+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.719+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:56:54.794+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.794+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:54.803+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:56:54.916+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.916+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:54.927+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.927+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:56:54.946+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.238 seconds
[2025-07-17T21:57:25.128+0000] {processor.py:186} INFO - Started process (PID=737) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:57:25.129+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:57:25.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.131+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:57:25.208+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.208+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:25.220+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:57:25.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.330+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:25.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.341+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:57:25.362+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.241 seconds
[2025-07-17T21:57:55.553+0000] {processor.py:186} INFO - Started process (PID=873) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:57:55.554+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:57:55.557+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.556+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:57:55.629+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.629+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:55.637+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:57:55.737+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.736+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:55.748+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.748+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:57:55.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.222 seconds
[2025-07-17T21:58:26.043+0000] {processor.py:186} INFO - Started process (PID=1009) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:58:26.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T21:58:26.047+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.047+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:58:26.118+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.118+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:26.127+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T21:58:26.222+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.222+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:26.233+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T21:58:26.252+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.217 seconds
