[2025-07-17T22:41:28.231+0000] {processor.py:186} INFO - Started process (PID=213) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:28.232+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:41:28.234+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.234+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:28.329+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.328+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:28.338+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:28.449+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.449+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:28.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.601+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:41:28.627+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.402 seconds
[2025-07-17T22:41:59.051+0000] {processor.py:186} INFO - Started process (PID=342) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:59.052+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:41:59.054+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.054+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:59.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.126+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.135+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:59.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.394+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:59.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.408+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:41:59.428+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.384 seconds
[2025-07-17T22:42:29.878+0000] {processor.py:186} INFO - Started process (PID=473) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:42:29.879+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:42:29.880+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.880+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:42:30.089+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.089+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.096+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:42:30.191+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.190+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.200+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.199+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:42:30.217+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.345 seconds
