[2025-07-17T22:41:28.231+0000] {processor.py:186} INFO - Started process (PID=213) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:28.232+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:41:28.234+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.234+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:28.329+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.328+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:28.338+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:28.449+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.449+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:28.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.601+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:41:28.627+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.402 seconds
[2025-07-17T22:41:59.051+0000] {processor.py:186} INFO - Started process (PID=342) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:59.052+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:41:59.054+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.054+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:59.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.126+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.135+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:41:59.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.394+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:59.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.408+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:41:59.428+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.384 seconds
[2025-07-17T22:42:29.878+0000] {processor.py:186} INFO - Started process (PID=473) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:42:29.879+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:42:29.880+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.880+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:42:30.089+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.089+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.096+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:42:30.191+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.190+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.200+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.199+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:42:30.217+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.345 seconds
[2025-07-17T22:43:00.611+0000] {processor.py:186} INFO - Started process (PID=606) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:43:00.612+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:43:00.614+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.614+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:43:00.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.684+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.693+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:43:00.794+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.793+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:00.804+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.804+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:43:00.823+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.218 seconds
[2025-07-17T22:43:31.018+0000] {processor.py:186} INFO - Started process (PID=737) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:43:31.019+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:43:31.021+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:43:31.100+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.100+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:31.108+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:43:31.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.206+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:31.217+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.217+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:43:31.235+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.223 seconds
[2025-07-17T22:44:02.132+0000] {processor.py:186} INFO - Started process (PID=868) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:44:02.133+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:44:02.134+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.134+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:44:02.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.215+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:02.226+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:44:02.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.348+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.348+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:44:02.364+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.238 seconds
[2025-07-17T22:44:33.114+0000] {processor.py:186} INFO - Started process (PID=999) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:44:33.115+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:44:33.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.116+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:44:33.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.194+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:33.203+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:44:33.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.303+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.314+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.313+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:44:33.332+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.225 seconds
[2025-07-17T22:45:04.035+0000] {processor.py:186} INFO - Started process (PID=1130) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:45:04.036+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:45:04.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.037+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:45:04.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.112+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:04.121+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:45:04.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.220+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:04.231+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.231+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:45:04.251+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.225 seconds
[2025-07-17T22:45:34.734+0000] {processor.py:186} INFO - Started process (PID=1261) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:45:34.735+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:45:34.737+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.737+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:45:34.822+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.821+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:34.834+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:45:34.955+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.955+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:34.971+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:45:34.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.268 seconds
[2025-07-17T22:46:05.634+0000] {processor.py:186} INFO - Started process (PID=1392) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:46:05.635+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:46:05.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:46:05.711+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.711+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:05.721+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:46:05.830+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.830+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:05.844+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.844+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:46:05.864+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.236 seconds
[2025-07-17T22:46:35.951+0000] {processor.py:186} INFO - Started process (PID=1523) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:46:35.952+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:46:35.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.953+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:46:36.034+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.034+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:36.042+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:46:36.140+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.140+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:36.151+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.151+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:46:36.171+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.226 seconds
[2025-07-17T22:47:07.040+0000] {processor.py:186} INFO - Started process (PID=1660) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:47:07.042+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:47:07.043+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.043+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:47:07.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.110+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:07.118+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:47:07.210+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.210+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:07.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.220+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:47:07.239+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.205 seconds
[2025-07-17T22:47:38.193+0000] {processor.py:186} INFO - Started process (PID=1797) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:47:38.194+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:47:38.196+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.195+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:47:38.276+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.276+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:38.285+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:47:38.385+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.385+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:38.397+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.396+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:47:38.416+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.229 seconds
[2025-07-17T22:48:09.039+0000] {processor.py:186} INFO - Started process (PID=1928) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:48:09.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:48:09.042+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.042+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:48:09.105+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.105+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:09.114+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:48:09.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.209+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:09.219+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.219+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:48:09.238+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.205 seconds
[2025-07-17T22:48:40.042+0000] {processor.py:186} INFO - Started process (PID=2059) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:48:40.043+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:48:40.045+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:48:40.116+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.116+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:40.125+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:48:40.223+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.222+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:40.233+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:48:40.251+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.217 seconds
[2025-07-17T22:49:10.548+0000] {processor.py:186} INFO - Started process (PID=2190) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:49:10.549+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:49:10.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.550+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:49:10.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.626+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:10.636+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:49:10.737+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.736+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:10.748+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.748+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:49:10.770+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.229 seconds
[2025-07-17T22:49:40.892+0000] {processor.py:186} INFO - Started process (PID=2321) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:49:40.893+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:49:40.894+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.894+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:49:40.963+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.962+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:40.971+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:49:41.058+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:41.058+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:41.068+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:41.068+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:49:41.083+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.197 seconds
[2025-07-17T22:50:11.291+0000] {processor.py:186} INFO - Started process (PID=2450) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:50:11.292+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:50:11.294+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:11.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:50:11.364+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:11.364+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:11.372+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:50:11.467+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:11.466+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:11.477+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:11.476+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:50:11.491+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.205 seconds
[2025-07-17T22:50:42.215+0000] {processor.py:186} INFO - Started process (PID=2581) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:50:42.216+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:50:42.217+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:42.217+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:50:42.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:42.293+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:42.302+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:50:42.401+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:42.401+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:42.412+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:42.411+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:50:42.429+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.220 seconds
[2025-07-17T22:51:12.973+0000] {processor.py:186} INFO - Started process (PID=2714) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:51:12.975+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:51:12.976+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.976+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:51:13.055+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:13.055+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:13.063+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:51:13.181+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:13.181+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:13.194+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:13.194+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:51:13.217+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.251 seconds
[2025-07-17T22:51:43.531+0000] {processor.py:186} INFO - Started process (PID=2843) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:51:43.532+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:51:43.534+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:43.533+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:51:43.605+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:43.605+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:43.615+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:51:43.721+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:43.721+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:43.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:43.736+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:51:43.757+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.231 seconds
[2025-07-17T22:52:14.219+0000] {processor.py:186} INFO - Started process (PID=2976) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:52:14.220+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:52:14.221+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:14.221+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:52:14.298+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:14.298+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:14.307+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:52:14.402+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:14.402+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:14.413+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:14.413+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:52:14.432+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.220 seconds
[2025-07-17T22:52:44.938+0000] {processor.py:186} INFO - Started process (PID=3107) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:52:44.939+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:52:44.940+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:44.940+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:52:45.008+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:45.008+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:45.019+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:52:45.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:45.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:45.125+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:45.125+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:52:45.144+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.213 seconds
[2025-07-17T22:53:15.451+0000] {processor.py:186} INFO - Started process (PID=3238) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:53:15.452+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:53:15.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:15.453+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:53:15.527+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:15.527+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:15.536+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:53:15.640+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:15.640+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:15.652+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:15.651+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:53:15.671+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.226 seconds
[2025-07-17T22:53:46.105+0000] {processor.py:186} INFO - Started process (PID=3373) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:53:46.106+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-17T22:53:46.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:46.107+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:53:46.182+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:46.182+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:46.191+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-17T22:53:46.324+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:46.323+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:46.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:46.336+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-17T22:53:46.360+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.261 seconds
