# 💰 Система мониторинга затрат Biome AI

Простая система для отслеживания затрат на OpenAI API с автоматическим сохранением в Redis.

## 🎯 Что делает система

- **Автоматически считает токены и стоимость** каждого запроса к OpenAI
- **Сохраняет данные в Redis** для дальнейшего анализа
- **Показывает статистику** через простой API эндпоинт
- **Работает с существующими DAG файлами** - просто раскомментирует уже написанный код

## � Как запустить (3 простых шага)

### 1. Включить мониторинг в DAG файлах

```bash
cd src/dags
python enable_cost_tracking.py
```

Этот скрипт просто раскомментирует уже существующие функции `analyze_token_usage` и добавит сохранение в Redis.

### 2. Перезапустить Airflow

Перезапустите Airflow, чтобы изменения вступили в силу.

### 3. Проверить статистику

Откройте в браузере или через curl:
```bash
curl http://localhost:8000/cost-stats
```

**ВСЁ!** Система работает.

## � Что вы увидите

После запуска любого DAG файла в логах появится:

```
Input tokens: 45, Output tokens: 123
Cost for input tokens: $0.0014, Cost for output tokens: $0.0074
💰 Total cost: $0.0088
```

А в API эндпоинте `/cost-stats`:

```json
{
  "total_cost": 0.156789,
  "total_operations": 23,
  "operations_by_dag": {
    "chatgpt_message_pipeline": {
      "count": 15,
      "cost": 0.089234
    },
    "chatgpt_analyze_food_pipeline": {
      "count": 8,
      "cost": 0.067555
    }
  },
  "message": "💰 Статистика затрат Biome AI"
}
```

## 💡 Что происходит под капотом

1. **В каждом DAG файле** уже есть функция `analyze_token_usage` (закомментированная)
2. **Скрипт `enable_cost_tracking.py`** просто раскомментирует эти функции
3. **Добавляется код сохранения в Redis** с TTL 30 дней
4. **API эндпоинт `/cost-stats`** читает данные из Redis и показывает статистику

## 🔧 Настройки

- **Redis**: localhost:6379, база данных 0
- **Стоимость токенов GPT-4o**: $0.03/$0.06 за 1000 входных/выходных токенов
- **TTL данных**: 30 дней

## ❓ Если что-то не работает

1. Проверьте, что Redis запущен: `redis-cli ping`
2. Посмотрите логи Airflow на ошибки
3. Убедитесь, что DAG файлы обновлены: поищите "💰 Total cost" в логах

**Готово!** Теперь у вас есть простой мониторинг затрат, который работает с существующим кодом.
