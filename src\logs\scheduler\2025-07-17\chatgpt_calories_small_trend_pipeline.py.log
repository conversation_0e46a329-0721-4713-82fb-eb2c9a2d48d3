[2025-07-17T22:41:27.682+0000] {processor.py:186} INFO - Started process (PID=197) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:27.683+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:41:27.685+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.685+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:27.771+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.771+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:27.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:27.911+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:27.927+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.927+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:41:28.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.499 seconds
[2025-07-17T22:41:58.593+0000] {processor.py:186} INFO - Started process (PID=332) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:58.594+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:41:58.597+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.597+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:58.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.684+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:58.691+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:58.964+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.964+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:58.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.975+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:41:58.994+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.409 seconds
[2025-07-17T22:42:29.499+0000] {processor.py:186} INFO - Started process (PID=465) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:42:29.500+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:42:29.501+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.501+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:42:29.694+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.694+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:29.700+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:42:29.787+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.786+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:29.799+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.799+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:42:29.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.323 seconds
[2025-07-17T22:43:00.357+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:00.358+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:43:00.359+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.358+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:00.432+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.431+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.439+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:00.540+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.539+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:00.551+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.550+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:43:00.569+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.219 seconds
[2025-07-17T22:43:30.744+0000] {processor.py:186} INFO - Started process (PID=727) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:30.745+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:43:30.746+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.746+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:30.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.818+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:30.827+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:30.929+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.929+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:30.941+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.941+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:43:30.962+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.224 seconds
[2025-07-17T22:44:01.862+0000] {processor.py:186} INFO - Started process (PID=858) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:01.863+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:44:01.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.864+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:01.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.939+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:01.947+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:02.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.056+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.070+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:44:02.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.234 seconds
[2025-07-17T22:44:32.860+0000] {processor.py:186} INFO - Started process (PID=989) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:32.861+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:44:32.863+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.862+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:32.931+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.931+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:32.939+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:33.032+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.032+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.043+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:44:33.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.209 seconds
[2025-07-17T22:45:03.761+0000] {processor.py:186} INFO - Started process (PID=1120) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:45:03.762+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:45:03.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.763+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:45:03.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.834+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:03.843+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:45:03.948+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.948+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:03.961+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:45:03.982+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.227 seconds
[2025-07-17T22:45:34.470+0000] {processor.py:186} INFO - Started process (PID=1251) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:45:34.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:45:34.473+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.472+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:45:34.548+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.548+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:34.558+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:45:34.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.656+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:34.669+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.669+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:45:34.690+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.226 seconds
[2025-07-17T22:46:05.375+0000] {processor.py:186} INFO - Started process (PID=1382) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:46:05.376+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:46:05.377+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.377+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:46:05.445+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.444+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:05.453+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:46:05.548+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.547+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:05.560+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.560+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:46:05.581+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.211 seconds
[2025-07-17T22:46:35.705+0000] {processor.py:186} INFO - Started process (PID=1513) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:46:35.706+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:46:35.707+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.707+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:46:35.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.770+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:35.779+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:46:35.871+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.871+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:35.882+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.882+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:46:35.900+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.201 seconds
[2025-07-17T22:47:06.796+0000] {processor.py:186} INFO - Started process (PID=1650) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:47:06.797+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:47:06.798+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.798+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:47:06.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.865+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:06.872+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:47:06.968+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.968+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:06.980+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.980+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:47:06.999+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.209 seconds
[2025-07-17T22:47:37.924+0000] {processor.py:186} INFO - Started process (PID=1787) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:47:37.925+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:47:37.926+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.926+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:47:37.999+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.999+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:38.008+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:47:38.112+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.112+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:38.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:47:38.147+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.230 seconds
[2025-07-17T22:48:08.793+0000] {processor.py:186} INFO - Started process (PID=1918) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:48:08.794+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:48:08.795+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.795+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:48:08.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.860+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:08.869+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:48:08.960+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.959+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:08.970+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:48:08.989+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.201 seconds
[2025-07-17T22:48:39.770+0000] {processor.py:186} INFO - Started process (PID=2049) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:48:39.772+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:48:39.773+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.773+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:48:39.848+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.848+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:39.857+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:48:39.955+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.955+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:39.966+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.966+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:48:39.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.224 seconds
[2025-07-17T22:49:10.235+0000] {processor.py:186} INFO - Started process (PID=2180) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:49:10.236+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:49:10.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.238+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:49:10.324+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.324+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:10.334+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:49:10.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.454+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:10.466+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.466+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:49:10.486+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.258 seconds
[2025-07-17T22:49:40.653+0000] {processor.py:186} INFO - Started process (PID=2311) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:49:40.654+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:49:40.655+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.655+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:49:40.721+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.721+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:40.729+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:49:40.815+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.815+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:40.825+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.825+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:49:40.844+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.197 seconds
