[2025-07-17T21:34:32.620+0000] {processor.py:186} INFO - Started process (PID=296) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:34:32.621+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:34:32.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.624+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:34:32.718+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.717+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.724+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:34:32.969+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.969+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.978+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.978+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:34:33.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.392 seconds
[2025-07-17T21:35:04.287+0000] {processor.py:186} INFO - Started process (PID=432) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:04.288+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:35:04.290+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.290+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:04.500+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.500+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:04.505+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:04.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.598+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:04.610+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.609+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:35:04.627+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.345 seconds
[2025-07-17T21:35:35.320+0000] {processor.py:186} INFO - Started process (PID=568) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:35.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:35:35.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:35.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.398+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:35.407+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:35.508+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.508+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:35.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.519+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:35:35.540+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.226 seconds
[2025-07-17T21:36:05.638+0000] {processor.py:186} INFO - Started process (PID=704) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:05.639+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:36:05.642+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.642+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:05.737+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.736+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:05.746+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:05.863+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.863+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:05.876+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.876+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:36:05.896+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.264 seconds
[2025-07-17T21:36:36.247+0000] {processor.py:186} INFO - Started process (PID=841) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:36.248+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:36:36.250+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.249+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:36.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.326+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.333+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:36.432+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.431+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.443+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:36:36.461+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.220 seconds
