[2025-07-17T21:34:32.620+0000] {processor.py:186} INFO - Started process (PID=296) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:34:32.621+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:34:32.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.624+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:34:32.718+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.717+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.724+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:34:32.969+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.969+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.978+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.978+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:34:33.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.392 seconds
[2025-07-17T21:35:04.287+0000] {processor.py:186} INFO - Started process (PID=432) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:04.288+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:35:04.290+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.290+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:04.500+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.500+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:04.505+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:04.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.598+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:04.610+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.609+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:35:04.627+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.345 seconds
[2025-07-17T21:35:35.320+0000] {processor.py:186} INFO - Started process (PID=568) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:35.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:35:35.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:35.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.398+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:35.407+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:35:35.508+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.508+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:35.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.519+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:35:35.540+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.226 seconds
[2025-07-17T21:36:05.638+0000] {processor.py:186} INFO - Started process (PID=704) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:05.639+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:36:05.642+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.642+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:05.737+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.736+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:05.746+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:05.863+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.863+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:05.876+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.876+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:36:05.896+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.264 seconds
[2025-07-17T21:36:36.247+0000] {processor.py:186} INFO - Started process (PID=841) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:36.248+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:36:36.250+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.249+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:36.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.326+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.333+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:36:36.432+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.431+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.443+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:36:36.461+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.220 seconds
[2025-07-17T21:37:06.880+0000] {processor.py:186} INFO - Started process (PID=976) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:37:06.881+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:37:06.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.883+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:37:06.957+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.956+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:06.965+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:37:07.066+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.065+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:07.076+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.075+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:37:07.095+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.220 seconds
[2025-07-17T21:37:37.391+0000] {processor.py:186} INFO - Started process (PID=1114) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:37:37.392+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:37:37.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.395+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:37:37.469+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.469+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:37.477+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:37:37.579+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.579+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:37.592+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.592+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:37:37.613+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.229 seconds
[2025-07-17T21:38:07.983+0000] {processor.py:186} INFO - Started process (PID=1250) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:38:07.984+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:38:07.986+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.985+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:38:08.054+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.054+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:08.061+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:38:08.149+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.149+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:08.158+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.158+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:38:08.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.197 seconds
[2025-07-17T21:38:38.818+0000] {processor.py:186} INFO - Started process (PID=1386) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:38:38.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:38:38.821+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.821+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:38:38.899+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.898+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:38.909+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:38:39.003+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.002+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:39.014+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.013+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:38:39.032+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.221 seconds
[2025-07-17T21:39:09.270+0000] {processor.py:186} INFO - Started process (PID=1522) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:39:09.271+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:39:09.273+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.273+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:39:09.345+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.345+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:09.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:39:09.447+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.447+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:09.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.458+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:39:09.478+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.214 seconds
[2025-07-17T21:39:39.855+0000] {processor.py:186} INFO - Started process (PID=1658) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:39:39.856+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:39:39.859+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.858+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:39:39.950+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.950+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:39.959+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:39:40.069+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.069+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:40.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.079+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:39:40.101+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.253 seconds
[2025-07-17T21:40:10.800+0000] {processor.py:186} INFO - Started process (PID=1794) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:40:10.801+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:40:10.804+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.804+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:40:10.903+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.902+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:10.912+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:40:11.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.070+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:11.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.082+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:40:11.103+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.310 seconds
[2025-07-17T21:40:41.852+0000] {processor.py:186} INFO - Started process (PID=1930) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:40:41.853+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:40:41.856+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.856+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:40:41.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.937+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:41.945+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:40:42.053+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.052+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:42.065+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.065+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:40:42.087+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.241 seconds
[2025-07-17T21:42:59.503+0000] {processor.py:186} INFO - Started process (PID=302) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:42:59.504+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:42:59.507+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.507+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:42:59.594+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.594+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:59.601+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:42:59.851+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.851+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:59.862+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.862+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:42:59.883+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.386 seconds
[2025-07-17T21:43:30.398+0000] {processor.py:186} INFO - Started process (PID=438) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:43:30.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:43:30.401+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.401+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:43:30.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.628+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:30.634+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:43:30.758+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.757+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:30.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.770+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:43:30.793+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.402 seconds
[2025-07-17T21:44:00.987+0000] {processor.py:186} INFO - Started process (PID=574) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:44:00.988+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:44:00.990+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.990+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:44:01.081+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.081+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:01.091+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:44:01.198+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.197+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:01.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.209+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:44:01.231+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.252 seconds
[2025-07-17T21:44:31.450+0000] {processor.py:186} INFO - Started process (PID=710) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:44:31.451+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:44:31.453+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.452+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:44:31.531+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.530+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:31.540+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:44:31.662+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.662+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:31.676+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.676+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:44:31.697+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.254 seconds
[2025-07-17T21:45:02.591+0000] {processor.py:186} INFO - Started process (PID=846) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:45:02.592+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T21:45:02.595+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.595+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:45:02.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.683+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:02.690+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T21:45:02.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.792+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:02.804+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.804+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T21:45:02.823+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.239 seconds
