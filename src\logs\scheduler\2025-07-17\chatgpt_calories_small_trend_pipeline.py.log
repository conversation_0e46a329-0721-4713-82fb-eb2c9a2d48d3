[2025-07-17T22:41:27.682+0000] {processor.py:186} INFO - Started process (PID=197) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:27.683+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:41:27.685+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.685+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:27.771+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.771+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:27.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:27.911+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:27.927+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.927+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:41:28.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.499 seconds
[2025-07-17T22:41:58.593+0000] {processor.py:186} INFO - Started process (PID=332) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:58.594+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:41:58.597+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.597+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:58.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.684+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:58.691+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:41:58.964+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.964+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:58.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.975+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:41:58.994+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.409 seconds
[2025-07-17T22:42:29.499+0000] {processor.py:186} INFO - Started process (PID=465) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:42:29.500+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:42:29.501+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.501+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:42:29.694+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.694+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:29.700+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:42:29.787+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.786+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:29.799+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.799+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:42:29.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.323 seconds
[2025-07-17T22:43:00.357+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:00.358+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:43:00.359+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.358+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:00.432+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.431+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.439+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:00.540+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.539+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:00.551+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.550+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:43:00.569+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.219 seconds
[2025-07-17T22:43:30.744+0000] {processor.py:186} INFO - Started process (PID=727) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:30.745+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:43:30.746+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.746+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:30.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.818+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:30.827+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:43:30.929+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.929+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:30.941+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.941+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:43:30.962+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.224 seconds
[2025-07-17T22:44:01.862+0000] {processor.py:186} INFO - Started process (PID=858) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:01.863+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:44:01.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.864+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:01.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.939+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:01.947+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:02.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.056+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.070+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:44:02.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.234 seconds
[2025-07-17T22:44:32.860+0000] {processor.py:186} INFO - Started process (PID=989) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:32.861+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-17T22:44:32.863+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.862+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:32.931+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.931+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:32.939+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-17T22:44:33.032+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.032+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.043+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-17T22:44:33.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.209 seconds
