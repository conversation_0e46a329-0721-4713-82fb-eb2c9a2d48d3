"""
Централизованная система мониторинга затрат для Biome AI
Безопасный модуль для расчета и отслеживания стоимости операций
"""

import tiktoken
import json
import redis
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, List, Any
from enum import Enum
import logging
import hashlib

# Логи
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OperationType(str, Enum):
    """Типы операций для мониторинга затрат"""
    CHAT_COMPLETION = "chat_completion"
    RECOMMENDATION_GENERATION = "recommendation_generation"
    RISK_ASSESSMENT = "risk_assessment"
    IMAGE_ANALYSIS = "image_analysis"
    FOOD_ANALYSIS = "food_analysis"
    CHALLENGE_GENERATION = "challenge_generation"
    ONBOARDING = "onboarding"
    GENERAL_CHAT = "general_chat"
    PERPLEXITY_SEARCH = "perplexity_search"

class CostTracker:
    """
    Безопасный трекер затрат с интеграцией Redis
    """
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379, redis_db: int = 0):
        """
        Инициализация трекера затрат
        
        Args:
            redis_host: Хост Redis сервера
            redis_port: Порт Redis сервера  
            redis_db: Номер базы данных Redis
        """
        try:
            self.redis_client = redis.Redis(
                host=redis_host, 
                port=redis_port, 
                db=redis_db,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            # Проверяем соединение
            self.redis_client.ping()
            logger.info("Успешное подключение к Redis")
        except Exception as e:
            logger.error(f"Ошибка подключения к Redis: {e}")
            self.redis_client = None
    
    def analyze_token_usage(
        self, 
        input_prompt: str, 
        output_response: str, 
        model: str = "gpt-4o",
        cost_rate_input: float = 0.03,
        cost_rate_output: float = 0.06,
        tokens_unit: int = 1000
    ) -> Dict[str, Any]:
        """
        Анализирует использование токенов для входного промпта и выходного ответа
        
        Args:
            input_prompt: Входной промпт
            output_response: Выходной ответ
            model: Модель OpenAI
            cost_rate_input: Стоимость за 1000 входных токенов в USD
            cost_rate_output: Стоимость за 1000 выходных токенов в USD
            tokens_unit: Единица измерения токенов для расчета стоимости
            
        Returns:
            Словарь с информацией о токенах и стоимости
        """
        try:
            encoder = tiktoken.encoding_for_model(model)
            tokens_input = len(encoder.encode(input_prompt))
            tokens_output = len(encoder.encode(output_response))
            
            cost_input = (tokens_input / tokens_unit) * cost_rate_input
            cost_output = (tokens_output / tokens_unit) * cost_rate_output
            total_cost = cost_input + cost_output
            
            result = {
                "model": model,
                "tokens_input": tokens_input,
                "tokens_output": tokens_output,
                "total_tokens": tokens_input + tokens_output,
                "cost_input": round(cost_input, 6),
                "cost_output": round(cost_output, 6),
                "total_cost": round(total_cost, 6),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "cost_rate_input": cost_rate_input,
                "cost_rate_output": cost_rate_output
            }
            
            logger.info(f"Анализ токенов: {tokens_input} входных, {tokens_output} выходных, стоимость: ${total_cost:.6f}")
            return result
            
        except Exception as e:
            logger.error(f"Ошибка анализа токенов: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def calculate_storage_cost(
        self, 
        data_size_mb: float, 
        storage_days: int = 30,
        cost_per_gb_month: float = 0.023
    ) -> Dict[str, Any]:
        """
        Рассчитывает стоимость хранения данных
        
        Args:
            data_size_mb: Размер данных в мегабайтах
            storage_days: Количество дней хранения
            cost_per_gb_month: Стоимость за GB в месяц в USD
            
        Returns:
            Словарь с информацией о стоимости хранения
        """
        try:
            data_size_gb = data_size_mb / 1024
            storage_months = storage_days / 30
            storage_cost = data_size_gb * storage_months * cost_per_gb_month
            
            return {
                "data_size_mb": data_size_mb,
                "data_size_gb": round(data_size_gb, 6),
                "storage_days": storage_days,
                "storage_months": round(storage_months, 6),
                "storage_cost": round(storage_cost, 6),
                "cost_per_gb_month": cost_per_gb_month,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            logger.error(f"Ошибка расчета стоимости хранения: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def calculate_integration_cost(
        self, 
        api_calls: int,
        cost_per_call: float = 0.001
    ) -> Dict[str, Any]:
        """
        Рассчитывает стоимость интеграций и API вызовов
        
        Args:
            api_calls: Количество API вызовов
            cost_per_call: Стоимость за один вызов в USD
            
        Returns:
            Словарь с информацией о стоимости интеграций
        """
        try:
            integration_cost = api_calls * cost_per_call
            
            return {
                "api_calls": api_calls,
                "cost_per_call": cost_per_call,
                "integration_cost": round(integration_cost, 6),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            logger.error(f"Ошибка расчета стоимости интеграций: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    def track_operation_cost(
        self,
        operation_type: OperationType,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        token_usage: Optional[Dict] = None,
        storage_cost: Optional[Dict] = None,
        integration_cost: Optional[Dict] = None,
        additional_metadata: Optional[Dict] = None
    ) -> bool:
        """
        Безопасно сохраняет информацию о затратах операции в Redis

        Args:
            operation_type: Тип операции
            user_id: ID пользователя (опционально для безопасности)
            session_id: ID сессии
            token_usage: Данные об использовании токенов
            storage_cost: Данные о стоимости хранения
            integration_cost: Данные о стоимости интеграций
            additional_metadata: Дополнительные метаданные

        Returns:
            True если успешно сохранено, False в противном случае
        """
        if not self.redis_client:
            logger.warning("Redis недоступен, пропускаем сохранение метрик")
            return False

        try:
            # Сейф ключ для операции.
            timestamp = datetime.now(timezone.utc)
            operation_id = hashlib.md5(
                f"{operation_type}_{timestamp.isoformat()}_{session_id}".encode()
            ).hexdigest()[:16]

            # Сохраняемые данные
            cost_data = {
                "operation_id": operation_id,
                "operation_type": operation_type.value,
                "timestamp": timestamp.isoformat(),
                "session_id": session_id,
                "token_usage": token_usage or {},
                "storage_cost": storage_cost or {},
                "integration_cost": integration_cost or {},
                "additional_metadata": additional_metadata or {}
            }

            # Добавление user_id только если он есть.
            if user_id:
                cost_data["user_id_hash"] = hashlib.sha256(str(user_id).encode()).hexdigest()[:16]

            # Сохранение в Redis с TTL (30 дней)
            redis_key = f"cost_tracking:{operation_type.value}:{operation_id}"
            self.redis_client.setex(
                redis_key,
                2592000,  # 30 дней в секундах
                json.dumps(cost_data)
            )

            self._update_aggregated_stats(operation_type, cost_data)

            logger.info(f"Сохранены метрики для операции {operation_type.value}: {operation_id}")
            return True

        except Exception as e:
            logger.error(f"Ошибка сохранения метрик: {e}")
            return False

    def _update_aggregated_stats(self, operation_type: OperationType, cost_data: Dict) -> None:
        """
        Обновляет агрегированную статистику по операциям

        Args:
            operation_type: Тип операции
            cost_data: Данные о затратах
        """
        try:
            today = datetime.now(timezone.utc).date().isoformat()
            stats_key = f"cost_stats:daily:{operation_type.value}:{today}"

            current_stats = self.redis_client.get(stats_key)
            if current_stats:
                stats = json.loads(current_stats)
            else:
                stats = {
                    "operation_count": 0,
                    "total_cost": 0.0,
                    "total_tokens": 0,
                    "date": today,
                    "operation_type": operation_type.value
                }

           
            stats["operation_count"] += 1

            if cost_data.get("token_usage"):
                token_data = cost_data["token_usage"]
                stats["total_cost"] += token_data.get("total_cost", 0)
                stats["total_tokens"] += token_data.get("total_tokens", 0)

            if cost_data.get("storage_cost"):
                stats["total_cost"] += cost_data["storage_cost"].get("storage_cost", 0)

            if cost_data.get("integration_cost"):
                stats["total_cost"] += cost_data["integration_cost"].get("integration_cost", 0)

        
            self.redis_client.setex(
                stats_key,
                7776000,  # 90 дней в секундах
                json.dumps(stats)
            )

        except Exception as e:
            logger.error(f"Ошибка обновления агрегированной статистики: {e}")

    def get_operation_stats(
        self,
        operation_type: Optional[OperationType] = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        Получает статистику по операциям за указанный период

        Args:
            operation_type: Тип операции (если None, то по всем типам)
            days: Количество дней для анализа

        Returns:
            Словарь со статистикой
        """
        if not self.redis_client:
            return {"error": "Redis недоступен"}

        try:
            stats = {}
            current_date = datetime.now(timezone.utc).date()

            
            operation_types = [operation_type] if operation_type else list(OperationType)

            for op_type in operation_types:
                op_stats = {
                    "total_operations": 0,
                    "total_cost": 0.0,
                    "total_tokens": 0,
                    "daily_breakdown": []
                }

                for i in range(days):
                    date = (current_date - timedelta(days=i)).isoformat()
                    stats_key = f"cost_stats:daily:{op_type.value}:{date}"

                    daily_data = self.redis_client.get(stats_key)
                    if daily_data:
                        daily_stats = json.loads(daily_data)
                        op_stats["total_operations"] += daily_stats.get("operation_count", 0)
                        op_stats["total_cost"] += daily_stats.get("total_cost", 0)
                        op_stats["total_tokens"] += daily_stats.get("total_tokens", 0)
                        op_stats["daily_breakdown"].append({
                            "date": date,
                            **daily_stats
                        })

                stats[op_type.value] = op_stats

            return {
                "period_days": days,
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "statistics": stats
            }

        except Exception as e:
            logger.error(f"Ошибка получения статистики: {e}")
            return {"error": str(e)}

# Основные сценарии использования (тест-кейсы)
class UsageScenarios:
    """
    Определяет основные сценарии использования системы и их стоимость
    """

    @staticmethod
    def get_scenario_definitions() -> Dict[str, Dict]:
        """
        Возвращает определения основных сценариев использования
        """
        return {
            "onboarding": {
                "name": "Онбординг пользователя",
                "description": "Полный процесс онбординга нового пользователя",
                "operations": [
                    {"type": OperationType.ONBOARDING, "count": 1},
                    {"type": OperationType.RECOMMENDATION_GENERATION, "count": 3},
                    {"type": OperationType.RISK_ASSESSMENT, "count": 2}
                ],
                "estimated_tokens": 15000,
                "estimated_cost": 0.45
            },
            "daily_interaction": {
                "name": "Ежедневное взаимодействие",
                "description": "Типичное ежедневное использование приложения",
                "operations": [
                    {"type": OperationType.GENERAL_CHAT, "count": 5},
                    {"type": OperationType.FOOD_ANALYSIS, "count": 3},
                    {"type": OperationType.RECOMMENDATION_GENERATION, "count": 1}
                ],
                "estimated_tokens": 8000,
                "estimated_cost": 0.24
            },
            "challenge_creation": {
                "name": "Создание челленджа",
                "description": "Генерация персонализированного челленджа",
                "operations": [
                    {"type": OperationType.CHALLENGE_GENERATION, "count": 1},
                    {"type": OperationType.RECOMMENDATION_GENERATION, "count": 2}
                ],
                "estimated_tokens": 6000,
                "estimated_cost": 0.18
            },
            "risk_analysis": {
                "name": "Анализ рисков",
                "description": "Комплексная оценка рисков для здоровья",
                "operations": [
                    {"type": OperationType.RISK_ASSESSMENT, "count": 3},
                    {"type": OperationType.RECOMMENDATION_GENERATION, "count": 2}
                ],
                "estimated_tokens": 10000,
                "estimated_cost": 0.30
            },
            "image_analysis": {
                "name": "Анализ изображений еды",
                "description": "Анализ фотографий еды и расчет калорий",
                "operations": [
                    {"type": OperationType.IMAGE_ANALYSIS, "count": 1},
                    {"type": OperationType.FOOD_ANALYSIS, "count": 1}
                ],
                "estimated_tokens": 4000,
                "estimated_cost": 0.12
            }
        }

# Глобальный экземпляр трекера затрат
cost_tracker = CostTracker()


def track_openai_request(
    operation_type: OperationType,
    input_prompt: str,
    output_response: str,
    model: str = "gpt-4o",
    user_id: Optional[int] = None,
    session_id: Optional[str] = None,
    additional_metadata: Optional[Dict] = None
) -> Dict[str, Any]:
    """
 функция для отслеживания OpenAI запросов

    Args:
        operation_type: Тип операции
        input_prompt: Входной промпт
        output_response: Выходной ответ
        model: Модель OpenAI
        user_id: ID пользователя
        session_id: ID сессии
        additional_metadata: Дополнительные метаданные

    Returns:
        Результат анализа токенов
    """
   
    token_analysis = cost_tracker.analyze_token_usage(
        input_prompt=input_prompt,
        output_response=output_response,
        model=model
    )

    # Сохраняем метрики
    cost_tracker.track_operation_cost(
        operation_type=operation_type,
        user_id=user_id,
        session_id=session_id,
        token_usage=token_analysis,
        additional_metadata=additional_metadata
    )

    return token_analysis

def get_cost_summary(days: int = 7) -> Dict[str, Any]:
    """
    Получает сводку по затратам за указанный период

    Args:
        days: Количество дней для анализа

    Returns:
        Сводка по затратам
    """
    return cost_tracker.get_operation_stats(days=days)
