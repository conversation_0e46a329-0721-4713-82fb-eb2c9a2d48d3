[2025-07-17T21:34:28.605+0000] {processor.py:186} INFO - Started process (PID=201) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:34:28.606+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:34:28.608+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.608+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:34:28.698+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.698+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:28.707+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:34:28.816+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.816+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:28.981+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.981+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:34:29.004+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.405 seconds
[2025-07-17T21:34:59.758+0000] {processor.py:186} INFO - Started process (PID=339) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:34:59.760+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:34:59.762+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.762+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:34:59.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.836+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:59.845+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:35:00.111+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:00.120+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.120+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:35:00.140+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.387 seconds
[2025-07-17T21:35:30.834+0000] {processor.py:186} INFO - Started process (PID=475) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:35:30.835+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:35:30.839+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.838+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:35:30.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.918+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:30.924+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:35:31.020+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.020+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:31.033+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.033+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:35:31.053+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.226 seconds
[2025-07-17T21:36:01.189+0000] {processor.py:186} INFO - Started process (PID=611) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:36:01.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:36:01.192+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.192+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:36:01.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.263+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.271+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:36:01.376+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.375+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:01.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.387+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:36:01.407+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.223 seconds
[2025-07-17T21:36:31.492+0000] {processor.py:186} INFO - Started process (PID=747) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:36:31.493+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:36:31.495+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.494+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:36:31.562+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.562+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:31.571+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:36:31.668+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.668+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.680+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:36:31.703+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.218 seconds
[2025-07-17T21:37:01.879+0000] {processor.py:186} INFO - Started process (PID=883) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:37:01.880+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:37:01.882+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.882+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:37:01.958+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.958+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:01.967+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:37:02.072+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.072+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:02.084+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.084+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:37:02.109+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.238 seconds
[2025-07-17T21:37:32.562+0000] {processor.py:186} INFO - Started process (PID=1019) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:37:32.562+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:37:32.565+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.564+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:37:32.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.639+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:32.647+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:37:32.750+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.749+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:32.761+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.760+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:37:32.782+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.226 seconds
[2025-07-17T21:38:03.012+0000] {processor.py:186} INFO - Started process (PID=1155) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:38:03.014+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:38:03.017+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.017+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:38:03.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.119+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:03.129+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:38:03.227+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.227+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:03.240+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.240+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:38:03.262+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.256 seconds
[2025-07-17T21:38:33.557+0000] {processor.py:186} INFO - Started process (PID=1291) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:38:33.557+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:38:33.560+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.560+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:38:33.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.635+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:33.643+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:38:33.744+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.744+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:33.755+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.755+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:38:33.777+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.226 seconds
[2025-07-17T21:39:04.436+0000] {processor.py:186} INFO - Started process (PID=1427) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:39:04.437+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:39:04.439+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.439+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:39:04.516+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.515+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:04.523+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:39:04.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.628+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:04.640+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.640+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:39:04.662+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.232 seconds
[2025-07-17T21:39:35.036+0000] {processor.py:186} INFO - Started process (PID=1563) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:39:35.038+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:39:35.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:39:35.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.110+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.120+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:39:35.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.215+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:35.226+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.225+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:39:35.245+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.216 seconds
[2025-07-17T21:40:05.727+0000] {processor.py:186} INFO - Started process (PID=1699) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:40:05.728+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:40:05.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.730+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:40:05.820+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.819+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:05.828+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:40:05.943+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.943+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:05.956+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.955+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:40:05.979+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.258 seconds
[2025-07-17T21:40:36.558+0000] {processor.py:186} INFO - Started process (PID=1835) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:40:36.559+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:40:36.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:40:36.655+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.654+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:36.664+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:40:36.794+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.794+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:36.807+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.806+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:40:36.827+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.277 seconds
[2025-07-17T21:41:07.601+0000] {processor.py:186} INFO - Started process (PID=2033) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:41:07.603+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:41:07.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.606+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:41:07.772+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.771+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:41:07.822+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:41:08.091+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:08.091+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:41:08.111+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:08.111+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:41:08.143+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.553 seconds
[2025-07-17T21:42:55.425+0000] {processor.py:186} INFO - Started process (PID=201) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:42:55.426+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:42:55.429+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.428+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:42:55.508+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.508+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:55.516+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:42:55.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.639+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:55.798+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.798+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:42:55.818+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.399 seconds
[2025-07-17T21:43:26.260+0000] {processor.py:186} INFO - Started process (PID=337) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:43:26.262+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:43:26.265+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.265+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:43:26.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.337+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:26.344+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:43:26.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.631+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:26.643+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.642+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:43:26.663+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.409 seconds
[2025-07-17T21:43:57.620+0000] {processor.py:186} INFO - Started process (PID=475) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:43:57.621+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:43:57.625+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.624+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:43:57.701+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.701+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:57.711+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:43:57.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.817+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:57.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.835+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:43:57.860+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.246 seconds
[2025-07-17T21:44:28.166+0000] {processor.py:186} INFO - Started process (PID=611) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:44:28.167+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:44:28.169+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.168+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:44:28.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.243+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:28.252+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:44:28.354+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.353+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:28.363+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.363+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:44:28.382+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.224 seconds
[2025-07-17T21:44:58.780+0000] {processor.py:186} INFO - Started process (PID=747) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:44:58.781+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:44:58.787+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.786+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:44:58.885+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.885+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:58.895+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:44:59.022+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.021+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:59.035+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.034+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:44:59.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.290 seconds
[2025-07-17T21:55:23.361+0000] {processor.py:186} INFO - Started process (PID=202) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:55:23.362+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:55:23.364+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.364+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:55:23.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.454+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:23.463+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:55:23.580+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.580+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:23.746+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.746+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:55:23.765+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.412 seconds
[2025-07-17T21:55:54.167+0000] {processor.py:186} INFO - Started process (PID=340) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:55:54.168+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:55:54.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.174+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:55:54.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.245+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:54.253+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:55:54.516+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.515+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:54.525+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.525+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:55:54.542+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.381 seconds
[2025-07-17T21:56:24.916+0000] {processor.py:186} INFO - Started process (PID=476) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:56:24.916+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:56:24.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.918+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:56:24.996+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.996+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:25.004+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:56:25.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.119+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:25.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.131+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:56:25.151+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.240 seconds
[2025-07-17T21:56:55.227+0000] {processor.py:186} INFO - Started process (PID=613) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:56:55.228+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T21:56:55.230+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.230+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:56:55.300+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.300+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:55.307+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T21:56:55.405+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.405+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:55.416+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.416+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T21:56:55.436+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.214 seconds
