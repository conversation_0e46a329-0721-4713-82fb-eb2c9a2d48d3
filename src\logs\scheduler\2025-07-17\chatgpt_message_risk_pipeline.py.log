[2025-07-17T22:41:29.601+0000] {processor.py:186} INFO - Started process (PID=238) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:41:29.602+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:41:29.605+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.604+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:41:29.690+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.689+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:29.698+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:41:29.794+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.794+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.959+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.959+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:41:29.975+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.382 seconds
[2025-07-17T22:42:00.232+0000] {processor.py:186} INFO - Started process (PID=369) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:42:00.234+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:42:00.236+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.236+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:42:00.311+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.310+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:00.317+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:42:00.548+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.547+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:00.557+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:42:00.576+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.349 seconds
[2025-07-17T22:42:31.029+0000] {processor.py:186} INFO - Started process (PID=500) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:42:31.030+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:42:31.032+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.031+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:42:31.218+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.217+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:31.224+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:42:31.305+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.304+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:31.313+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.313+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:42:31.330+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.305 seconds
[2025-07-17T22:43:01.661+0000] {processor.py:186} INFO - Started process (PID=631) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:43:01.662+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:43:01.663+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.663+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:43:01.741+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.740+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:01.748+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:43:01.847+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.847+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:01.858+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.858+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:43:01.878+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.223 seconds
[2025-07-17T22:43:32.097+0000] {processor.py:186} INFO - Started process (PID=762) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:43:32.098+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:43:32.099+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.099+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:43:32.172+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.171+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:32.180+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:43:32.285+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.284+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:32.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.295+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:43:32.312+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.220 seconds
[2025-07-17T22:44:02.940+0000] {processor.py:186} INFO - Started process (PID=893) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:44:02.941+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:44:02.943+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.942+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:44:03.010+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.010+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:03.018+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:44:03.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.107+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:03.118+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.118+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:44:03.137+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.202 seconds
[2025-07-17T22:44:33.921+0000] {processor.py:186} INFO - Started process (PID=1024) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:44:33.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:44:33.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:44:34.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.005+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:34.014+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:44:34.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.104+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:34.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.114+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:44:34.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.219 seconds
[2025-07-17T22:45:05.067+0000] {processor.py:186} INFO - Started process (PID=1155) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:45:05.068+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:45:05.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.070+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:45:05.138+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.138+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:05.147+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:45:05.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.239+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:05.250+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.250+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:45:05.269+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.208 seconds
[2025-07-17T22:45:35.636+0000] {processor.py:186} INFO - Started process (PID=1284) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:45:35.637+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:45:35.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.639+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:45:35.722+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.722+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:35.730+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:45:35.827+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.827+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:35.839+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.839+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:45:35.859+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.230 seconds
[2025-07-17T22:46:06.455+0000] {processor.py:186} INFO - Started process (PID=1415) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:46:06.456+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:46:06.457+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.457+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:46:06.532+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.532+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:06.542+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:46:06.645+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.645+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:06.662+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.661+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:46:06.682+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.234 seconds
[2025-07-17T22:46:36.983+0000] {processor.py:186} INFO - Started process (PID=1548) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:46:36.984+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:46:36.985+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.985+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:46:37.052+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.052+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:37.059+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:46:37.177+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.177+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:37.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.187+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:46:37.205+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.227 seconds
[2025-07-17T22:47:07.762+0000] {processor.py:186} INFO - Started process (PID=1683) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:47:07.762+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:47:07.764+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.763+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:47:07.832+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.832+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:07.839+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:47:07.935+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.935+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:07.947+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.947+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:47:07.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.211 seconds
[2025-07-17T22:47:39.010+0000] {processor.py:186} INFO - Started process (PID=1820) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:47:39.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:47:39.013+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.012+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:47:39.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.104+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:39.113+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:47:39.217+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.217+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:39.228+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.228+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:47:39.251+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.249 seconds
[2025-07-17T22:48:09.769+0000] {processor.py:186} INFO - Started process (PID=1951) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:48:09.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:48:09.771+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.771+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:48:09.841+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.840+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:09.846+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:48:09.940+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.940+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:09.951+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.951+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:48:09.971+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.207 seconds
[2025-07-17T22:48:40.818+0000] {processor.py:186} INFO - Started process (PID=2083) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:48:40.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:48:40.820+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.820+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:48:40.898+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.898+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:40.908+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:48:41.013+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.013+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:41.025+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.025+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:48:41.046+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.234 seconds
[2025-07-17T22:49:11.472+0000] {processor.py:186} INFO - Started process (PID=2213) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:49:11.473+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:49:11.476+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.475+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:49:11.571+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.571+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:11.578+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:49:11.695+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.695+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:11.709+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.708+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:49:11.730+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.266 seconds
[2025-07-17T22:49:42.381+0000] {processor.py:186} INFO - Started process (PID=2344) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:49:42.382+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-17T22:49:42.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.383+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:49:42.449+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.449+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:42.458+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-17T22:49:42.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:42.574+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.574+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-17T22:49:42.590+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.215 seconds
