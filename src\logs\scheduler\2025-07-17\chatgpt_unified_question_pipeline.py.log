[2025-07-17T21:34:30.158+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:34:30.159+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:34:30.161+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.161+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:34:30.184+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:34:30.305+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.305+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:30.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.317+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:34:30.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.193 seconds
[2025-07-17T21:35:01.696+0000] {processor.py:186} INFO - Started process (PID=379) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:01.697+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:35:01.701+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.701+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:01.718+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:02.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.004+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:02.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.014+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:35:02.036+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.348 seconds
[2025-07-17T21:35:32.326+0000] {processor.py:186} INFO - Started process (PID=515) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:32.327+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:35:32.331+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.330+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:32.354+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:32.485+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.485+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.499+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.499+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:35:32.519+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.200 seconds
[2025-07-17T21:36:02.595+0000] {processor.py:186} INFO - Started process (PID=651) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:02.596+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:36:02.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.597+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:02.611+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:02.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:02.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.743+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:36:02.770+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.180 seconds
[2025-07-17T21:36:32.856+0000] {processor.py:186} INFO - Started process (PID=785) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:32.857+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:36:32.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.859+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:32.875+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:32.990+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.990+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:33.001+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.001+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:36:33.021+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.171 seconds
[2025-07-17T21:37:03.312+0000] {processor.py:186} INFO - Started process (PID=921) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:37:03.313+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:37:03.315+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.315+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:37:03.333+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:37:03.464+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.464+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:03.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.485+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:37:03.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.206 seconds
[2025-07-17T21:37:33.701+0000] {processor.py:186} INFO - Started process (PID=1057) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:37:33.702+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:37:33.704+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.704+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:37:33.722+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:37:33.848+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.848+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:33.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.860+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:37:33.882+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.187 seconds
[2025-07-17T21:38:04.206+0000] {processor.py:186} INFO - Started process (PID=1190) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:38:04.207+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:38:04.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.209+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:38:04.224+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:38:04.344+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.343+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:04.355+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.355+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:38:04.376+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.176 seconds
[2025-07-17T21:38:34.751+0000] {processor.py:186} INFO - Started process (PID=1326) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:38:34.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:38:34.755+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.755+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:38:34.776+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:38:34.916+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.916+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:34.927+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.927+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:38:34.949+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.204 seconds
[2025-07-17T21:39:05.313+0000] {processor.py:186} INFO - Started process (PID=1460) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:39:05.314+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:39:05.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.316+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:39:05.335+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:39:05.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.458+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:05.469+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.469+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:39:05.490+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.184 seconds
[2025-07-17T21:39:36.026+0000] {processor.py:186} INFO - Started process (PID=1601) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:39:36.027+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:39:36.029+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:39:36.048+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:39:36.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:36.205+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.204+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:39:36.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.206 seconds
[2025-07-17T21:40:07.069+0000] {processor.py:186} INFO - Started process (PID=1739) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:40:07.070+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:40:07.073+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.072+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:40:07.093+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:40:07.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.255+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:07.272+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.271+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:40:07.300+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.238 seconds
[2025-07-17T21:40:38.155+0000] {processor.py:186} INFO - Started process (PID=1875) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:40:38.156+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:40:38.159+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.158+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:40:38.176+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:40:38.316+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.316+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:38.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:40:38.350+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.202 seconds
[2025-07-17T21:42:57.041+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:42:57.042+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:42:57.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:42:57.064+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:42:57.189+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.188+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:57.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.202+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:42:57.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.193 seconds
[2025-07-17T21:43:28.008+0000] {processor.py:186} INFO - Started process (PID=377) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:43:28.009+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:43:28.012+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.012+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:43:28.037+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:43:28.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.334+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:28.346+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:43:28.368+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.367 seconds
[2025-07-17T21:43:58.915+0000] {processor.py:186} INFO - Started process (PID=519) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:43:58.916+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:43:58.919+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.918+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:43:58.938+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:43:59.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.070+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.084+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.084+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:43:59.106+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.197 seconds
[2025-07-17T21:44:29.710+0000] {processor.py:186} INFO - Started process (PID=657) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:44:29.711+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:44:29.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.714+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:44:29.737+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:44:29.864+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.864+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:29.877+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.877+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:44:29.900+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.197 seconds
[2025-07-17T21:45:00.052+0000] {processor.py:186} INFO - Started process (PID=791) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:45:00.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:45:00.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:00.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:45:00.073+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:45:00.189+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:00.189+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:00.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:00.201+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:45:00.224+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.177 seconds
[2025-07-17T21:55:24.894+0000] {processor.py:186} INFO - Started process (PID=242) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:55:24.895+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:55:24.898+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.897+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:55:24.915+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:55:25.036+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.035+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:25.048+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.048+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:55:25.070+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.182 seconds
[2025-07-17T21:55:55.789+0000] {processor.py:186} INFO - Started process (PID=378) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:55:55.790+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:55:55.793+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.792+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:55:55.810+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:55:56.062+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:56.073+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.073+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:55:56.093+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.310 seconds
[2025-07-17T21:56:26.323+0000] {processor.py:186} INFO - Started process (PID=516) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:56:26.324+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:56:26.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.326+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:56:26.343+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:56:26.476+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.476+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:26.490+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.490+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:56:26.513+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.195 seconds
[2025-07-17T21:56:56.615+0000] {processor.py:186} INFO - Started process (PID=653) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:56:56.616+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:56:56.618+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.618+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:56:56.634+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:56:56.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.740+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:56.752+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.751+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:56:56.770+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.161 seconds
[2025-07-17T21:57:26.975+0000] {processor.py:186} INFO - Started process (PID=795) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:57:26.976+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:57:26.978+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.978+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:57:26.993+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:57:27.097+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:27.097+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:27.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:27.107+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:57:27.124+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.154 seconds
[2025-07-17T21:57:57.477+0000] {processor.py:186} INFO - Started process (PID=931) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:57:57.478+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:57:57.481+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.481+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:57:57.501+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:57:57.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.673+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:57.687+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.687+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:57:57.710+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.240 seconds
[2025-07-17T21:58:27.965+0000] {processor.py:186} INFO - Started process (PID=1067) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:58:27.966+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:58:27.969+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.968+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:58:27.983+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:58:28.086+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:28.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:28.097+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:28.097+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:58:28.115+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.156 seconds
[2025-07-17T22:00:36.466+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:00:36.468+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:00:36.470+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.470+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:00:36.484+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:00:36.597+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.597+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:36.609+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.609+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:00:36.633+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.171 seconds
[2025-07-17T22:01:07.263+0000] {processor.py:186} INFO - Started process (PID=377) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:01:07.264+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:01:07.267+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.266+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:01:07.290+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:01:07.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.583+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:07.595+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.595+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:01:07.618+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.362 seconds
[2025-07-17T22:01:37.697+0000] {processor.py:186} INFO - Started process (PID=515) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:01:37.698+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:01:37.700+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.700+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:01:37.717+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:01:37.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.818+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:37.829+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.829+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:01:37.847+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.157 seconds
[2025-07-17T22:02:07.938+0000] {processor.py:186} INFO - Started process (PID=649) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:02:07.939+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:02:07.941+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.941+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:02:07.955+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:02:08.062+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:08.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:08.072+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:08.072+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:02:08.089+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.157 seconds
[2025-07-17T22:02:38.361+0000] {processor.py:186} INFO - Started process (PID=785) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:02:38.362+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:02:38.364+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.364+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:02:38.379+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:02:38.485+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.484+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:38.495+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.495+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:02:38.514+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.160 seconds
[2025-07-17T22:03:08.691+0000] {processor.py:186} INFO - Started process (PID=921) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:03:08.692+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:03:08.694+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.694+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:03:08.708+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:03:08.816+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.815+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:08.827+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.827+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:03:08.847+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.161 seconds
[2025-07-17T22:03:39.231+0000] {processor.py:186} INFO - Started process (PID=1057) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:03:39.233+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:03:39.236+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.235+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:03:39.252+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:03:39.363+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.363+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:39.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.375+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:03:39.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.170 seconds
[2025-07-17T22:04:10.222+0000] {processor.py:186} INFO - Started process (PID=1193) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:04:10.223+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:04:10.226+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.225+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:04:10.246+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:04:10.358+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.358+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:10.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.368+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:04:10.390+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.175 seconds
[2025-07-17T22:04:40.832+0000] {processor.py:186} INFO - Started process (PID=1331) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:04:40.833+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:04:40.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.835+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:04:40.851+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:04:40.959+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.959+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:40.970+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:04:40.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.161 seconds
[2025-07-17T22:05:11.084+0000] {processor.py:186} INFO - Started process (PID=1465) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:05:11.085+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:05:11.088+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.087+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:05:11.103+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:05:11.215+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.215+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:11.226+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.226+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:05:11.245+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.166 seconds
[2025-07-17T22:05:41.389+0000] {processor.py:186} INFO - Started process (PID=1601) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:05:41.390+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:05:41.392+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.392+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:05:41.409+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:05:41.525+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.525+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:41.537+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.536+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:05:41.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.174 seconds
[2025-07-17T22:06:12.011+0000] {processor.py:186} INFO - Started process (PID=1737) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:06:12.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:06:12.013+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:06:12.028+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:06:12.133+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.133+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:12.144+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:06:12.162+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.156 seconds
[2025-07-17T22:07:28.161+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:07:28.162+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:07:28.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.164+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:07:28.184+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:07:28.296+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.296+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:28.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.306+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:07:28.325+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.173 seconds
[2025-07-17T22:07:59.648+0000] {processor.py:186} INFO - Started process (PID=377) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:07:59.649+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:07:59.651+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:07:59.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:07:59.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.930+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:59.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.939+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:07:59.959+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.316 seconds
[2025-07-17T22:08:30.423+0000] {processor.py:186} INFO - Started process (PID=515) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:08:30.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:08:30.426+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:08:30.445+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:08:30.586+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.585+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:30.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.600+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:08:30.628+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.211 seconds
[2025-07-17T22:09:00.850+0000] {processor.py:186} INFO - Started process (PID=651) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:09:00.851+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:09:00.853+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.852+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:09:00.867+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:09:00.973+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.972+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:00.983+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.983+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:09:01.003+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.158 seconds
