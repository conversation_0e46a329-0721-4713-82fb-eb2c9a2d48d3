[2025-07-17T22:41:26.818+0000] {processor.py:186} INFO - Started process (PID=185) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:26.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:41:26.821+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:26.821+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:26.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:26.918+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:26.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:27.166+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.166+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:27.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:41:27.210+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.398 seconds
[2025-07-17T22:41:58.076+0000] {processor.py:186} INFO - Started process (PID=322) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:58.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:41:58.080+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:58.156+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.156+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:58.164+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:58.466+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.466+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:58.480+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.479+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:41:58.504+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.435 seconds
[2025-07-17T22:42:28.716+0000] {processor.py:186} INFO - Started process (PID=453) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:42:28.717+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:42:28.718+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:28.718+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:42:28.976+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:28.976+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:28.983+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:42:29.072+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.071+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:29.083+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.083+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:42:29.102+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.394 seconds
[2025-07-17T22:42:59.817+0000] {processor.py:186} INFO - Started process (PID=584) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:42:59.818+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:42:59.820+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:59.819+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:42:59.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:59.906+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:59.915+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:43:00.018+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.018+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:00.033+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.032+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:43:00.052+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.242 seconds
[2025-07-17T22:43:30.199+0000] {processor.py:186} INFO - Started process (PID=715) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:43:30.200+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:43:30.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.201+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:43:30.279+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.279+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:30.289+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:43:30.402+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.402+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:30.416+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.416+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:43:30.442+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.251 seconds
[2025-07-17T22:44:01.323+0000] {processor.py:186} INFO - Started process (PID=846) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:44:01.324+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:44:01.325+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.325+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:44:01.403+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.403+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:01.412+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:44:01.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.524+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:01.538+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.538+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:44:01.560+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.243 seconds
[2025-07-17T22:44:32.353+0000] {processor.py:186} INFO - Started process (PID=977) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:44:32.354+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:44:32.356+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.356+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:44:32.434+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.433+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:32.441+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:44:32.540+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.540+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:32.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.550+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:44:32.569+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.223 seconds
[2025-07-17T22:45:03.249+0000] {processor.py:186} INFO - Started process (PID=1108) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:45:03.250+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:45:03.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.251+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:45:03.324+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.324+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:03.331+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:45:03.431+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.431+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:03.444+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.444+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:45:03.465+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.221 seconds
[2025-07-17T22:45:33.911+0000] {processor.py:186} INFO - Started process (PID=1239) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:45:33.912+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:45:33.913+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:33.913+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:45:33.993+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:33.993+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:34.003+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:45:34.108+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.108+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:34.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.127+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:45:34.161+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.256 seconds
[2025-07-17T22:46:04.883+0000] {processor.py:186} INFO - Started process (PID=1370) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:46:04.884+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:46:04.885+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:04.885+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:46:04.957+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:04.957+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:04.966+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:46:05.067+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.067+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:05.080+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.079+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:46:05.099+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.224 seconds
[2025-07-17T22:46:35.206+0000] {processor.py:186} INFO - Started process (PID=1501) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:46:35.207+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:46:35.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.208+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:46:35.287+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.286+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:35.296+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:46:35.402+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.401+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:35.413+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.413+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:46:35.433+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.233 seconds
[2025-07-17T22:47:06.301+0000] {processor.py:186} INFO - Started process (PID=1638) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:47:06.302+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:47:06.304+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.303+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:47:06.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.384+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:06.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:47:06.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.493+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:06.506+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.506+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:47:06.524+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.229 seconds
[2025-07-17T22:47:37.384+0000] {processor.py:186} INFO - Started process (PID=1775) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:47:37.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:47:37.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.387+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:47:37.467+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.467+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:37.476+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:47:37.586+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.586+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:37.600+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.600+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:47:37.622+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.243 seconds
[2025-07-17T22:48:08.278+0000] {processor.py:186} INFO - Started process (PID=1906) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:48:08.279+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:48:08.281+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.280+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:48:08.356+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.356+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:08.364+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:48:08.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.458+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:08.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.471+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:48:08.490+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.217 seconds
[2025-07-17T22:48:39.231+0000] {processor.py:186} INFO - Started process (PID=2037) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:48:39.231+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:48:39.233+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.232+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:48:39.314+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.313+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:39.323+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:48:39.429+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.429+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:39.444+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.444+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:48:39.465+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.240 seconds
[2025-07-17T22:49:09.628+0000] {processor.py:186} INFO - Started process (PID=2168) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:49:09.629+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:49:09.630+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:09.630+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:49:09.706+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:09.706+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:09.715+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:49:09.841+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:09.840+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:09.853+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:09.853+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:49:09.878+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.257 seconds
[2025-07-17T22:49:40.169+0000] {processor.py:186} INFO - Started process (PID=2299) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:49:40.170+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:49:40.172+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.172+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:49:40.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.246+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:40.255+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:49:40.365+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.365+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:40.377+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.377+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:49:40.396+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.234 seconds
[2025-07-17T22:50:10.757+0000] {processor.py:186} INFO - Started process (PID=2430) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:50:10.758+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:50:10.759+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:10.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:50:10.833+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:10.833+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:10.840+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:50:10.949+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:10.949+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:10.963+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:10.963+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:50:10.983+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.232 seconds
[2025-07-17T22:50:41.366+0000] {processor.py:186} INFO - Started process (PID=2561) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:50:41.367+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:50:41.369+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:41.369+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:50:41.446+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:41.446+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:41.454+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:50:41.552+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:41.552+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:41.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:41.563+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:50:41.588+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.229 seconds
[2025-07-17T22:51:12.093+0000] {processor.py:186} INFO - Started process (PID=2692) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:51:12.095+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:51:12.096+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.096+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:51:12.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.176+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:12.187+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:51:12.301+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.301+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:12.314+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.313+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:51:12.332+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.247 seconds
[2025-07-17T22:51:42.733+0000] {processor.py:186} INFO - Started process (PID=2823) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:51:42.734+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:51:42.735+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:42.735+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:51:42.833+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:42.833+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:42.840+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:51:42.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:42.937+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:42.948+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:42.948+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:51:42.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.240 seconds
[2025-07-17T22:52:13.499+0000] {processor.py:186} INFO - Started process (PID=2954) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:52:13.500+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:52:13.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:13.502+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:52:13.575+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:13.574+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:13.584+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:52:13.682+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:13.682+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:13.693+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:13.693+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:52:13.712+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.219 seconds
