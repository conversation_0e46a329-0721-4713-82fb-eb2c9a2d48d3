[2025-07-17T21:34:30.158+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:34:30.159+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:34:30.161+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.161+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:34:30.184+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:34:30.305+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.305+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:30.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.317+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:34:30.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.193 seconds
[2025-07-17T21:35:01.696+0000] {processor.py:186} INFO - Started process (PID=379) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:01.697+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:35:01.701+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.701+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:01.718+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:02.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.004+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:02.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.014+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:35:02.036+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.348 seconds
[2025-07-17T21:35:32.326+0000] {processor.py:186} INFO - Started process (PID=515) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:32.327+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:35:32.331+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.330+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:32.354+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:35:32.485+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.485+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.499+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.499+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:35:32.519+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.200 seconds
[2025-07-17T21:36:02.595+0000] {processor.py:186} INFO - Started process (PID=651) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:02.596+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:36:02.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.597+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:02.611+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:02.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:02.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.743+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:36:02.770+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.180 seconds
[2025-07-17T21:36:32.856+0000] {processor.py:186} INFO - Started process (PID=785) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:32.857+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T21:36:32.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.859+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:32.875+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T21:36:32.990+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.990+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:33.001+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.001+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T21:36:33.021+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.171 seconds
