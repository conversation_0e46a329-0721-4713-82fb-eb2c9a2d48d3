[2025-07-17T22:41:26.818+0000] {processor.py:186} INFO - Started process (PID=185) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:26.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:41:26.821+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:26.821+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:26.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:26.918+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:26.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:27.166+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.166+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:27.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:41:27.210+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.398 seconds
[2025-07-17T22:41:58.076+0000] {processor.py:186} INFO - Started process (PID=322) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:58.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:41:58.080+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:58.156+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.156+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:58.164+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:41:58.466+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.466+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:58.480+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.479+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:41:58.504+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.435 seconds
[2025-07-17T22:42:28.716+0000] {processor.py:186} INFO - Started process (PID=453) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:42:28.717+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:42:28.718+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:28.718+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:42:28.976+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:28.976+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:28.983+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:42:29.072+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.071+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:29.083+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.083+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:42:29.102+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.394 seconds
