[2025-07-17T22:21:48.226+0000] {processor.py:186} INFO - Started process (PID=3949) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:21:48.227+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-17T22:21:48.229+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.228+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:21:48.248+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-17T22:21:48.381+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.381+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:21:48.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.395+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-17T22:21:48.416+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.196 seconds
