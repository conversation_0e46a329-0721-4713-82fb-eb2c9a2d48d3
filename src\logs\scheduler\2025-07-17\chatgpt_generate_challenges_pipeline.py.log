[2025-07-17T22:41:30.032+0000] {processor.py:186} INFO - Started process (PID=251) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:41:30.033+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:41:30.036+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:41:30.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.109+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:30.116+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:41:30.334+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.334+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:30.344+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.343+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:41:30.365+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.340 seconds
[2025-07-17T22:42:01.041+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:42:01.042+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:42:01.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:42:01.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.127+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:01.140+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:42:01.399+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.399+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:01.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.408+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:42:01.425+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.391 seconds
[2025-07-17T22:42:31.732+0000] {processor.py:186} INFO - Started process (PID=513) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:42:31.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:42:31.734+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:42:31.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.962+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:31.969+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:42:32.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.063+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:32.074+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.074+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:42:32.093+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.367 seconds
[2025-07-17T22:43:02.221+0000] {processor.py:186} INFO - Started process (PID=644) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:43:02.223+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:43:02.224+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:43:02.298+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.297+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:02.305+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:43:02.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.408+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:02.419+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.419+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:43:02.442+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.227 seconds
[2025-07-17T22:43:32.632+0000] {processor.py:186} INFO - Started process (PID=775) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:43:32.633+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:43:32.634+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.634+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:43:32.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.715+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:32.724+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:43:32.825+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.825+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:32.838+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.837+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:43:32.859+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.233 seconds
[2025-07-17T22:44:03.438+0000] {processor.py:186} INFO - Started process (PID=908) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:44:03.439+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:44:03.440+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.440+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:44:03.519+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.519+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:03.526+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:44:03.620+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.619+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:03.630+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.630+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:44:03.648+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.216 seconds
[2025-07-17T22:44:34.430+0000] {processor.py:186} INFO - Started process (PID=1039) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:44:34.431+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:44:34.432+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.432+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:44:34.505+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.505+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:34.513+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:44:34.608+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.608+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:34.621+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.620+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:44:34.640+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.216 seconds
[2025-07-17T22:45:05.554+0000] {processor.py:186} INFO - Started process (PID=1170) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:45:05.556+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:45:05.557+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.557+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:45:05.629+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.629+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:05.637+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:45:05.733+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.732+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:05.745+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.745+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:45:05.765+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.216 seconds
[2025-07-17T22:45:36.402+0000] {processor.py:186} INFO - Started process (PID=1301) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:45:36.404+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:45:36.405+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.405+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:45:36.489+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.489+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:36.498+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:45:36.613+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.612+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:36.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.628+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:45:36.651+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.257 seconds
[2025-07-17T22:46:07.218+0000] {processor.py:186} INFO - Started process (PID=1432) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:46:07.219+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:46:07.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.220+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:46:07.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.289+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:07.297+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:46:07.403+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.402+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:07.414+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.414+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:46:07.434+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.222 seconds
[2025-07-17T22:46:37.526+0000] {processor.py:186} INFO - Started process (PID=1563) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:46:37.528+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:46:37.529+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.529+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:46:37.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.601+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:37.609+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:46:37.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.715+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:37.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.728+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:46:37.750+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.230 seconds
[2025-07-17T22:47:08.261+0000] {processor.py:186} INFO - Started process (PID=1698) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:47:08.262+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:47:08.263+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.263+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:47:08.327+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.326+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:08.335+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:47:08.426+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:08.435+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.435+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:47:08.455+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.199 seconds
[2025-07-17T22:47:39.350+0000] {processor.py:186} INFO - Started process (PID=1835) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:47:39.351+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:47:39.352+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.352+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:47:39.473+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.473+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:39.481+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:47:39.595+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.594+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:39.610+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.609+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:47:39.629+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.286 seconds
[2025-07-17T22:48:10.272+0000] {processor.py:186} INFO - Started process (PID=1966) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:48:10.273+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:48:10.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.274+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:48:10.349+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.349+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:10.356+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:48:10.455+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.455+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:10.467+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.467+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:48:10.486+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.220 seconds
[2025-07-17T22:48:41.128+0000] {processor.py:186} INFO - Started process (PID=2097) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:48:41.129+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:48:41.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.130+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:48:41.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.209+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:41.219+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:48:41.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.326+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:41.339+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.338+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:48:41.360+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.238 seconds
[2025-07-17T22:49:12.094+0000] {processor.py:186} INFO - Started process (PID=2228) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:49:12.095+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:49:12.096+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.096+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:49:12.184+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.184+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:12.192+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:49:12.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.303+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:12.316+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.316+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:49:12.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.252 seconds
[2025-07-17T22:49:42.867+0000] {processor.py:186} INFO - Started process (PID=2359) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:49:42.868+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:49:42.870+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.869+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:49:42.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.938+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:42.946+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:49:43.031+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:43.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:43.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:43.041+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:49:43.060+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.198 seconds
