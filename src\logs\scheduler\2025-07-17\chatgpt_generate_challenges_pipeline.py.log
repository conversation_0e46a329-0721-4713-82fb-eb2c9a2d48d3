[2025-07-17T21:34:28.002+0000] {processor.py:186} INFO - Started process (PID=188) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:28.003+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:34:28.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:28.116+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.115+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:28.126+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:28.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.341+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:28.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.359+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:34:28.388+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.391 seconds
[2025-07-17T21:34:58.889+0000] {processor.py:186} INFO - Started process (PID=324) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:58.890+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:34:58.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:58.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:58.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:58.976+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:58.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:59.269+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.269+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:59.280+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.280+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:34:59.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.416 seconds
[2025-07-17T21:35:29.839+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:35:29.840+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:35:29.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:29.843+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:35:30.094+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.094+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:30.102+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:35:30.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:30.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.254+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:35:30.273+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.442 seconds
[2025-07-17T21:36:00.480+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:00.480+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:36:00.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:00.569+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.568+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:00.578+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:00.691+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.690+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:00.708+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.707+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:36:00.734+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.260 seconds
[2025-07-17T21:36:30.897+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:30.898+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:36:30.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:30.900+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:30.968+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:30.967+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:30.973+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:31.066+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.065+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.077+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.077+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:36:31.098+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.206 seconds
[2025-07-17T21:37:01.166+0000] {processor.py:186} INFO - Started process (PID=868) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:01.167+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:37:01.170+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.169+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:01.249+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.249+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:01.257+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:01.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:01.381+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.380+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:37:01.401+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.241 seconds
[2025-07-17T21:37:31.993+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:31.994+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:37:31.997+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:31.997+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:32.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.070+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:32.078+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:32.192+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.192+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:32.207+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.207+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:37:32.230+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.243 seconds
[2025-07-17T21:38:02.472+0000] {processor.py:186} INFO - Started process (PID=1140) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:02.473+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:38:02.476+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.475+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:02.555+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.555+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:02.563+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:02.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:02.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.672+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:38:02.695+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.228 seconds
[2025-07-17T21:38:33.008+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:33.009+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:38:33.012+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.012+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:33.096+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.096+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:33.105+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:33.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.204+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:33.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.216+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:38:33.237+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.235 seconds
[2025-07-17T21:39:03.895+0000] {processor.py:186} INFO - Started process (PID=1412) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:03.896+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:39:03.898+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:03.898+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:03.986+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:03.985+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:03.994+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:04.100+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.100+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:04.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.113+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:39:04.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.245 seconds
[2025-07-17T21:39:34.518+0000] {processor.py:186} INFO - Started process (PID=1548) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:34.519+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:39:34.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.521+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:34.589+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.588+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:34.597+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:34.700+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.700+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:34.712+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.711+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:39:34.733+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.221 seconds
[2025-07-17T21:40:05.091+0000] {processor.py:186} INFO - Started process (PID=1684) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:05.092+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:40:05.096+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.095+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:05.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.187+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:05.196+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:05.325+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:05.339+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.339+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:40:05.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.279 seconds
[2025-07-17T21:40:35.949+0000] {processor.py:186} INFO - Started process (PID=1820) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:35.950+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:40:35.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:35.952+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:36.035+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.035+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:36.044+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:36.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.165+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:36.177+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.176+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:40:36.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.259 seconds
[2025-07-17T21:41:07.125+0000] {processor.py:186} INFO - Started process (PID=1956) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:41:07.126+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:41:07.129+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.128+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:41:07.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.219+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:41:07.229+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:41:07.367+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:41:07.382+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.381+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:41:07.412+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.295 seconds
[2025-07-17T21:42:54.880+0000] {processor.py:186} INFO - Started process (PID=188) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:42:54.882+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:42:54.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:54.884+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:42:54.974+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:54.974+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:54.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:42:55.200+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.200+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:55.211+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:42:55.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.359 seconds
[2025-07-17T21:43:25.772+0000] {processor.py:186} INFO - Started process (PID=324) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:25.773+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:43:25.776+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:25.775+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:25.859+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:25.859+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:25.867+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:26.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.142+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:26.152+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.152+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:43:26.170+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.406 seconds
[2025-07-17T21:43:56.769+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:56.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:43:56.772+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:56.772+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:56.999+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:56.999+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:57.007+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:57.130+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.130+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:57.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.140+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:43:57.161+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.398 seconds
[2025-07-17T21:44:27.292+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:27.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:44:27.296+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.296+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:27.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.384+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:27.394+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:27.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.502+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:27.515+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.515+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:44:27.537+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.253 seconds
[2025-07-17T21:44:57.828+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:57.829+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:44:57.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:57.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:57.920+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:57.920+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:57.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:58.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.044+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:58.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.057+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:44:58.079+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.257 seconds
[2025-07-17T21:55:22.745+0000] {processor.py:186} INFO - Started process (PID=189) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:22.746+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:55:22.750+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:22.749+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:22.854+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:22.854+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:22.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:23.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.117+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:23.129+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.129+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:55:23.157+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.421 seconds
[2025-07-17T21:55:53.372+0000] {processor.py:186} INFO - Started process (PID=325) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:53.372+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:55:53.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:53.450+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.449+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:53.457+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:53.727+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.726+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:53.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.736+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:55:53.754+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.388 seconds
[2025-07-17T21:56:24.121+0000] {processor.py:186} INFO - Started process (PID=461) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:24.122+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:56:24.124+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.124+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:24.351+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.351+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:24.360+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:24.450+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.450+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:24.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.459+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:56:24.477+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.364 seconds
[2025-07-17T21:56:54.707+0000] {processor.py:186} INFO - Started process (PID=598) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:54.708+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:56:54.710+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.710+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:54.789+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.789+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:54.796+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:54.904+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.904+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:54.916+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.916+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:56:54.936+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.235 seconds
