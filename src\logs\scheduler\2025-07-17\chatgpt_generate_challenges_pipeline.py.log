[2025-07-17T21:34:28.002+0000] {processor.py:186} INFO - Started process (PID=188) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:28.003+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:34:28.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:28.116+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.115+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:28.126+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:28.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.341+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:28.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.359+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:34:28.388+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.391 seconds
[2025-07-17T21:34:58.889+0000] {processor.py:186} INFO - Started process (PID=324) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:58.890+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:34:58.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:58.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:58.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:58.976+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:58.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:59.269+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.269+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:59.280+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.280+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:34:59.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.416 seconds
[2025-07-17T21:35:29.839+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:35:29.840+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:35:29.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:29.843+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:35:30.094+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.094+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:30.102+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:35:30.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:30.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.254+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:35:30.273+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.442 seconds
[2025-07-17T21:36:00.480+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:00.480+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:36:00.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:00.569+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.568+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:00.578+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:00.691+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.690+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:00.708+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.707+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:36:00.734+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.260 seconds
[2025-07-17T21:36:30.897+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:30.898+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:36:30.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:30.900+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:30.968+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:30.967+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:30.973+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:31.066+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.065+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.077+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.077+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:36:31.098+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.206 seconds
