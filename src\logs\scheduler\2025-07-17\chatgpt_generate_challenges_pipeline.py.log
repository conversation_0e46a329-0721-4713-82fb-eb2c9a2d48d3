[2025-07-17T21:34:28.002+0000] {processor.py:186} INFO - Started process (PID=188) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:28.003+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:34:28.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:28.116+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.115+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:28.126+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:28.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.341+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:28.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.359+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:34:28.388+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.391 seconds
[2025-07-17T21:34:58.889+0000] {processor.py:186} INFO - Started process (PID=324) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:58.890+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:34:58.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:58.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:58.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:58.976+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:58.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:34:59.269+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.269+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:59.280+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.280+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:34:59.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.416 seconds
[2025-07-17T21:35:29.839+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:35:29.840+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:35:29.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:29.843+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:35:30.094+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.094+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:30.102+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:35:30.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:30.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.254+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:35:30.273+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.442 seconds
[2025-07-17T21:36:00.480+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:00.480+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:36:00.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:00.569+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.568+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:00.578+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:00.691+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.690+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:00.708+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.707+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:36:00.734+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.260 seconds
[2025-07-17T21:36:30.897+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:30.898+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:36:30.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:30.900+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:30.968+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:30.967+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:30.973+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:36:31.066+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.065+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.077+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.077+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:36:31.098+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.206 seconds
[2025-07-17T21:37:01.166+0000] {processor.py:186} INFO - Started process (PID=868) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:01.167+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:37:01.170+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.169+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:01.249+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.249+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:01.257+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:01.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:01.381+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.380+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:37:01.401+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.241 seconds
[2025-07-17T21:37:31.993+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:31.994+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:37:31.997+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:31.997+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:32.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.070+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:32.078+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:37:32.192+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.192+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:32.207+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.207+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:37:32.230+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.243 seconds
[2025-07-17T21:38:02.472+0000] {processor.py:186} INFO - Started process (PID=1140) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:02.473+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:38:02.476+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.475+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:02.555+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.555+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:02.563+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:02.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:02.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.672+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:38:02.695+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.228 seconds
[2025-07-17T21:38:33.008+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:33.009+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:38:33.012+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.012+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:33.096+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.096+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:33.105+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:38:33.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.204+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:33.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.216+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:38:33.237+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.235 seconds
[2025-07-17T21:39:03.895+0000] {processor.py:186} INFO - Started process (PID=1412) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:03.896+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:39:03.898+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:03.898+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:03.986+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:03.985+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:03.994+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:04.100+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.100+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:04.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.113+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:39:04.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.245 seconds
[2025-07-17T21:39:34.518+0000] {processor.py:186} INFO - Started process (PID=1548) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:34.519+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:39:34.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.521+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:34.589+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.588+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:34.597+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:39:34.700+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.700+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:34.712+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.711+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:39:34.733+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.221 seconds
[2025-07-17T21:40:05.091+0000] {processor.py:186} INFO - Started process (PID=1684) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:05.092+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:40:05.096+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.095+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:05.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.187+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:05.196+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:05.325+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:05.339+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.339+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:40:05.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.279 seconds
[2025-07-17T21:40:35.949+0000] {processor.py:186} INFO - Started process (PID=1820) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:35.950+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:40:35.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:35.952+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:36.035+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.035+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:36.044+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:40:36.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.165+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:36.177+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.176+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:40:36.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.259 seconds
[2025-07-17T21:41:07.125+0000] {processor.py:186} INFO - Started process (PID=1956) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:41:07.126+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:41:07.129+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.128+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:41:07.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.219+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:41:07.229+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:41:07.367+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:41:07.382+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.381+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:41:07.412+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.295 seconds
[2025-07-17T21:42:54.880+0000] {processor.py:186} INFO - Started process (PID=188) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:42:54.882+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:42:54.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:54.884+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:42:54.974+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:54.974+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:54.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:42:55.200+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.200+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:55.211+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:42:55.233+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.359 seconds
[2025-07-17T21:43:25.772+0000] {processor.py:186} INFO - Started process (PID=324) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:25.773+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:43:25.776+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:25.775+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:25.859+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:25.859+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:25.867+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:26.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.142+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:26.152+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.152+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:43:26.170+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.406 seconds
[2025-07-17T21:43:56.769+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:56.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:43:56.772+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:56.772+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:56.999+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:56.999+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:57.007+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:43:57.130+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.130+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:57.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.140+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:43:57.161+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.398 seconds
[2025-07-17T21:44:27.292+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:27.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:44:27.296+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.296+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:27.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.384+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:27.394+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:27.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.502+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:27.515+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.515+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:44:27.537+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.253 seconds
[2025-07-17T21:44:57.828+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:57.829+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:44:57.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:57.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:57.920+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:57.920+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:57.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:44:58.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.044+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:58.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.057+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:44:58.079+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.257 seconds
[2025-07-17T21:55:22.745+0000] {processor.py:186} INFO - Started process (PID=189) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:22.746+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:55:22.750+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:22.749+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:22.854+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:22.854+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:22.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:23.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.117+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:23.129+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.129+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:55:23.157+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.421 seconds
[2025-07-17T21:55:53.372+0000] {processor.py:186} INFO - Started process (PID=325) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:53.372+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:55:53.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:53.450+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.449+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:53.457+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:55:53.727+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.726+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:53.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.736+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:55:53.754+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.388 seconds
[2025-07-17T21:56:24.121+0000] {processor.py:186} INFO - Started process (PID=461) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:24.122+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:56:24.124+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.124+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:24.351+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.351+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:24.360+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:24.450+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.450+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:24.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.459+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:56:24.477+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.364 seconds
[2025-07-17T21:56:54.707+0000] {processor.py:186} INFO - Started process (PID=598) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:54.708+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:56:54.710+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.710+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:54.789+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.789+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:54.796+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:56:54.904+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.904+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:54.916+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.916+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:56:54.936+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.235 seconds
[2025-07-17T21:57:25.117+0000] {processor.py:186} INFO - Started process (PID=734) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:57:25.118+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:57:25.121+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.120+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:57:25.197+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.197+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:25.206+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:57:25.316+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.315+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:25.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.327+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:57:25.349+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.238 seconds
[2025-07-17T21:57:55.543+0000] {processor.py:186} INFO - Started process (PID=870) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:57:55.544+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:57:55.546+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.546+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:57:55.614+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.614+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:55.620+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:57:55.719+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.719+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:55.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.731+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:57:55.750+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.213 seconds
[2025-07-17T21:58:26.034+0000] {processor.py:186} INFO - Started process (PID=1006) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:58:26.036+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T21:58:26.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.038+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:58:26.118+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.118+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:26.127+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T21:58:26.222+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.222+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:26.235+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.235+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T21:58:26.254+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.226 seconds
[2025-07-17T22:00:34.253+0000] {processor.py:186} INFO - Started process (PID=182) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:00:34.254+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:00:34.257+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:34.256+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:00:34.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:34.336+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:34.348+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:00:34.685+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:34.685+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:34.698+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:34.698+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:00:34.724+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.477 seconds
[2025-07-17T22:01:04.872+0000] {processor.py:186} INFO - Started process (PID=324) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:01:04.873+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:01:04.875+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:04.875+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:01:04.950+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:04.950+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:04.957+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:01:05.192+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:05.192+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:05.201+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:05.201+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:01:05.217+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.352 seconds
[2025-07-17T22:01:35.493+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:01:35.494+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:01:35.497+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:35.497+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:01:35.705+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:35.705+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:35.711+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:01:35.807+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:35.807+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:35.817+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:35.817+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:01:35.832+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.345 seconds
[2025-07-17T22:02:06.406+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:02:06.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:02:06.410+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:06.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:02:06.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:06.483+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:06.493+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:02:06.592+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:06.592+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:06.604+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:06.604+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:02:06.624+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.223 seconds
[2025-07-17T22:02:36.809+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:02:36.810+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:02:36.812+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:36.811+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:02:36.886+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:36.885+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:36.894+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:02:36.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:36.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:37.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.007+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:02:37.026+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.223 seconds
[2025-07-17T22:03:07.109+0000] {processor.py:186} INFO - Started process (PID=868) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:03:07.110+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:03:07.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:07.112+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:03:07.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:07.193+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:07.200+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:03:07.300+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:07.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:07.311+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:07.311+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:03:07.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.228 seconds
[2025-07-17T22:03:37.419+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:03:37.420+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:03:37.422+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:37.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:03:37.506+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:37.506+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:37.517+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:03:37.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:37.626+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:37.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:37.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:03:37.661+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.248 seconds
[2025-07-17T22:04:08.657+0000] {processor.py:186} INFO - Started process (PID=1140) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:04:08.658+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:04:08.661+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:08.660+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:04:08.738+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:08.737+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:08.747+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:04:08.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:08.852+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:08.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:08.864+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:04:08.885+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.235 seconds
[2025-07-17T22:04:39.033+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:04:39.034+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:04:39.037+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.036+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:04:39.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.113+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:39.122+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:04:39.222+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.222+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:39.233+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:04:39.252+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.226 seconds
[2025-07-17T22:05:09.427+0000] {processor.py:186} INFO - Started process (PID=1412) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:05:09.427+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:05:09.430+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:09.429+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:05:09.506+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:09.506+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:09.514+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:05:09.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:09.623+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:09.636+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:09.636+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:05:09.657+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.236 seconds
[2025-07-17T22:05:39.948+0000] {processor.py:186} INFO - Started process (PID=1548) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:05:39.949+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:05:39.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:39.951+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:05:40.045+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.044+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:40.055+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:05:40.186+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.186+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:40.197+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.197+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:05:40.218+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.275 seconds
[2025-07-17T22:06:10.478+0000] {processor.py:186} INFO - Started process (PID=1684) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:06:10.479+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:06:10.482+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:10.481+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:06:10.559+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:10.559+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:10.566+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:06:10.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:10.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:10.671+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:10.671+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:06:10.690+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.217 seconds
[2025-07-17T22:07:26.083+0000] {processor.py:186} INFO - Started process (PID=182) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:07:26.084+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:07:26.086+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:26.086+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:07:26.154+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:26.153+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:26.165+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:07:26.351+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:26.350+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:26.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:26.360+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:07:26.382+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.306 seconds
[2025-07-17T22:07:57.358+0000] {processor.py:186} INFO - Started process (PID=318) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:07:57.359+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:07:57.361+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:57.361+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:07:57.431+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:57.430+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:57.438+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:07:57.674+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:57.674+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:57.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:57.683+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:07:57.703+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.350 seconds
[2025-07-17T22:08:28.154+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:08:28.155+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:08:28.157+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:28.157+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:08:28.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:28.373+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:28.383+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:08:28.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:28.471+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:28.480+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:28.480+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:08:28.502+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.355 seconds
[2025-07-17T22:08:58.957+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:08:58.958+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:08:58.961+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:58.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:08:59.039+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.038+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:59.049+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:08:59.148+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.148+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:59.159+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.159+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:08:59.178+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.228 seconds
[2025-07-17T22:09:29.849+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:09:29.850+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:09:29.853+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:29.853+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:09:29.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:29.937+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:29.945+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:09:30.050+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.049+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:30.062+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.062+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:09:30.083+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.241 seconds
[2025-07-17T22:10:00.399+0000] {processor.py:186} INFO - Started process (PID=868) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:10:00.400+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:10:00.403+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:00.403+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:10:00.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:00.484+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:00.492+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:10:00.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:00.601+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:00.622+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:00.622+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:10:00.653+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.260 seconds
[2025-07-17T22:10:30.788+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:10:30.789+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:10:30.791+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:30.791+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:10:30.866+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:30.866+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:30.874+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:10:30.970+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:30.970+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:30.981+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:30.981+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:10:31.001+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.219 seconds
[2025-07-17T22:11:01.639+0000] {processor.py:186} INFO - Started process (PID=1140) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:11:01.640+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:11:01.642+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:01.642+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:11:01.726+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:01.726+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:01.736+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:11:01.841+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:01.841+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:01.853+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:01.853+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:11:01.877+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.244 seconds
[2025-07-17T22:11:32.667+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:11:32.668+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:11:32.670+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:32.670+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:11:32.744+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:32.743+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:32.751+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:11:32.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:32.852+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:32.864+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:32.864+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:11:32.886+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.225 seconds
[2025-07-17T22:12:03.682+0000] {processor.py:186} INFO - Started process (PID=1412) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:12:03.683+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:12:03.685+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:03.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:12:03.753+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:03.753+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:03.762+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:12:03.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:03.860+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:03.871+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:03.871+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:12:03.890+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.214 seconds
[2025-07-17T22:12:34.790+0000] {processor.py:186} INFO - Started process (PID=1543) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:12:34.791+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-17T22:12:34.793+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:34.793+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:12:34.867+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:34.867+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:34.876+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-17T22:12:34.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:34.975+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:34.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:34.988+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-17T22:12:35.008+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.225 seconds
