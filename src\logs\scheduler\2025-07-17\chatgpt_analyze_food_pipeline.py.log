[2025-07-17T22:41:31.997+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:41:31.998+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:41:32.000+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.000+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:41:32.078+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.078+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:32.087+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:41:32.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:32.346+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.345+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:41:32.368+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.375 seconds
[2025-07-17T22:42:02.785+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:02.786+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:42:02.788+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.788+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:02.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.988+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:02.996+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:03.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.115+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:03.124+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:42:03.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.359 seconds
[2025-07-17T22:42:33.911+0000] {processor.py:186} INFO - Started process (PID=558) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:33.913+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:42:33.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.914+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:34.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.003+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:34.010+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:34.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:34.126+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:42:34.147+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.242 seconds
[2025-07-17T22:43:04.251+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:04.252+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:43:04.254+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.254+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:04.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.337+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:04.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:04.452+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.451+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:04.462+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.462+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:43:04.483+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.240 seconds
[2025-07-17T22:43:35.518+0000] {processor.py:186} INFO - Started process (PID=815) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:35.519+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:43:35.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.520+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:35.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.598+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:35.606+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:35.703+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.703+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:35.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.715+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:43:35.735+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.224 seconds
[2025-07-17T22:44:06.468+0000] {processor.py:186} INFO - Started process (PID=951) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:06.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:44:06.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.471+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:06.546+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.546+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:06.554+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:06.655+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.654+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:06.665+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.665+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:44:06.684+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.223 seconds
[2025-07-17T22:44:37.403+0000] {processor.py:186} INFO - Started process (PID=1082) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:37.404+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:44:37.406+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.406+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:37.487+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.487+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:37.495+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:37.595+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.594+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:37.607+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:44:37.625+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.230 seconds
[2025-07-17T22:45:08.106+0000] {processor.py:186} INFO - Started process (PID=1215) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:08.107+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:45:08.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.113+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:08.184+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.184+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:08.193+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:08.297+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.296+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:08.309+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.309+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:45:08.332+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.232 seconds
[2025-07-17T22:45:39.043+0000] {processor.py:186} INFO - Started process (PID=1344) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:39.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:45:39.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.045+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:39.129+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.129+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:39.139+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:39.257+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.256+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:39.267+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.267+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:45:39.286+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.251 seconds
[2025-07-17T22:46:10.112+0000] {processor.py:186} INFO - Started process (PID=1472) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:10.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:46:10.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.114+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:10.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.187+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:10.195+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:10.291+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.291+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:10.302+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.302+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:46:10.320+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.214 seconds
[2025-07-17T22:46:40.508+0000] {processor.py:186} INFO - Started process (PID=1614) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:40.509+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:46:40.510+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.510+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:40.574+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.574+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:40.583+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:40.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.673+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:40.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.684+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:46:40.704+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.201 seconds
