[2025-07-17T22:41:31.997+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:41:31.998+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:41:32.000+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.000+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:41:32.078+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.078+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:32.087+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:41:32.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:32.346+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.345+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:41:32.368+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.375 seconds
[2025-07-17T22:42:02.785+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:02.786+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:42:02.788+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.788+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:02.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.988+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:02.996+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:03.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.115+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:03.124+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:42:03.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.359 seconds
[2025-07-17T22:42:33.911+0000] {processor.py:186} INFO - Started process (PID=558) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:33.913+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:42:33.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.914+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:34.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.003+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:34.010+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:34.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:34.126+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:42:34.147+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.242 seconds
[2025-07-17T22:43:04.251+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:04.252+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:43:04.254+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.254+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:04.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.337+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:04.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:04.452+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.451+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:04.462+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.462+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:43:04.483+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.240 seconds
[2025-07-17T22:43:35.518+0000] {processor.py:186} INFO - Started process (PID=815) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:35.519+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:43:35.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.520+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:35.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.598+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:35.606+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:43:35.703+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.703+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:35.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.715+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:43:35.735+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.224 seconds
[2025-07-17T22:44:06.468+0000] {processor.py:186} INFO - Started process (PID=951) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:06.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:44:06.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.471+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:06.546+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.546+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:06.554+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:06.655+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.654+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:06.665+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.665+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:44:06.684+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.223 seconds
[2025-07-17T22:44:37.403+0000] {processor.py:186} INFO - Started process (PID=1082) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:37.404+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:44:37.406+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.406+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:37.487+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.487+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:37.495+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:44:37.595+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.594+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:37.607+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:44:37.625+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.230 seconds
[2025-07-17T22:45:08.106+0000] {processor.py:186} INFO - Started process (PID=1215) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:08.107+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:45:08.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.113+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:08.184+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.184+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:08.193+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:08.297+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.296+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:08.309+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.309+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:45:08.332+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.232 seconds
[2025-07-17T22:45:39.043+0000] {processor.py:186} INFO - Started process (PID=1344) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:39.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:45:39.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.045+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:39.129+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.129+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:39.139+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:45:39.257+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.256+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:39.267+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.267+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:45:39.286+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.251 seconds
[2025-07-17T22:46:10.112+0000] {processor.py:186} INFO - Started process (PID=1472) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:10.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:46:10.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.114+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:10.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.187+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:10.195+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:10.291+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.291+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:10.302+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.302+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:46:10.320+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.214 seconds
[2025-07-17T22:46:40.508+0000] {processor.py:186} INFO - Started process (PID=1614) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:40.509+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:46:40.510+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.510+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:40.574+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.574+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:40.583+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:46:40.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.673+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:40.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.684+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:46:40.704+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.201 seconds
[2025-07-17T22:47:11.358+0000] {processor.py:186} INFO - Started process (PID=1740) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:47:11.359+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:47:11.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.360+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:47:11.433+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.433+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:11.442+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:47:11.540+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.539+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:11.551+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.551+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:47:11.569+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.217 seconds
[2025-07-17T22:47:42.360+0000] {processor.py:186} INFO - Started process (PID=1877) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:47:42.361+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:47:42.362+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.362+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:47:42.455+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.455+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:42.463+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:47:42.555+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.555+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:42.567+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.566+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:47:42.585+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.232 seconds
[2025-07-17T22:48:13.387+0000] {processor.py:186} INFO - Started process (PID=2008) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:48:13.388+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:48:13.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.389+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:48:13.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.454+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:13.461+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:48:13.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:13.555+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.555+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:48:13.574+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.193 seconds
[2025-07-17T22:48:44.691+0000] {processor.py:186} INFO - Started process (PID=2139) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:48:44.692+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:48:44.694+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.693+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:48:44.781+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.781+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:44.790+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:48:44.916+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.916+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:44.929+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.929+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:48:44.950+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.268 seconds
[2025-07-17T22:49:15.367+0000] {processor.py:186} INFO - Started process (PID=2268) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:49:15.368+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:49:15.369+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.369+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:49:15.452+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.452+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:15.461+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:49:15.577+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.576+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:15.590+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.590+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:49:15.612+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.252 seconds
[2025-07-17T22:49:46.028+0000] {processor.py:186} INFO - Started process (PID=2406) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:49:46.029+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:49:46.031+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:46.030+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:49:46.097+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:46.097+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:46.105+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:49:46.192+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:46.192+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:46.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:46.204+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:49:46.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.200 seconds
[2025-07-17T22:50:16.599+0000] {processor.py:186} INFO - Started process (PID=2537) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:50:16.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:50:16.602+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:50:16.672+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.672+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:16.680+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:50:16.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.779+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:16.790+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.790+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:50:16.809+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.215 seconds
[2025-07-17T22:50:47.255+0000] {processor.py:186} INFO - Started process (PID=2663) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:50:47.256+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:50:47.257+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.257+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:50:47.344+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.344+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:47.352+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:50:47.452+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.452+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:47.461+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.461+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:50:47.480+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.232 seconds
[2025-07-17T22:51:17.849+0000] {processor.py:186} INFO - Started process (PID=2799) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:51:17.850+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:51:17.851+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.851+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:51:17.913+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.913+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:17.922+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:51:18.011+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:18.011+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:18.021+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:18.020+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:51:18.037+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.193 seconds
[2025-07-17T22:51:48.668+0000] {processor.py:186} INFO - Started process (PID=2925) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:51:48.669+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:51:48.670+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.670+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:51:48.734+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.734+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:48.742+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:51:48.832+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.832+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:48.842+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.842+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:51:48.861+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.200 seconds
[2025-07-17T22:52:19.342+0000] {processor.py:186} INFO - Started process (PID=3056) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:52:19.342+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:52:19.344+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.344+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:52:19.413+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.413+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:19.422+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:52:19.510+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.510+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:19.521+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.520+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:52:19.541+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.205 seconds
