[2025-07-17T21:34:28.431+0000] {processor.py:186} INFO - Started process (PID=196) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:28.432+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:34:28.435+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.435+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:28.539+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.539+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:28.547+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:28.669+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.668+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:28.812+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.812+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:34:28.835+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.411 seconds
[2025-07-17T21:34:59.346+0000] {processor.py:186} INFO - Started process (PID=334) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:59.347+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:34:59.350+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.350+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:59.422+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.422+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:59.431+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:59.671+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.671+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:59.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.680+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:34:59.700+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.360 seconds
[2025-07-17T21:35:30.486+0000] {processor.py:186} INFO - Started process (PID=470) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:35:30.487+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:35:30.490+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.490+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:35:30.566+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.566+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:30.575+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:35:30.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.699+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:30.723+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.723+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:35:30.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.289 seconds
[2025-07-17T21:36:00.922+0000] {processor.py:186} INFO - Started process (PID=606) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:00.923+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:36:00.925+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.925+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:01.000+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.000+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.009+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:01.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:01.120+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.119+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:36:01.140+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.223 seconds
[2025-07-17T21:36:31.241+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:31.242+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:36:31.244+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.244+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:31.309+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.309+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:31.319+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:31.414+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.414+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.424+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.424+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:36:31.442+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.207 seconds
[2025-07-17T21:37:01.599+0000] {processor.py:186} INFO - Started process (PID=878) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:37:01.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:37:01.602+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:37:01.677+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.677+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:01.686+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:37:01.793+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.792+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:01.802+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:01.802+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:37:01.821+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.229 seconds
[2025-07-17T21:37:32.273+0000] {processor.py:186} INFO - Started process (PID=1014) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:37:32.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:37:32.277+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.277+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:37:32.366+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.366+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:32.375+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:37:32.476+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.476+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:32.487+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.487+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:37:32.507+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.239 seconds
[2025-07-17T21:38:02.736+0000] {processor.py:186} INFO - Started process (PID=1150) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:38:02.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:38:02.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.739+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:38:02.813+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.813+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:02.822+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:38:02.928+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.927+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:02.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:02.938+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:38:02.958+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.228 seconds
[2025-07-17T21:38:33.279+0000] {processor.py:186} INFO - Started process (PID=1286) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:38:33.280+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:38:33.283+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.283+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:38:33.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.360+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:33.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:38:33.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.471+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:33.481+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.481+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:38:33.503+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.230 seconds
[2025-07-17T21:39:04.174+0000] {processor.py:186} INFO - Started process (PID=1422) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:39:04.175+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:39:04.177+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.177+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:39:04.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.254+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:04.264+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:39:04.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:04.370+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.370+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:39:04.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.224 seconds
[2025-07-17T21:39:34.770+0000] {processor.py:186} INFO - Started process (PID=1558) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:39:34.771+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:39:34.773+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.773+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:39:34.842+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.842+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:34.851+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:39:34.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.952+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:34.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:34.964+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:39:34.984+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.220 seconds
[2025-07-17T21:40:05.418+0000] {processor.py:186} INFO - Started process (PID=1694) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:40:05.419+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:40:05.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:40:05.511+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.510+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:05.521+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:40:05.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.631+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:05.643+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:05.643+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:40:05.666+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.254 seconds
[2025-07-17T21:40:36.250+0000] {processor.py:186} INFO - Started process (PID=1830) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:40:36.251+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:40:36.254+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.253+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:40:36.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.334+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:36.343+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:40:36.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.459+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:36.470+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.469+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:40:36.493+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.251 seconds
[2025-07-17T21:41:07.557+0000] {processor.py:186} INFO - Started process (PID=1995) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:41:07.560+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:41:07.566+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.565+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:41:07.697+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:07.696+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:41:07.730+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:41:08.022+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:08.021+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:41:08.047+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:08.047+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:41:08.085+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.537 seconds
[2025-07-17T21:42:55.273+0000] {processor.py:186} INFO - Started process (PID=196) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:42:55.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:42:55.277+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.276+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:42:55.361+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.360+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:55.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:42:55.477+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.477+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:55.630+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.630+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:42:55.653+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.386 seconds
[2025-07-17T21:43:26.215+0000] {processor.py:186} INFO - Started process (PID=332) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:43:26.216+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:43:26.218+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.218+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:43:26.307+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.307+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:26.316+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:43:26.614+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.614+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:26.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.624+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:43:26.642+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.433 seconds
[2025-07-17T21:43:57.337+0000] {processor.py:186} INFO - Started process (PID=470) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:43:57.338+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:43:57.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.341+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:43:57.419+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.419+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:57.427+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:43:57.532+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.532+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:57.544+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.543+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:43:57.567+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.236 seconds
[2025-07-17T21:44:27.868+0000] {processor.py:186} INFO - Started process (PID=606) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:44:27.869+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:44:27.871+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.870+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:44:27.948+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:27.948+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:27.958+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:44:28.076+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.076+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:28.090+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.089+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:44:28.112+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.251 seconds
[2025-07-17T21:44:58.438+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:44:58.439+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:44:58.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.441+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:44:58.526+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.525+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:58.538+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:44:58.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:58.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:58.680+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:44:58.709+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.278 seconds
[2025-07-17T21:55:23.202+0000] {processor.py:186} INFO - Started process (PID=197) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:55:23.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:55:23.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:55:23.298+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.298+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:23.306+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:55:23.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.422+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:23.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.583+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:55:23.605+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.410 seconds
[2025-07-17T21:55:53.794+0000] {processor.py:186} INFO - Started process (PID=335) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:55:53.795+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:55:53.797+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.797+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:55:53.864+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:53.863+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:53.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:55:54.086+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:54.095+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.094+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:55:54.111+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.323 seconds
[2025-07-17T21:56:24.645+0000] {processor.py:186} INFO - Started process (PID=471) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:56:24.646+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:56:24.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.649+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:56:24.725+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.724+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:24.733+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:56:24.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.833+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:24.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:24.845+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:56:24.866+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.227 seconds
[2025-07-17T21:56:54.982+0000] {processor.py:186} INFO - Started process (PID=608) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:56:54.983+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:56:54.985+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:54.985+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:56:55.053+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.052+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:55.061+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:56:55.151+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.151+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:55.161+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.160+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:56:55.179+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.202 seconds
[2025-07-17T21:57:25.404+0000] {processor.py:186} INFO - Started process (PID=744) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:57:25.405+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:57:25.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.407+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:57:25.481+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.480+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:25.488+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:57:25.581+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.581+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:25.590+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.590+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:57:25.609+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.212 seconds
[2025-07-17T21:57:55.802+0000] {processor.py:186} INFO - Started process (PID=880) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:57:55.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:57:55.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.805+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:57:55.883+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.883+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:55.892+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:57:55.999+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:55.998+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:56.012+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.012+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:57:56.032+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.236 seconds
[2025-07-17T21:58:26.295+0000] {processor.py:186} INFO - Started process (PID=1016) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:58:26.296+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:58:26.299+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.299+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:58:26.373+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.373+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:26.381+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:58:26.479+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.479+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:26.489+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.489+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:58:26.508+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.219 seconds
[2025-07-17T22:00:34.769+0000] {processor.py:186} INFO - Started process (PID=196) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:00:34.771+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:00:34.773+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:34.773+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:00:34.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:34.865+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:34.873+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:00:34.980+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:34.979+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:35.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.123+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:00:35.145+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.384 seconds
[2025-07-17T22:01:05.279+0000] {processor.py:186} INFO - Started process (PID=332) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:01:05.281+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:01:05.285+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:05.284+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:01:05.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:05.373+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:05.381+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:01:05.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:05.632+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:05.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:05.644+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:01:05.666+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.397 seconds
[2025-07-17T22:01:35.873+0000] {processor.py:186} INFO - Started process (PID=468) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:01:35.874+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:01:35.876+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:35.876+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:01:35.949+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:35.948+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:35.957+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:01:36.054+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:36.053+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:36.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:36.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:01:36.084+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.217 seconds
[2025-07-17T22:02:06.663+0000] {processor.py:186} INFO - Started process (PID=606) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:02:06.663+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:02:06.666+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:06.665+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:02:06.732+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:06.732+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:06.739+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:02:06.825+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:06.825+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:06.835+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:06.835+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:02:06.853+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.196 seconds
[2025-07-17T22:02:37.072+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:02:37.073+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:02:37.075+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.075+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:02:37.139+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.139+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:37.149+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:02:37.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.239+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:37.249+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:02:37.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.201 seconds
[2025-07-17T22:03:07.376+0000] {processor.py:186} INFO - Started process (PID=878) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:03:07.377+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:03:07.379+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:07.379+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:03:07.465+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:07.465+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:07.473+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:03:07.561+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:07.561+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:07.570+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:07.570+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:03:07.587+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.216 seconds
[2025-07-17T22:03:37.717+0000] {processor.py:186} INFO - Started process (PID=1014) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:03:37.718+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:03:37.721+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:37.721+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:03:37.797+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:37.797+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:37.806+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:03:37.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:37.915+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:37.924+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:37.924+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:03:37.950+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.240 seconds
[2025-07-17T22:04:08.924+0000] {processor.py:186} INFO - Started process (PID=1150) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:04:08.925+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:04:08.927+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:08.927+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:04:08.996+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:08.996+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:09.005+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:04:09.111+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:09.121+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.120+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:04:09.141+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.223 seconds
[2025-07-17T22:04:39.297+0000] {processor.py:186} INFO - Started process (PID=1286) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:04:39.298+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:04:39.300+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.300+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:04:39.366+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.365+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:39.372+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:04:39.462+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.462+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:39.472+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.472+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:04:39.490+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.198 seconds
[2025-07-17T22:05:09.696+0000] {processor.py:186} INFO - Started process (PID=1422) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:05:09.697+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:05:09.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:09.699+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:05:09.765+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:09.765+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:09.774+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:05:09.874+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:09.873+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:09.883+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:09.883+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:05:09.904+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.214 seconds
[2025-07-17T22:05:40.259+0000] {processor.py:186} INFO - Started process (PID=1558) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:05:40.261+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:05:40.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.263+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:05:40.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.337+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:40.345+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:05:40.433+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.433+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:40.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.442+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:05:40.460+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.207 seconds
[2025-07-17T22:06:10.725+0000] {processor.py:186} INFO - Started process (PID=1694) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:06:10.726+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:06:10.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:10.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:06:10.798+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:10.798+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:10.805+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:06:10.889+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:10.888+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:10.898+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:10.898+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:06:10.913+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.194 seconds
[2025-07-17T22:07:26.421+0000] {processor.py:186} INFO - Started process (PID=192) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:07:26.422+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:07:26.424+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:26.424+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:07:26.491+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:26.491+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:26.500+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:07:26.614+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:26.614+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:26.796+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:26.796+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:07:26.819+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.404 seconds
[2025-07-17T22:07:57.737+0000] {processor.py:186} INFO - Started process (PID=334) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:07:57.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:07:57.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:57.739+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:07:57.804+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:57.804+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:57.812+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:07:58.053+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:58.052+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:58.061+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:58.061+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:07:58.076+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.345 seconds
[2025-07-17T22:08:28.718+0000] {processor.py:186} INFO - Started process (PID=470) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:08:28.718+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:08:28.721+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:28.721+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:08:28.790+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:28.790+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:28.798+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:08:28.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:28.923+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:28.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:28.933+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:08:28.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.242 seconds
[2025-07-17T22:08:59.214+0000] {processor.py:186} INFO - Started process (PID=606) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:08:59.214+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:08:59.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.216+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:08:59.280+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.280+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:59.289+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:08:59.379+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.378+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:59.388+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.388+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:08:59.403+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.195 seconds
[2025-07-17T22:09:30.123+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:09:30.123+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:09:30.126+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.126+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:09:30.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.199+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:30.208+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:09:30.304+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.304+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:30.315+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.315+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:09:30.335+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.218 seconds
[2025-07-17T22:10:00.696+0000] {processor.py:186} INFO - Started process (PID=876) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:10:00.697+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:10:00.700+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:00.700+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:10:00.771+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:00.771+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:00.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:10:00.873+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:00.872+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:00.886+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:00.886+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:10:00.904+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.214 seconds
[2025-07-17T22:10:31.046+0000] {processor.py:186} INFO - Started process (PID=1014) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:10:31.047+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:10:31.049+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.049+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:10:31.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.113+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:31.121+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:10:31.214+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.213+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:31.223+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.223+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:10:31.240+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.200 seconds
[2025-07-17T22:11:01.919+0000] {processor.py:186} INFO - Started process (PID=1150) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:11:01.920+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:11:01.922+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:01.922+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:11:01.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:01.988+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:01.994+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:11:02.086+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:02.098+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.098+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:11:02.121+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.207 seconds
[2025-07-17T22:11:32.924+0000] {processor.py:186} INFO - Started process (PID=1286) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:11:32.925+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:11:32.927+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:32.927+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:11:32.996+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:32.996+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:33.003+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:11:33.094+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.094+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:33.105+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.105+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:11:33.129+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.211 seconds
[2025-07-17T22:12:03.928+0000] {processor.py:186} INFO - Started process (PID=1422) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:12:03.929+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:12:03.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:03.930+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:12:04.000+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.000+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:04.010+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:12:04.096+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.096+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:04.106+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.106+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:12:04.122+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.200 seconds
[2025-07-17T22:12:35.043+0000] {processor.py:186} INFO - Started process (PID=1553) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:12:35.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:12:35.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:35.045+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:12:35.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:35.119+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:35.129+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:12:35.222+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:35.222+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:35.232+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:35.231+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:12:35.250+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.212 seconds
