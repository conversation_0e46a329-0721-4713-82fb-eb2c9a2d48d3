[2025-07-17T22:41:31.997+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:41:31.998+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:41:32.000+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.000+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:41:32.078+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.078+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:32.087+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:41:32.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:32.346+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.345+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:41:32.368+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.375 seconds
[2025-07-17T22:42:02.785+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:02.786+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:42:02.788+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.788+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:02.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.988+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:02.996+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:03.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.115+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:03.124+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:42:03.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.359 seconds
[2025-07-17T22:42:33.911+0000] {processor.py:186} INFO - Started process (PID=558) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:33.913+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T22:42:33.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.914+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:34.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.003+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:34.010+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T22:42:34.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:34.126+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T22:42:34.147+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.242 seconds
