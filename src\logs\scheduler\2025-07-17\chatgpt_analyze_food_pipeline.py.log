[2025-07-17T21:34:28.431+0000] {processor.py:186} INFO - Started process (PID=196) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:28.432+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:34:28.435+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.435+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:28.539+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.539+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:28.547+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:28.669+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.668+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:28.812+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:28.812+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:34:28.835+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.411 seconds
[2025-07-17T21:34:59.346+0000] {processor.py:186} INFO - Started process (PID=334) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:59.347+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:34:59.350+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.350+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:59.422+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.422+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:59.431+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:34:59.671+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.671+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:59.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:59.680+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:34:59.700+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.360 seconds
[2025-07-17T21:35:30.486+0000] {processor.py:186} INFO - Started process (PID=470) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:35:30.487+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:35:30.490+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.490+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:35:30.566+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.566+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:30.575+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:35:30.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.699+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:30.723+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:30.723+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:35:30.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.289 seconds
[2025-07-17T21:36:00.922+0000] {processor.py:186} INFO - Started process (PID=606) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:00.923+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:36:00.925+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:00.925+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:01.000+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.000+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.009+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:01.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:01.120+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.119+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:36:01.140+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.223 seconds
[2025-07-17T21:36:31.241+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:31.242+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-17T21:36:31.244+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.244+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:31.309+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.309+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:31.319+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-17T21:36:31.414+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.414+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.424+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.424+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-17T21:36:31.442+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.207 seconds
