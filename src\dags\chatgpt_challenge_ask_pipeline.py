from datetime import datetime
# from pydantic import BaseModel
import json
from airflow.decorators import dag, task
import configparser
import requests
import tiktoken

# Читаем конфиг и извлекаем токен
config = configparser.ConfigParser()
config.read("/opt/airflow/pipe/config.ini")
API_TOKEN_GPT = config.get('TOKENS', 'API_TOKEN_GPT')

default_args = {
    'owner': '<PERSON><PERSON>',
    'retries': 0,
}

@dag(dag_id='chatgpt_challenge_ask_pipeline',
     default_args=default_args,
     start_date=datetime.now(),
     catchup=False,
     schedule_interval=None)
def chatgpt():
    @task()
    def generate_openai_response(**kwargs):
        conf = kwargs.get('dag_run').conf
        if not conf or "prompt" not in conf:
            raise ValueError("Не найден промпт в конфигурации DAG.")
        prompt = conf["prompt"]

        belgium_proxies = {
            "http": "http://**************:8888",
            "https": "http://**************:8888"
        }
        germany_proxies = {
            "http": "http://**************:8888",
            "https": "http://**************:8888"
        }

        headers = {
            "Authorization": f"Bearer {API_TOKEN_GPT}",
            "Content-Type": "application/json"
        }

        data = {
            "model": "gpt-4o",
            "temperature": 0.75,
            "messages": [{"role": "system", "content": prompt}]
        }

        url = "https://api.openai.com/v1/chat/completions"
        max_retries = 2
        response = None
        for attempt in range(max_retries):
            try:
                response = requests.post(url, json=data, headers=headers, proxies=germany_proxies, timeout=90)
                response.raise_for_status()
                break
            except requests.exceptions.Timeout:
                if attempt == max_retries - 1:
                    raise
        if response is None:
            raise Exception("No response received after retries")
        # response = requests.post(url, json=data, headers=headers)

        content = response.json()["choices"][0]["message"]["content"]

        # Мониторинг затрат OpenAI
        try:
        
            response_data = response.json()

            cost_analysis = track_openai_cost(
                response_data=response_data,
                context={
                    "dag_id": "chatgpt_challenge_ask_pipeline",
                    "task_id": "generate_openai_response"
                }
            )

            print(f"💰 Стоимость операции: ${cost_analysis.get('total_cost', 0):.6f}")
            print(f"🔢 Токены: {cost_analysis.get('total_tokens', 0)}")

        except Exception as e:
            print(f"⚠️ Ошибка отслеживания затрат: {e}")

        # Просто раскомментируем существующую функцию и добавим сохранение в Redis
        try:
            def analyze_token_usage(input_prompt: str, output_response: str, cost_rate_input: float = 0.03,
                                    cost_rate_output: float = 0.06, tokens_unit: int = 1000, model: str = "gpt-4o"):
                encoder = tiktoken.encoding_for_model(model)
                tokens_input = len(encoder.encode(input_prompt))
                tokens_output = len(encoder.encode(output_response))
                cost_input = (tokens_input / tokens_unit) * cost_rate_input
                cost_output = (tokens_output / tokens_unit) * cost_rate_output

                # Сохраняем в Redis для мониторинга
                try:
                    import redis
                    import json
                    from datetime import datetime
from cost_tracking import track_openai_cost

                    r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                    cost_data = {
                        "dag_id": "chatgpt_challenge_ask_pipeline",
                        "tokens_input": tokens_input,
                        "tokens_output": tokens_output,
                        "cost_input": cost_input,
                        "cost_output": cost_output,
                        "total_cost": cost_input + cost_output,
                        "timestamp": datetime.now().isoformat(),
                        "model": model
                    }

                    # Сохраняем с TTL 30 дней
                    key = f"cost_tracking:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    r.setex(key, 2592000, json.dumps(cost_data))

                except Exception as redis_error:
                    print(f"Redis сохранение не удалось: {redis_error}")

                print(f"Input tokens: {tokens_input}, Output tokens: {tokens_output}")
                print(f"Cost for input tokens: ${cost_input:.4f}, Cost for output tokens: ${cost_output:.4f}")
                print(f"💰 Total cost: ${cost_input + cost_output:.6f}")

                return {
                    "tokens_input": tokens_input,
                    "tokens_output": tokens_output,
                    "cost_input": cost_input,
                    "cost_output": cost_output,
                    "total_cost": cost_input + cost_output
                }

            stats = analyze_token_usage(prompt, content)

        except Exception as e:
            print(f"⚠️ Ошибка анализа затрат: {e}")

        return content.strip('"')

    @task()
    def get_json_metrics(response):
        response = response.strip()
        start = response.find('{')
        end = response.rfind('}')

        if start != -1 and end != -1:
            json_string = response[start:end + 1].strip()
            metrics = json_string.replace("True", "true").replace("False", "false").replace("None", "null").replace("[]", "null").replace("'", '"')
            try:
                return metrics
            except json.JSONDecodeError:
                raise ValueError("Failed to parse JSON from cleaned string")
        else:
            try:
                response_cleaned = response.strip()
                metrics = json.loads(response_cleaned)
                return metrics
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON format in response")

    response = generate_openai_response(provide_context=True)
    get_json_metrics(response)

dag_chatgpt = chatgpt()