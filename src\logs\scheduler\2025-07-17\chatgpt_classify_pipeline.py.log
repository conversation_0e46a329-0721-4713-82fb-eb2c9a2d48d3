[2025-07-17T21:34:31.755+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:34:31.756+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:34:31.759+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:34:31.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.835+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:31.846+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:34:32.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.119+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.129+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.129+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:34:32.156+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.408 seconds
[2025-07-17T21:35:03.075+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:03.076+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:35:03.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:03.320+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.320+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:03.326+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:03.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.427+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.426+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:35:03.445+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.376 seconds
[2025-07-17T21:35:33.699+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:33.700+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:35:33.702+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.702+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:33.791+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.790+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:33.799+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:33.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.917+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:33.929+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.929+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:35:33.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.262 seconds
[2025-07-17T21:36:04.292+0000] {processor.py:186} INFO - Started process (PID=686) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:04.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:36:04.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.295+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:04.373+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.373+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:04.380+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:04.476+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.476+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:04.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.486+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:36:04.507+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.221 seconds
[2025-07-17T21:36:34.714+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:34.715+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:36:34.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.717+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:34.790+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.789+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.798+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:34.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.892+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.902+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.902+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:36:34.919+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.210 seconds
[2025-07-17T21:37:05.281+0000] {processor.py:186} INFO - Started process (PID=956) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:05.282+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:37:05.285+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.285+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:05.361+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.361+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:05.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:05.472+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.472+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:05.482+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.482+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:37:05.500+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.226 seconds
[2025-07-17T21:37:35.800+0000] {processor.py:186} INFO - Started process (PID=1092) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:35.801+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:37:35.804+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.804+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:35.882+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.881+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.889+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:36.009+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:36.009+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:36.019+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:36.019+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:37:36.039+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.245 seconds
[2025-07-17T21:38:06.403+0000] {processor.py:186} INFO - Started process (PID=1228) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:06.404+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:38:06.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.408+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:06.491+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.491+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:06.499+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:06.616+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.615+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.628+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:38:06.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.255 seconds
[2025-07-17T21:38:37.264+0000] {processor.py:186} INFO - Started process (PID=1366) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:37.265+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:38:37.267+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.267+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:37.343+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.343+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:37.351+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:37.448+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.448+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:37.458+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.458+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:38:37.479+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.221 seconds
[2025-07-17T21:39:07.703+0000] {processor.py:186} INFO - Started process (PID=1502) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:07.704+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:39:07.707+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.706+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:07.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.779+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:07.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:07.887+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.887+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.897+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.897+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:39:07.917+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.220 seconds
[2025-07-17T21:39:38.197+0000] {processor.py:186} INFO - Started process (PID=1636) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:38.198+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:39:38.201+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.201+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:38.281+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.280+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:38.290+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:38.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.417+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:38.430+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.430+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:39:38.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.257 seconds
[2025-07-17T21:40:09.119+0000] {processor.py:186} INFO - Started process (PID=1772) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:09.120+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:40:09.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.123+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:09.205+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.204+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:09.215+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:09.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.336+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:09.348+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.347+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:40:09.367+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.255 seconds
[2025-07-17T21:40:40.202+0000] {processor.py:186} INFO - Started process (PID=1908) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:40.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:40:40.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:40.291+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.291+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:40.299+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:40.410+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:40.420+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.420+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:40:40.440+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.244 seconds
[2025-07-17T21:42:58.598+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:42:58.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:42:58.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:42:58.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.698+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:58.711+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:42:58.976+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.976+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:58.987+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.987+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:42:59.009+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.419 seconds
[2025-07-17T21:43:29.530+0000] {processor.py:186} INFO - Started process (PID=418) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:43:29.531+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:43:29.535+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.534+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:43:29.767+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.767+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:29.773+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:43:29.875+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.874+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:29.887+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.887+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:43:29.906+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.382 seconds
[2025-07-17T21:44:00.017+0000] {processor.py:186} INFO - Started process (PID=554) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:00.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:44:00.022+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:00.111+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.111+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:00.120+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:00.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.237+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:00.248+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.248+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:44:00.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.257 seconds
[2025-07-17T21:44:30.875+0000] {processor.py:186} INFO - Started process (PID=690) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:30.876+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:44:30.878+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.878+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:30.959+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.958+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.967+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:31.073+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.072+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:31.084+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.084+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:44:31.110+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.241 seconds
[2025-07-17T21:45:02.015+0000] {processor.py:186} INFO - Started process (PID=826) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:45:02.016+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:45:02.019+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.019+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:45:02.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.110+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:02.116+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:45:02.225+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.225+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:02.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.236+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:45:02.258+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.249 seconds
[2025-07-17T21:55:26.369+0000] {processor.py:186} INFO - Started process (PID=277) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:26.370+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:55:26.372+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.372+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:26.440+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.439+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:26.447+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:26.718+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.718+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:26.729+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.729+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:55:26.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.383 seconds
[2025-07-17T21:55:57.067+0000] {processor.py:186} INFO - Started process (PID=419) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:57.068+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:55:57.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.070+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:57.312+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.312+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:57.318+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:57.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.408+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:57.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.417+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:55:57.433+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.372 seconds
[2025-07-17T21:56:27.812+0000] {processor.py:186} INFO - Started process (PID=558) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:27.813+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:56:27.816+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.815+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:27.888+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.888+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:27.895+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:27.997+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.996+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:28.008+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.008+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:56:28.030+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.223 seconds
[2025-07-17T21:56:58.194+0000] {processor.py:186} INFO - Started process (PID=694) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:58.195+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:56:58.198+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.197+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:58.267+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.267+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:58.274+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:58.369+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:58.381+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.380+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:56:58.397+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.208 seconds
[2025-07-17T21:57:28.780+0000] {processor.py:186} INFO - Started process (PID=828) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:28.781+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:57:28.783+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.783+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:28.851+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.850+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:28.859+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:28.956+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.956+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:28.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.965+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:57:28.982+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.208 seconds
[2025-07-17T21:57:59.410+0000] {processor.py:186} INFO - Started process (PID=964) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:59.411+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:57:59.414+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.413+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:59.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.486+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:59.495+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:59.602+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.602+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:59.613+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.613+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:57:59.631+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.228 seconds
[2025-07-17T21:58:29.765+0000] {processor.py:186} INFO - Started process (PID=1100) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:58:29.767+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:58:29.769+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.769+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:58:29.842+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.842+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:29.851+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:58:29.987+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.987+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:29.996+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.996+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:58:30.016+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.256 seconds
