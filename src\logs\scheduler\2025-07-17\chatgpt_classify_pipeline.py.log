[2025-07-17T22:41:30.399+0000] {processor.py:186} INFO - Started process (PID=258) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:41:30.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:41:30.401+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.401+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:41:30.470+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.469+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:30.479+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:41:30.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.699+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:30.710+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.710+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:41:30.735+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.341 seconds
[2025-07-17T22:42:01.443+0000] {processor.py:186} INFO - Started process (PID=389) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:01.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:42:01.445+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:01.519+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.519+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:01.528+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:01.764+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.764+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:01.776+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.776+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:42:01.796+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.358 seconds
[2025-07-17T22:42:32.140+0000] {processor.py:186} INFO - Started process (PID=520) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:32.141+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:42:32.144+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.143+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:32.396+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.396+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:32.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:32.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.494+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:32.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.502+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:42:32.516+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.383 seconds
[2025-07-17T22:43:03.247+0000] {processor.py:186} INFO - Started process (PID=651) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:43:03.248+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:43:03.249+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.249+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:43:03.319+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.318+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:03.326+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:43:03.418+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.418+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:03.429+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:43:03.449+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.209 seconds
[2025-07-17T22:43:33.663+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:43:33.664+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:43:33.665+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:33.665+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:43:33.742+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:33.742+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:33.752+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:43:33.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:33.859+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:33.872+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:33.872+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:43:33.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.242 seconds
[2025-07-17T22:44:04.470+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:44:04.472+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:44:04.474+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:04.474+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:44:04.596+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:04.596+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:04.603+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:44:04.701+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:04.701+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:04.712+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:04.712+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:44:04.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.267 seconds
[2025-07-17T22:44:35.475+0000] {processor.py:186} INFO - Started process (PID=1044) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:44:35.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:44:35.477+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:35.477+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:44:35.574+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:35.574+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:35.581+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:44:35.676+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:35.676+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:35.687+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:35.687+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:44:35.704+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.235 seconds
[2025-07-17T22:45:05.816+0000] {processor.py:186} INFO - Started process (PID=1175) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:45:05.817+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:45:05.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:45:05.887+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.886+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:05.895+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:45:06.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:06.006+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:06.024+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:06.023+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:45:06.052+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.242 seconds
[2025-07-17T22:45:36.407+0000] {processor.py:186} INFO - Started process (PID=1304) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:45:36.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:45:36.410+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:45:36.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.494+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:36.502+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:45:36.618+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.617+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:36.634+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.633+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:45:36.656+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.255 seconds
[2025-07-17T22:46:07.226+0000] {processor.py:186} INFO - Started process (PID=1435) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:46:07.226+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:46:07.228+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.227+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:46:07.304+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.304+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:07.313+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:46:07.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.423+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:07.436+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.435+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:46:07.454+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.235 seconds
[2025-07-17T22:46:37.535+0000] {processor.py:186} INFO - Started process (PID=1566) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:46:37.536+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:46:37.537+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.537+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:46:37.610+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.610+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:37.619+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:46:37.724+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.724+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:37.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.736+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:46:37.755+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.228 seconds
[2025-07-17T22:47:08.495+0000] {processor.py:186} INFO - Started process (PID=1705) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:47:08.496+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:47:08.498+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.497+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:47:08.572+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.572+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:08.581+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:47:08.672+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.672+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:08.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.683+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:47:08.703+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.215 seconds
[2025-07-17T22:47:39.634+0000] {processor.py:186} INFO - Started process (PID=1840) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:47:39.635+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:47:39.636+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:47:39.725+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.725+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:39.734+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:47:39.840+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.840+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:39.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.852+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:47:39.873+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.246 seconds
[2025-07-17T22:48:10.524+0000] {processor.py:186} INFO - Started process (PID=1973) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:48:10.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:48:10.527+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.527+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:48:10.607+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.607+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:10.614+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:48:10.710+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.709+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:10.720+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:48:10.740+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.222 seconds
[2025-07-17T22:48:41.359+0000] {processor.py:186} INFO - Started process (PID=2102) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:48:41.360+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:48:41.362+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.362+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:48:41.437+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.437+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:41.444+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:48:41.553+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.552+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:41.568+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.567+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:48:41.591+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.238 seconds
[2025-07-17T22:49:12.378+0000] {processor.py:186} INFO - Started process (PID=2235) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:49:12.379+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:49:12.380+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.380+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:49:12.465+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.465+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:12.472+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:49:12.575+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.575+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:12.586+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.586+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:49:12.606+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.234 seconds
[2025-07-17T22:49:43.098+0000] {processor.py:186} INFO - Started process (PID=2366) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:49:43.099+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:49:43.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:43.100+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:49:43.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:43.165+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:43.172+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:49:43.262+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:43.262+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:43.273+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:43.273+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:49:43.293+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.199 seconds
[2025-07-17T22:50:13.894+0000] {processor.py:186} INFO - Started process (PID=2495) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:50:13.895+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:50:13.896+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:13.895+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:50:13.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:13.975+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:13.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:50:14.077+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:14.076+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:14.087+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:14.087+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:50:14.106+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.218 seconds
[2025-07-17T22:50:44.601+0000] {processor.py:186} INFO - Started process (PID=2628) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:50:44.602+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:50:44.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:44.603+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:50:44.682+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:44.682+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:44.688+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:50:44.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:44.792+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:44.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:44.805+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:50:44.822+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.227 seconds
[2025-07-17T22:51:15.135+0000] {processor.py:186} INFO - Started process (PID=2757) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:51:15.136+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:51:15.138+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:15.137+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:51:15.208+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:15.208+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:15.216+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:51:15.316+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:15.316+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:15.329+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:15.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:51:15.349+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.220 seconds
[2025-07-17T22:51:45.804+0000] {processor.py:186} INFO - Started process (PID=2890) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:51:45.805+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:51:45.806+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:45.806+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:51:45.872+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:45.872+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:45.879+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:51:45.964+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:45.963+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:45.978+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:45.978+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:51:45.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.196 seconds
[2025-07-17T22:52:16.456+0000] {processor.py:186} INFO - Started process (PID=3021) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:52:16.457+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:52:16.458+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:16.458+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:52:16.529+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:16.529+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:16.536+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:52:16.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:16.627+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:16.638+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:16.638+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:52:16.655+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.205 seconds
[2025-07-17T22:52:47.165+0000] {processor.py:186} INFO - Started process (PID=3152) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:52:47.166+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:52:47.167+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:47.167+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:52:47.232+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:47.232+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:47.238+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:52:47.333+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:47.333+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:47.343+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:47.343+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:52:47.361+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.201 seconds
[2025-07-17T22:53:17.820+0000] {processor.py:186} INFO - Started process (PID=3289) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:53:17.821+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:53:17.822+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:17.822+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:53:17.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:17.892+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:17.899+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:53:17.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:17.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:18.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:18.005+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:53:18.022+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.208 seconds
[2025-07-17T22:53:48.734+0000] {processor.py:186} INFO - Started process (PID=3420) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:53:48.735+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:53:48.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:48.736+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:53:48.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:48.805+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:48.813+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:53:48.910+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:48.909+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:48.919+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:48.919+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:53:48.939+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.211 seconds
