[2025-07-17T22:21:49.730+0000] {processor.py:186} INFO - Started process (PID=3984) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:21:49.731+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:21:49.733+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:49.733+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:21:49.802+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:49.802+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:21:49.812+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:21:49.916+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:49.915+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:21:49.929+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:49.928+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:21:49.950+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.225 seconds
