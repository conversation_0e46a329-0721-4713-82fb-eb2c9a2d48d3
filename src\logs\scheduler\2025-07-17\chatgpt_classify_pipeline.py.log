[2025-07-17T21:34:31.755+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:34:31.756+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:34:31.759+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:34:31.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.835+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:31.846+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:34:32.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.119+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.129+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.129+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:34:32.156+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.408 seconds
[2025-07-17T21:35:03.075+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:03.076+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:35:03.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:03.320+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.320+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:03.326+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:03.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.427+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.426+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:35:03.445+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.376 seconds
[2025-07-17T21:35:33.699+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:33.700+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:35:33.702+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.702+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:33.791+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.790+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:33.799+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:35:33.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.917+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:33.929+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.929+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:35:33.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.262 seconds
[2025-07-17T21:36:04.292+0000] {processor.py:186} INFO - Started process (PID=686) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:04.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:36:04.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.295+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:04.373+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.373+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:04.380+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:04.476+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.476+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:04.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.486+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:36:04.507+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.221 seconds
[2025-07-17T21:36:34.714+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:34.715+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:36:34.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.717+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:34.790+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.789+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.798+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:36:34.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.892+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.902+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.902+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:36:34.919+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.210 seconds
[2025-07-17T21:37:05.281+0000] {processor.py:186} INFO - Started process (PID=956) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:05.282+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:37:05.285+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.285+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:05.361+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.361+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:05.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:05.472+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.472+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:05.482+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.482+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:37:05.500+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.226 seconds
[2025-07-17T21:37:35.800+0000] {processor.py:186} INFO - Started process (PID=1092) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:35.801+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:37:35.804+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.804+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:35.882+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.881+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.889+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:37:36.009+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:36.009+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:36.019+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:36.019+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:37:36.039+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.245 seconds
[2025-07-17T21:38:06.403+0000] {processor.py:186} INFO - Started process (PID=1228) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:06.404+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:38:06.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.408+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:06.491+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.491+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:06.499+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:06.616+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.615+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.628+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:38:06.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.255 seconds
[2025-07-17T21:38:37.264+0000] {processor.py:186} INFO - Started process (PID=1366) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:37.265+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:38:37.267+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.267+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:37.343+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.343+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:37.351+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:38:37.448+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.448+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:37.458+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.458+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:38:37.479+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.221 seconds
[2025-07-17T21:39:07.703+0000] {processor.py:186} INFO - Started process (PID=1502) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:07.704+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:39:07.707+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.706+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:07.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.779+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:07.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:07.887+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.887+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.897+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.897+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:39:07.917+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.220 seconds
[2025-07-17T21:39:38.197+0000] {processor.py:186} INFO - Started process (PID=1636) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:38.198+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:39:38.201+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.201+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:38.281+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.280+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:38.290+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:39:38.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.417+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:38.430+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.430+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:39:38.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.257 seconds
[2025-07-17T21:40:09.119+0000] {processor.py:186} INFO - Started process (PID=1772) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:09.120+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:40:09.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.123+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:09.205+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.204+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:09.215+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:09.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.336+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:09.348+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.347+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:40:09.367+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.255 seconds
[2025-07-17T21:40:40.202+0000] {processor.py:186} INFO - Started process (PID=1908) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:40.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:40:40.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:40.291+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.291+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:40.299+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:40:40.410+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:40.420+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.420+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:40:40.440+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.244 seconds
[2025-07-17T21:42:58.598+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:42:58.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:42:58.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:42:58.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.698+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:58.711+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:42:58.976+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.976+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:58.987+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.987+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:42:59.009+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.419 seconds
[2025-07-17T21:43:29.530+0000] {processor.py:186} INFO - Started process (PID=418) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:43:29.531+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:43:29.535+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.534+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:43:29.767+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.767+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:29.773+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:43:29.875+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.874+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:29.887+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.887+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:43:29.906+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.382 seconds
[2025-07-17T21:44:00.017+0000] {processor.py:186} INFO - Started process (PID=554) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:00.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:44:00.022+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:00.111+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.111+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:00.120+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:00.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.237+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:00.248+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.248+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:44:00.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.257 seconds
[2025-07-17T21:44:30.875+0000] {processor.py:186} INFO - Started process (PID=690) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:30.876+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:44:30.878+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.878+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:30.959+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.958+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.967+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:44:31.073+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.072+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:31.084+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.084+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:44:31.110+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.241 seconds
[2025-07-17T21:45:02.015+0000] {processor.py:186} INFO - Started process (PID=826) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:45:02.016+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:45:02.019+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.019+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:45:02.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.110+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:02.116+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:45:02.225+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.225+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:02.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.236+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:45:02.258+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.249 seconds
[2025-07-17T21:55:26.369+0000] {processor.py:186} INFO - Started process (PID=277) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:26.370+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:55:26.372+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.372+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:26.440+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.439+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:26.447+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:26.718+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.718+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:26.729+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.729+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:55:26.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.383 seconds
[2025-07-17T21:55:57.067+0000] {processor.py:186} INFO - Started process (PID=419) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:57.068+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:55:57.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.070+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:57.312+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.312+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:57.318+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:55:57.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.408+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:57.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.417+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:55:57.433+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.372 seconds
[2025-07-17T21:56:27.812+0000] {processor.py:186} INFO - Started process (PID=558) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:27.813+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:56:27.816+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.815+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:27.888+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.888+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:27.895+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:27.997+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.996+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:28.008+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.008+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:56:28.030+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.223 seconds
[2025-07-17T21:56:58.194+0000] {processor.py:186} INFO - Started process (PID=694) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:58.195+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:56:58.198+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.197+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:58.267+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.267+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:58.274+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:56:58.369+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:58.381+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.380+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:56:58.397+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.208 seconds
[2025-07-17T21:57:28.780+0000] {processor.py:186} INFO - Started process (PID=828) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:28.781+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:57:28.783+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.783+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:28.851+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.850+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:28.859+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:28.956+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.956+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:28.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.965+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:57:28.982+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.208 seconds
[2025-07-17T21:57:59.410+0000] {processor.py:186} INFO - Started process (PID=964) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:59.411+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:57:59.414+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.413+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:59.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.486+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:59.495+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:57:59.602+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.602+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:59.613+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.613+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:57:59.631+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.228 seconds
[2025-07-17T21:58:29.765+0000] {processor.py:186} INFO - Started process (PID=1100) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:58:29.767+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T21:58:29.769+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.769+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:58:29.842+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.842+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:29.851+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T21:58:29.987+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.987+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:29.996+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.996+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T21:58:30.016+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.256 seconds
[2025-07-17T22:00:37.921+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:00:37.923+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:00:37.925+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.924+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:00:37.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.994+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:38.000+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:00:38.252+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.252+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:38.261+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.261+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:00:38.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.364 seconds
[2025-07-17T22:01:08.846+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:01:08.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:01:08.849+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.849+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:01:09.088+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.088+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:09.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:01:09.197+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.197+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:09.208+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.208+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:01:09.229+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.390 seconds
[2025-07-17T22:01:39.516+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:01:39.517+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:01:39.519+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.519+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:01:39.587+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.587+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:39.597+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:01:39.696+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.696+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:39.707+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.707+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:01:39.723+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.213 seconds
[2025-07-17T22:02:09.986+0000] {processor.py:186} INFO - Started process (PID=684) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:02:09.987+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:02:09.989+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.989+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:02:10.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.056+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:10.064+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:02:10.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.165+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:10.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.174+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:02:10.192+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.211 seconds
[2025-07-17T22:02:40.366+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:02:40.367+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:02:40.370+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.370+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:02:40.447+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.447+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:40.454+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:02:40.542+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.542+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:40.554+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.554+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:02:40.569+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.208 seconds
[2025-07-17T22:03:10.914+0000] {processor.py:186} INFO - Started process (PID=956) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:03:10.915+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:03:10.917+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.917+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:03:10.986+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.986+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:10.993+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:03:11.088+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:11.088+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:11.099+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:11.098+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:03:11.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.208 seconds
[2025-07-17T22:03:41.330+0000] {processor.py:186} INFO - Started process (PID=1092) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:03:41.331+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:03:41.334+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:41.333+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:03:41.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:41.459+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:41.715+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:03:42.073+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:42.072+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:42.140+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:42.140+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:03:42.203+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.879 seconds
[2025-07-17T22:04:13.081+0000] {processor.py:186} INFO - Started process (PID=1230) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:04:13.082+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:04:13.085+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.084+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:04:13.150+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.150+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:13.159+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:04:13.248+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.248+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:13.258+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.258+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:04:13.273+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.199 seconds
[2025-07-17T22:04:43.676+0000] {processor.py:186} INFO - Started process (PID=1366) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:04:43.677+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:04:43.679+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:43.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:04:43.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:43.743+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:43.752+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:04:43.855+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:43.855+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:43.866+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:43.866+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:04:43.885+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.215 seconds
[2025-07-17T22:05:14.167+0000] {processor.py:186} INFO - Started process (PID=1502) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:05:14.168+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:05:14.170+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.170+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:05:14.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.242+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:14.251+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:05:14.358+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.358+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:14.371+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.371+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:05:14.394+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.233 seconds
[2025-07-17T22:05:44.622+0000] {processor.py:186} INFO - Started process (PID=1638) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:05:44.623+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:05:44.625+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:44.625+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:05:44.700+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:44.700+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:44.709+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:05:44.844+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:44.844+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:44.869+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:44.869+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:05:44.896+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.279 seconds
[2025-07-17T22:06:15.078+0000] {processor.py:186} INFO - Started process (PID=1774) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:06:15.079+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:06:15.081+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.080+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:06:15.155+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.155+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:15.167+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:06:15.282+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.282+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:15.294+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.294+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:06:15.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.242 seconds
[2025-07-17T22:07:29.407+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:07:29.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:07:29.411+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:07:29.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.484+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:29.493+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:07:29.720+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.719+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:29.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.728+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:07:29.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.345 seconds
[2025-07-17T22:08:00.913+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:08:00.914+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:08:00.916+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.916+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:08:01.103+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.102+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:01.109+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:08:01.190+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.190+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:01.201+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.200+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:08:01.218+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.310 seconds
[2025-07-17T22:08:31.402+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:08:31.403+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:08:31.406+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.405+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:08:31.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.484+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:31.491+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:08:31.602+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.602+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:31.612+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.612+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:08:31.631+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.235 seconds
[2025-07-17T22:09:02.105+0000] {processor.py:186} INFO - Started process (PID=686) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:09:02.106+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:09:02.108+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:02.108+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:09:02.173+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:02.173+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:02.182+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:09:02.271+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:02.271+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:02.282+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:02.282+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:09:02.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.199 seconds
[2025-07-17T22:09:32.348+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:09:32.349+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:09:32.351+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:32.351+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:09:32.425+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:32.425+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:32.431+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:09:32.530+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:32.529+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:32.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:32.540+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:09:32.559+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.218 seconds
[2025-07-17T22:10:02.974+0000] {processor.py:186} INFO - Started process (PID=956) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:10:02.975+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:10:02.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:02.977+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:10:03.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:03.046+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:03.054+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:10:03.150+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:03.150+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:03.160+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:03.160+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:10:03.181+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.213 seconds
[2025-07-17T22:10:34.030+0000] {processor.py:186} INFO - Started process (PID=1092) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:10:34.030+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:10:34.033+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:34.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:10:34.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:34.107+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:34.115+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:10:34.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:34.202+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:34.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:34.212+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:10:34.229+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.205 seconds
[2025-07-17T22:11:05.105+0000] {processor.py:186} INFO - Started process (PID=1228) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:11:05.106+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:11:05.109+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:05.109+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:11:05.184+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:05.184+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:05.190+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:11:05.283+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:05.283+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:05.297+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:05.297+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:11:05.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.215 seconds
[2025-07-17T22:11:36.077+0000] {processor.py:186} INFO - Started process (PID=1364) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:11:36.078+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:11:36.080+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:36.080+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:11:36.150+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:36.149+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:36.158+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:11:36.258+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:36.258+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:36.270+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:36.269+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:11:36.292+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.220 seconds
[2025-07-17T22:12:06.931+0000] {processor.py:186} INFO - Started process (PID=1495) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:12:06.932+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:12:06.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:06.933+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:12:07.002+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:07.002+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:07.008+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:12:07.100+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:07.100+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:07.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:07.114+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:12:07.131+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.207 seconds
[2025-07-17T22:12:37.397+0000] {processor.py:186} INFO - Started process (PID=1626) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:12:37.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:12:37.400+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:37.400+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:12:37.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:37.486+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:37.499+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:12:37.608+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:37.608+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:37.620+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:37.620+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:12:37.640+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.249 seconds
