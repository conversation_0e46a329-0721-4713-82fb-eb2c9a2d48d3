[2025-07-17T22:41:30.399+0000] {processor.py:186} INFO - Started process (PID=258) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:41:30.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:41:30.401+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.401+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:41:30.470+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.469+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:30.479+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:41:30.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.699+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:30.710+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.710+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:41:30.735+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.341 seconds
[2025-07-17T22:42:01.443+0000] {processor.py:186} INFO - Started process (PID=389) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:01.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:42:01.445+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:01.519+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.519+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:01.528+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:01.764+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.764+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:01.776+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.776+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:42:01.796+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.358 seconds
[2025-07-17T22:42:32.140+0000] {processor.py:186} INFO - Started process (PID=520) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:32.141+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-17T22:42:32.144+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.143+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:32.396+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.396+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:32.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-17T22:42:32.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.494+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:32.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.502+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-17T22:42:32.516+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.383 seconds
