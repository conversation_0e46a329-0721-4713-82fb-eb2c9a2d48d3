[2025-07-17T22:41:28.685+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:28.686+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:41:28.689+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.688+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:28.769+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.769+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:28.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:28.878+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.877+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:41:29.089+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.410 seconds
[2025-07-17T22:41:59.468+0000] {processor.py:186} INFO - Started process (PID=352) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:59.469+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:41:59.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.471+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:59.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.549+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.557+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:59.783+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.783+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:59.791+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.791+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:41:59.808+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.346 seconds
[2025-07-17T22:42:30.266+0000] {processor.py:186} INFO - Started process (PID=483) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:42:30.267+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:42:30.268+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.268+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:42:30.483+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.482+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.490+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:42:30.581+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.580+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.589+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.589+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:42:30.602+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.342 seconds
[2025-07-17T22:43:00.883+0000] {processor.py:186} INFO - Started process (PID=614) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:00.884+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:43:00.885+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.885+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:00.959+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.959+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.968+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:01.060+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:01.072+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.072+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:43:01.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.215 seconds
[2025-07-17T22:43:31.301+0000] {processor.py:186} INFO - Started process (PID=745) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:31.302+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:43:31.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.303+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:31.380+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.380+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:31.387+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:31.490+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.490+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:31.503+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.502+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:43:31.523+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.230 seconds
[2025-07-17T22:44:02.420+0000] {processor.py:186} INFO - Started process (PID=876) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:02.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:44:02.422+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:02.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.494+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:02.502+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:02.593+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.603+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:44:02.623+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.209 seconds
[2025-07-17T22:44:33.382+0000] {processor.py:186} INFO - Started process (PID=1007) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:33.383+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:44:33.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:33.461+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.460+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:33.469+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:33.573+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.573+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.586+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.586+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:44:33.608+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.233 seconds
