[2025-07-17T21:34:30.009+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:34:30.010+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:34:30.013+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:34:30.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.101+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:30.108+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:34:30.339+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.339+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:30.348+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.348+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:34:30.370+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.368 seconds
[2025-07-17T21:35:01.127+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:01.133+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:35:01.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.140+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:01.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.246+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:01.476+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:01.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.625+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:01.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.638+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:35:01.661+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.543 seconds
[2025-07-17T21:35:32.035+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:32.037+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:35:32.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:32.136+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.136+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:32.144+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:32.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.237+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.251+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:35:32.272+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.248 seconds
[2025-07-17T21:36:02.334+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:02.335+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:36:02.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.338+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:02.406+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.406+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:02.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:02.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.512+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:02.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.523+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:36:02.542+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.214 seconds
[2025-07-17T21:36:32.848+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:32.849+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:36:32.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.852+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:32.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.922+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:32.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:33.031+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:33.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.041+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:36:33.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.213 seconds
