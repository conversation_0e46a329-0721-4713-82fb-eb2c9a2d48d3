[2025-07-17T21:34:30.009+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:34:30.010+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:34:30.013+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:34:30.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.101+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:30.108+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:34:30.339+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.339+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:30.348+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.348+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:34:30.370+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.368 seconds
[2025-07-17T21:35:01.127+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:01.133+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:35:01.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.140+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:01.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.246+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:01.476+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:01.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.625+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:01.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.638+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:35:01.661+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.543 seconds
[2025-07-17T21:35:32.035+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:32.037+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:35:32.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:32.136+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.136+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:32.144+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:32.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.237+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.251+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:35:32.272+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.248 seconds
[2025-07-17T21:36:02.334+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:02.335+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:36:02.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.338+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:02.406+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.406+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:02.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:02.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.512+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:02.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.523+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:36:02.542+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.214 seconds
[2025-07-17T21:36:32.848+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:32.849+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:36:32.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.852+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:32.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.922+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:32.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:33.031+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:33.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.041+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:36:33.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.213 seconds
[2025-07-17T21:37:03.304+0000] {processor.py:186} INFO - Started process (PID=918) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:03.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:37:03.307+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.306+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:03.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.386+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:03.396+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:03.514+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.514+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:03.528+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.528+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:37:03.546+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.248 seconds
[2025-07-17T21:37:33.691+0000] {processor.py:186} INFO - Started process (PID=1054) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:33.692+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:37:33.694+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.694+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:33.766+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.766+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:33.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:33.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.883+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:33.895+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.895+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:37:33.911+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.226 seconds
[2025-07-17T21:38:04.424+0000] {processor.py:186} INFO - Started process (PID=1195) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:04.425+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:38:04.427+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:04.504+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.504+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:04.514+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:04.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.624+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:04.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:38:04.665+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.246 seconds
[2025-07-17T21:38:35.002+0000] {processor.py:186} INFO - Started process (PID=1331) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:35.003+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:38:35.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:35.078+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.078+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:35.086+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:35.183+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.183+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:35.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.195+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:38:35.216+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.220 seconds
[2025-07-17T21:39:05.537+0000] {processor.py:186} INFO - Started process (PID=1467) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:05.538+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:39:05.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.541+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:05.613+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.613+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:05.622+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:05.723+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.723+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:05.735+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.735+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:39:05.756+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.225 seconds
[2025-07-17T21:39:35.861+0000] {processor.py:186} INFO - Started process (PID=1596) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:35.862+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:39:35.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.864+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:35.934+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.934+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.942+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:36.037+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.036+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:36.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.046+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:39:36.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.208 seconds
[2025-07-17T21:40:06.775+0000] {processor.py:186} INFO - Started process (PID=1732) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:06.777+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:40:06.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:06.879+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.878+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:06.887+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:07.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.003+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:07.017+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.017+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:40:07.037+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.268 seconds
[2025-07-17T21:40:37.840+0000] {processor.py:186} INFO - Started process (PID=1870) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:37.841+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:40:37.844+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:37.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.938+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:37.947+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:38.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.056+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:38.068+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.068+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:40:38.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.257 seconds
[2025-07-17T21:42:56.815+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:42:56.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:42:56.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:42:56.901+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.901+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:56.909+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:42:57.164+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.164+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:57.178+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.177+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:42:57.197+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.389 seconds
[2025-07-17T21:43:27.720+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:27.721+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:43:27.724+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:27.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.818+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:27.987+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:28.121+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.121+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:28.132+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.132+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:43:28.148+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.437 seconds
[2025-07-17T21:43:58.827+0000] {processor.py:186} INFO - Started process (PID=508) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:58.828+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:43:58.830+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.830+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:58.926+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.926+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:58.933+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:59.043+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.043+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.056+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:43:59.075+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.256 seconds
[2025-07-17T21:44:29.395+0000] {processor.py:186} INFO - Started process (PID=652) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:44:29.396+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:44:29.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.398+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:44:29.482+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.482+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:29.493+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:44:29.619+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.619+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:29.634+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.633+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:44:29.658+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.270 seconds
[2025-07-17T21:44:59.815+0000] {processor.py:186} INFO - Started process (PID=786) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:44:59.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:44:59.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.819+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:44:59.913+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.913+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:59.923+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:45:00.032+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:00.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:00.043+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:00.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:45:00.059+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.250 seconds
