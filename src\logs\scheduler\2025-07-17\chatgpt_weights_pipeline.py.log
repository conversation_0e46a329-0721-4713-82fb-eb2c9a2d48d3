[2025-07-17T21:34:30.009+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:34:30.010+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:34:30.013+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:34:30.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.101+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:30.108+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:34:30.339+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.339+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:30.348+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.348+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:34:30.370+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.368 seconds
[2025-07-17T21:35:01.127+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:01.133+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:35:01.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.140+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:01.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.246+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:01.476+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:01.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.625+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:01.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.638+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:35:01.661+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.543 seconds
[2025-07-17T21:35:32.035+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:32.037+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:35:32.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:32.136+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.136+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:32.144+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:35:32.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.237+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.251+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:35:32.272+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.248 seconds
[2025-07-17T21:36:02.334+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:02.335+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:36:02.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.338+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:02.406+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.406+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:02.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:02.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.512+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:02.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.523+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:36:02.542+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.214 seconds
[2025-07-17T21:36:32.848+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:32.849+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:36:32.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.852+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:32.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.922+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:32.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:36:33.031+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:33.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.041+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:36:33.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.213 seconds
[2025-07-17T21:37:03.304+0000] {processor.py:186} INFO - Started process (PID=918) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:03.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:37:03.307+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.306+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:03.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.386+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:03.396+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:03.514+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.514+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:03.528+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.528+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:37:03.546+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.248 seconds
[2025-07-17T21:37:33.691+0000] {processor.py:186} INFO - Started process (PID=1054) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:33.692+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:37:33.694+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.694+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:33.766+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.766+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:33.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:37:33.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.883+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:33.895+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.895+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:37:33.911+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.226 seconds
[2025-07-17T21:38:04.424+0000] {processor.py:186} INFO - Started process (PID=1195) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:04.425+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:38:04.427+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:04.504+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.504+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:04.514+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:04.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.624+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:04.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:38:04.665+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.246 seconds
[2025-07-17T21:38:35.002+0000] {processor.py:186} INFO - Started process (PID=1331) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:35.003+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:38:35.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:35.078+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.078+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:35.086+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:38:35.183+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.183+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:35.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.195+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:38:35.216+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.220 seconds
[2025-07-17T21:39:05.537+0000] {processor.py:186} INFO - Started process (PID=1467) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:05.538+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:39:05.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.541+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:05.613+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.613+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:05.622+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:05.723+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.723+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:05.735+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.735+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:39:05.756+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.225 seconds
[2025-07-17T21:39:35.861+0000] {processor.py:186} INFO - Started process (PID=1596) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:35.862+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:39:35.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.864+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:35.934+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.934+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.942+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:39:36.037+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.036+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:36.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.046+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:39:36.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.208 seconds
[2025-07-17T21:40:06.775+0000] {processor.py:186} INFO - Started process (PID=1732) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:06.777+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:40:06.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:06.879+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.878+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:06.887+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:07.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.003+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:07.017+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.017+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:40:07.037+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.268 seconds
[2025-07-17T21:40:37.840+0000] {processor.py:186} INFO - Started process (PID=1870) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:37.841+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:40:37.844+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:37.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.938+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:37.947+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:40:38.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.056+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:38.068+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.068+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:40:38.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.257 seconds
[2025-07-17T21:42:56.815+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:42:56.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:42:56.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:42:56.901+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.901+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:56.909+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:42:57.164+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.164+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:57.178+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.177+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:42:57.197+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.389 seconds
[2025-07-17T21:43:27.720+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:27.721+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:43:27.724+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:27.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.818+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:27.987+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:28.121+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.121+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:28.132+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.132+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:43:28.148+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.437 seconds
[2025-07-17T21:43:58.827+0000] {processor.py:186} INFO - Started process (PID=508) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:58.828+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:43:58.830+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.830+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:58.926+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.926+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:58.933+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:43:59.043+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.043+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.056+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:43:59.075+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.256 seconds
[2025-07-17T21:44:29.395+0000] {processor.py:186} INFO - Started process (PID=652) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:44:29.396+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:44:29.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.398+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:44:29.482+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.482+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:29.493+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:44:29.619+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.619+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:29.634+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.633+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:44:29.658+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.270 seconds
[2025-07-17T21:44:59.815+0000] {processor.py:186} INFO - Started process (PID=786) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:44:59.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:44:59.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.819+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:44:59.913+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.913+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:59.923+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:45:00.032+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:00.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:00.043+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:00.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:45:00.059+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.250 seconds
[2025-07-17T21:55:24.706+0000] {processor.py:186} INFO - Started process (PID=237) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:55:24.707+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:55:24.709+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.709+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:55:24.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.779+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:24.790+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:55:25.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.064+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:25.074+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.074+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:55:25.095+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.395 seconds
[2025-07-17T21:55:55.530+0000] {processor.py:186} INFO - Started process (PID=373) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:55:55.531+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:55:55.534+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.534+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:55:55.623+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.623+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:55.759+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:55:55.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.845+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:55.854+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.854+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:55:55.872+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.351 seconds
[2025-07-17T21:56:26.080+0000] {processor.py:186} INFO - Started process (PID=511) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:56:26.081+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:56:26.083+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.082+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:56:26.149+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.149+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:26.155+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:56:26.244+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.244+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:26.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.255+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:56:26.274+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.202 seconds
[2025-07-17T21:56:56.370+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:56:56.371+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:56:56.373+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.373+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:56:56.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.442+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:56.449+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:56:56.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.541+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:56.552+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.552+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:56:56.572+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.208 seconds
[2025-07-17T21:57:26.739+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:57:26.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:57:26.742+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:57:26.807+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.806+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:26.814+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:57:26.904+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.904+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:26.912+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.912+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:57:26.929+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.195 seconds
[2025-07-17T21:57:57.200+0000] {processor.py:186} INFO - Started process (PID=924) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:57:57.201+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:57:57.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.204+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:57:57.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.273+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:57.280+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:57:57.390+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:57.407+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.406+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:57:57.435+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.241 seconds
[2025-07-17T21:58:27.699+0000] {processor.py:186} INFO - Started process (PID=1060) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:58:27.700+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T21:58:27.704+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.704+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:58:27.786+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.786+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:27.792+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T21:58:27.887+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.887+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:27.898+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.898+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T21:58:27.916+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.225 seconds
[2025-07-17T22:00:36.276+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:00:36.277+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:00:36.279+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:00:36.353+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.353+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:36.360+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:00:36.622+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.622+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:36.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.631+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:00:36.649+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.380 seconds
[2025-07-17T22:01:06.959+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:01:06.960+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:01:06.963+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:01:07.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.203+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:07.210+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:01:07.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.326+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:07.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.335+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:01:07.353+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.400 seconds
[2025-07-17T22:01:37.420+0000] {processor.py:186} INFO - Started process (PID=508) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:01:37.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:01:37.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:01:37.490+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.490+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:37.498+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:01:37.584+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.584+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:37.597+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.596+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:01:37.619+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.205 seconds
[2025-07-17T22:02:07.694+0000] {processor.py:186} INFO - Started process (PID=641) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:02:07.695+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:02:07.697+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.697+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:02:07.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.762+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:07.771+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:02:07.858+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.857+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:07.869+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.868+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:02:07.883+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.195 seconds
[2025-07-17T22:02:38.112+0000] {processor.py:186} INFO - Started process (PID=777) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:02:38.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:02:38.116+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.115+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:02:38.184+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.183+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:38.192+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:02:38.279+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.279+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:38.288+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.288+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:02:38.305+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.198 seconds
[2025-07-17T22:03:08.441+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:03:08.442+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:03:08.445+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:03:08.511+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.510+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:08.518+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:03:08.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.606+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:08.617+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.617+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:03:08.634+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.199 seconds
[2025-07-17T22:03:39.222+0000] {processor.py:186} INFO - Started process (PID=1054) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:03:39.223+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:03:39.225+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.225+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:03:39.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.293+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:39.301+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:03:39.402+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.401+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:39.412+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.412+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:03:39.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.210 seconds
[2025-07-17T22:04:10.055+0000] {processor.py:186} INFO - Started process (PID=1188) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:04:10.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:04:10.058+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.058+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:04:10.139+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.139+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:10.145+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:04:10.240+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.240+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:10.250+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.250+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:04:10.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.217 seconds
[2025-07-17T22:04:40.586+0000] {processor.py:186} INFO - Started process (PID=1326) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:04:40.587+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:04:40.589+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.589+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:04:40.655+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.655+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:40.662+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:04:40.754+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.754+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:40.765+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.765+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:04:40.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.206 seconds
[2025-07-17T22:05:11.076+0000] {processor.py:186} INFO - Started process (PID=1462) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:05:11.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:05:11.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:05:11.146+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.146+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:11.154+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:05:11.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.250+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:11.262+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.261+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:05:11.278+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.208 seconds
[2025-07-17T22:05:41.383+0000] {processor.py:186} INFO - Started process (PID=1598) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:05:41.384+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:05:41.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.387+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:05:41.458+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.458+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:41.466+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:05:41.573+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.572+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:41.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.583+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:05:41.603+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.227 seconds
[2025-07-17T22:06:12.003+0000] {processor.py:186} INFO - Started process (PID=1734) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:06:12.004+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:06:12.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.006+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:06:12.073+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.073+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:12.080+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:06:12.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:12.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:06:12.196+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.198 seconds
[2025-07-17T22:07:27.847+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:07:27.848+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:07:27.850+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:27.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:07:27.931+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:27.931+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:27.938+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:07:28.190+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.190+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:28.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.199+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:07:28.216+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.375 seconds
[2025-07-17T22:07:59.358+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:07:59.359+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:07:59.362+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.361+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:07:59.440+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.440+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:59.570+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:07:59.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:59.668+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.668+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:07:59.684+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.331 seconds
[2025-07-17T22:08:30.144+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:08:30.145+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:08:30.147+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.147+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:08:30.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.220+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:30.228+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:08:30.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.335+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:30.347+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.347+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:08:30.367+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.229 seconds
[2025-07-17T22:09:00.595+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:09:00.596+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:09:00.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.598+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:09:00.670+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.670+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:00.680+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:09:00.774+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.773+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:00.784+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.784+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:09:00.802+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.213 seconds
[2025-07-17T22:09:31.222+0000] {processor.py:186} INFO - Started process (PID=780) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:09:31.223+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:09:31.225+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.225+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:09:31.294+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.294+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:31.302+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:09:31.399+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.399+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:31.409+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.409+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:09:31.425+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.209 seconds
[2025-07-17T22:10:01.836+0000] {processor.py:186} INFO - Started process (PID=916) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:10:01.837+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:10:01.840+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.839+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:10:01.914+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.914+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:01.923+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:10:02.030+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:02.030+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:02.042+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:02.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:10:02.061+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.231 seconds
[2025-07-17T22:10:32.868+0000] {processor.py:186} INFO - Started process (PID=1052) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:10:32.868+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:10:32.871+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:32.870+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:10:32.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:32.939+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:32.946+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:10:33.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:33.041+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:33.050+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:33.050+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:10:33.069+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.207 seconds
[2025-07-17T22:11:03.804+0000] {processor.py:186} INFO - Started process (PID=1188) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:11:03.805+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:11:03.807+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:03.807+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:11:03.886+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:03.886+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:03.895+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:11:04.016+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:04.015+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:04.027+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:04.026+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:11:04.046+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.248 seconds
[2025-07-17T22:11:34.986+0000] {processor.py:186} INFO - Started process (PID=1324) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:11:34.987+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:11:34.989+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:34.989+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:11:35.067+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:35.067+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:35.075+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:11:35.186+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:35.185+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:35.200+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:35.200+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:11:35.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.241 seconds
[2025-07-17T22:12:05.703+0000] {processor.py:186} INFO - Started process (PID=1455) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:12:05.704+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:12:05.705+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:05.705+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:12:05.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:05.780+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:05.790+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:12:05.887+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:05.887+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:05.897+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:05.897+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:12:05.916+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.219 seconds
[2025-07-17T22:12:36.354+0000] {processor.py:186} INFO - Started process (PID=1588) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:12:36.355+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:12:36.356+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:36.356+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:12:36.425+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:36.424+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:36.433+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:12:36.528+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:36.528+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:36.538+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:36.537+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:12:36.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.209 seconds
