[2025-07-17T22:41:28.685+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:28.686+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:41:28.689+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.688+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:28.769+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.769+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:28.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:28.878+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.877+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:41:29.089+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.410 seconds
[2025-07-17T22:41:59.468+0000] {processor.py:186} INFO - Started process (PID=352) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:59.469+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:41:59.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.471+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:59.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.549+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.557+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:41:59.783+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.783+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:59.791+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.791+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:41:59.808+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.346 seconds
[2025-07-17T22:42:30.266+0000] {processor.py:186} INFO - Started process (PID=483) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:42:30.267+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:42:30.268+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.268+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:42:30.483+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.482+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.490+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:42:30.581+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.580+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.589+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.589+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:42:30.602+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.342 seconds
[2025-07-17T22:43:00.883+0000] {processor.py:186} INFO - Started process (PID=614) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:00.884+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:43:00.885+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.885+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:00.959+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.959+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.968+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:01.060+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:01.072+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.072+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:43:01.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.215 seconds
[2025-07-17T22:43:31.301+0000] {processor.py:186} INFO - Started process (PID=745) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:31.302+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:43:31.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.303+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:31.380+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.380+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:31.387+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:43:31.490+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.490+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:31.503+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.502+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:43:31.523+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.230 seconds
[2025-07-17T22:44:02.420+0000] {processor.py:186} INFO - Started process (PID=876) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:02.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:44:02.422+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:02.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.494+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:02.502+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:02.593+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.603+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:44:02.623+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.209 seconds
[2025-07-17T22:44:33.382+0000] {processor.py:186} INFO - Started process (PID=1007) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:33.383+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:44:33.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:33.461+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.460+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:33.469+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:44:33.573+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.573+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.586+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.586+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:44:33.608+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.233 seconds
[2025-07-17T22:45:04.300+0000] {processor.py:186} INFO - Started process (PID=1138) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:45:04.301+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:45:04.302+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.302+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:45:04.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.383+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:04.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:45:04.488+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.488+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:04.499+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.499+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:45:04.521+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.227 seconds
[2025-07-17T22:45:35.057+0000] {processor.py:186} INFO - Started process (PID=1269) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:45:35.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:45:35.061+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.060+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:45:35.185+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.185+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:35.192+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:45:35.290+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.290+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:35.302+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.302+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:45:35.322+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.272 seconds
[2025-07-17T22:46:05.911+0000] {processor.py:186} INFO - Started process (PID=1400) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:46:05.912+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:46:05.913+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.913+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:46:05.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.995+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:06.003+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:46:06.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.113+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:06.125+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.125+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:46:06.145+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.241 seconds
[2025-07-17T22:46:36.228+0000] {processor.py:186} INFO - Started process (PID=1531) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:46:36.230+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:46:36.231+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.231+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:46:36.304+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.304+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:36.312+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:46:36.406+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.405+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:36.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.417+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:46:36.435+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.212 seconds
[2025-07-17T22:47:07.295+0000] {processor.py:186} INFO - Started process (PID=1668) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:47:07.296+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:47:07.297+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.297+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:47:07.364+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.364+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:07.371+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:47:07.463+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.463+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:07.474+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.474+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:47:07.493+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.204 seconds
[2025-07-17T22:47:38.480+0000] {processor.py:186} INFO - Started process (PID=1805) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:47:38.481+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:47:38.482+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.482+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:47:38.564+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.564+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:38.573+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:47:38.692+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.692+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:38.705+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.704+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:47:38.725+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.251 seconds
[2025-07-17T22:48:09.287+0000] {processor.py:186} INFO - Started process (PID=1936) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:48:09.287+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:48:09.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.289+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:48:09.359+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.358+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:09.367+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:48:09.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.459+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:09.470+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.470+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:48:09.489+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.208 seconds
[2025-07-17T22:48:40.315+0000] {processor.py:186} INFO - Started process (PID=2067) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:48:40.316+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:48:40.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.317+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:48:40.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.395+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:40.402+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:48:40.508+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.507+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:40.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.519+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:48:40.539+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.231 seconds
[2025-07-17T22:49:10.836+0000] {processor.py:186} INFO - Started process (PID=2198) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:49:10.838+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:49:10.839+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.839+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:49:10.916+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.915+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:10.923+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:49:11.045+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.045+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:11.059+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.058+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:49:11.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.261 seconds
[2025-07-17T22:49:41.926+0000] {processor.py:186} INFO - Started process (PID=2329) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:49:41.927+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:49:41.928+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:41.928+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:49:41.991+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:41.991+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:41.999+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:49:42.090+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.090+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:42.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.100+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:49:42.118+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.197 seconds
[2025-07-17T22:50:12.542+0000] {processor.py:186} INFO - Started process (PID=2460) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:50:12.544+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:50:12.546+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:12.546+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:50:12.638+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:12.638+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:12.645+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:50:12.748+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:12.748+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:12.761+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:12.760+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:50:12.785+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.251 seconds
[2025-07-17T22:50:43.257+0000] {processor.py:186} INFO - Started process (PID=2591) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:50:43.258+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:50:43.260+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.259+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:50:43.332+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.331+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:43.341+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:50:43.448+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.448+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:43.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.459+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:50:43.479+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.228 seconds
[2025-07-17T22:51:14.012+0000] {processor.py:186} INFO - Started process (PID=2722) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:51:14.013+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:51:14.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:51:14.109+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.109+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:14.118+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:51:14.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.264+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:14.283+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.283+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:51:14.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.309 seconds
[2025-07-17T22:51:44.567+0000] {processor.py:186} INFO - Started process (PID=2853) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:51:44.568+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:51:44.569+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.569+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:51:44.634+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.634+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:44.641+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:51:44.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.728+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:44.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.740+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:51:44.761+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.200 seconds
[2025-07-17T22:52:15.251+0000] {processor.py:186} INFO - Started process (PID=2984) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:52:15.252+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:52:15.253+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.253+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:52:15.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.317+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:15.324+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:52:15.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:15.426+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.426+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:52:15.446+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.201 seconds
[2025-07-17T22:52:45.974+0000] {processor.py:186} INFO - Started process (PID=3115) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:52:45.974+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:52:45.976+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:45.975+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:52:46.039+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:46.039+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:46.047+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:52:46.135+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:46.135+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:46.147+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:46.147+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:52:46.168+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.200 seconds
[2025-07-17T22:53:16.487+0000] {processor.py:186} INFO - Started process (PID=3246) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:53:16.488+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:53:16.490+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:16.489+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:53:16.560+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:16.560+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:16.567+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:53:16.665+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:16.665+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:16.677+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:16.677+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:53:16.696+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.215 seconds
[2025-07-17T22:53:47.399+0000] {processor.py:186} INFO - Started process (PID=3383) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:53:47.400+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:53:47.402+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:47.401+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:53:47.474+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:47.474+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:47.482+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:53:47.590+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:47.590+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:47.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:47.601+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:53:47.622+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.229 seconds
