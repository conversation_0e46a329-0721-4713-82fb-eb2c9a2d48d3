[2025-07-17T22:21:48.218+0000] {processor.py:186} INFO - Started process (PID=3946) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:21:48.219+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-17T22:21:48.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.220+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:21:48.298+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.298+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:21:48.308+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-17T22:21:48.419+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.419+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:21:48.430+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.430+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-17T22:21:48.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.236 seconds
