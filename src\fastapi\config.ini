[DATA]
BEARER_TOKEN=12c082765fbe925e59e0c4496cc395398bf9f800787dba680f65f195e9dd38196b217bc693049db8b15240b7048c5860bcd74dec65fd585ab06c70b0ecdb6aa45ba62e7bb7f15edc678920b18cadefdcd36b8019205434d235a92ab1cd70882023ae55ac6a7c236e091cf206519738ab4b090964ff73c036351a400f6b83e6b8
TEST_METRICS = {
               "personal_information": {
               "name": "",
               "name_field_name": "Имя",
               "name_weight": "null",
               "date_of_birth": "",
               "date_of_birth_field_name": "Дата рождения",
               "date_of_birth_weight": "null",
               "gender": "",
               "gender_field_name": "Пол",
               "gender_weight": "null",
               "height": "",
               "height_field_name": "Рост",
               "height_weight": "null",
               "weight": "",
               "weight_field_name": "Вес",
               "weight_weight": "null"
               },
               "lifestyle": {
               "activity_level": "",
               "activity_level_field_name": "Уровень активности",
               "activity_level_weight": "null",
               "sports": "",
               "sports_field_name": "Занятия спортом",
               "sports_weight": "null",
               "smoking": "",
               "smoking_field_name": "Курение",
               "smoking_weight": "null",
               "alcohol": "",
               "alcohol_field_name": "Алкоголь",
               "alcohol_weight": "null",
               "sleep_recovery": "",
               "sleep_recovery_field_name": "Восстановление после сна",
               "sleep_recovery_weight": "null",
               "sleep_schedule": "",
               "sleep_schedule_field_name": "Режим сна",
               "sleep_schedule_weight": "null",
               "sleep_quality": "",
               "sleep_quality_field_name": "Качество сна",
               "sleep_quality_weight": "null",
               "work_schedule": "",
               "work_schedule_field_name": "Режим работы",
               "work_schedule_weight": "null",
               "stress_level": "",
               "stress_level_field_name": "Уровень стресса",
               "stress_level_weight": "null"
               },
               "health_status": {
               "blood_pressure": "",
               "blood_pressure_field_name": "Давление",
               "blood_pressure_weight": "null",
               "chronic_conditions": [],
               "chronic_conditions_field_name": "Хронические заболевания",
               "chronic_conditions_weight": "null",
               "injuries": "",
               "injuries_field_name": "Травмы",
               "injuries_weight": "null",
               "genetic_conditions": [],
               "genetic_conditions_field_name": "Генетические заболевания",
               "genetic_conditions_weight": "null",
               "regular_medicine": [],
               "regular_medicine_field_name": "Лекарства",
               "regular_medicine_weight": "null",
               "allergies": [],
               "allergies_field_name": "Аллергии",
               "allergies_weight": "null"
               },
               "nutrition": {
               "food_level": "",
               "food_level_field_name": "Уровень питания",
               "food_level_weight": "null",
               "diet_schedule": "",
               "diet_schedule_field_name": "Режим питания",
               "diet_schedule_weight": "null",
               "preferred_dishes": [],
               "preferred_dishes_field_name": "Предпочитаемые блюда",
               "preferred_dishes_weight": "null",
               "diet_balance": "",
               "diet_balance_field_name": "Баланс рациона",
               "diet_balance_weight": "null",
               "food_intolerances": [],
               "food_intolerances_field_name": "Пищевая непереносимость",
               "food_intolerances_weight": "null",
               "calorie_intake": "",
               "calorie_intake_field_name": "Калорийность",
               "calorie_intake_weight": "null",
               "dietary_supplement": "",
               "dietary_supplement_field_name": "Пищевые добавки",
               "dietary_supplement_weight": "null"
               },
               "analysis": {
               "blood_analysis": "",
               "blood_analysis_field_name": "Анализ крови",
               "blood_analysis_weight": "null",
               "biochemical_analysis": "",
               "biochemical_analysis_field_name": "Биохимический анализ",
               "biochemical_analysis_weight": "null",
               "urine_analysis": "",
               "urine_analysis_field_name": "Анализ мочи",
               "urine_analysis_weight": "null",
               "lipid_profile": "",
               "lipid_profile_field_name": "Липидный профиль",
               "lipid_profile_weight": "null",
               "glucose_tolerance_test": "",
               "glucose_tolerance_test_field_name": "Глюкозотолерантный тест",
               "glucose_tolerance_test_weight": "null",
               "thyroid_test": "",
               "thyroid_test_field_name": "Тиреоидный тест",
               "thyroid_test_weight": "null",
               "glycated_hemoglobin": "",
               "glycated_hemoglobin_field_name": "Гликированный гемоглобин",
               "glycated_hemoglobin_weight": "null",
               "coagulogram": "",
               "coagulogram_field_name": "Коагулограмма",
               "coagulogram_weight": "null",
               "inflammatory_markers": "",
               "inflammatory_markers_field_name": "Маркеры воспаления",
               "inflammatory_markers_weight": "null" }
               }
