[2025-07-17T21:34:32.354+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:34:32.355+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:34:32.359+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.358+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:34:32.435+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.434+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.445+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:34:32.708+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.708+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.722+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.721+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:34:32.744+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.396 seconds
[2025-07-17T21:35:04.280+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:04.282+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:35:04.284+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.283+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:04.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.486+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:04.493+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:04.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.582+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:04.596+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.596+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:35:04.616+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.341 seconds
[2025-07-17T21:35:35.311+0000] {processor.py:186} INFO - Started process (PID=565) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:35.312+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:35:35.315+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.314+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:35.392+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.391+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:35.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:35.509+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.509+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:35.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.519+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:35:35.541+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.235 seconds
[2025-07-17T21:36:05.628+0000] {processor.py:186} INFO - Started process (PID=701) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:05.629+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:36:05.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.632+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:05.729+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.729+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:05.739+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:05.863+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.863+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:05.881+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.881+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:36:05.901+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.279 seconds
[2025-07-17T21:36:36.001+0000] {processor.py:186} INFO - Started process (PID=835) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:36.002+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:36:36.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:36.085+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.085+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.092+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:36.196+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.208+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.208+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:36:36.229+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.235 seconds
[2025-07-17T21:37:06.597+0000] {processor.py:186} INFO - Started process (PID=971) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:37:06.598+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:37:06.602+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.601+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:37:06.710+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.710+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:06.722+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:37:06.831+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.830+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:06.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.843+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:37:06.865+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.276 seconds
[2025-07-17T21:37:37.126+0000] {processor.py:186} INFO - Started process (PID=1107) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:37:37.127+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:37:37.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.131+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:37:37.210+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.210+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:37.222+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:37:37.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.327+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:37.340+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.339+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:37:37.360+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.241 seconds
[2025-07-17T21:38:07.739+0000] {processor.py:186} INFO - Started process (PID=1243) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:38:07.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:38:07.742+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:38:07.820+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.819+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:07.829+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:38:07.925+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.925+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:07.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:07.936+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:38:07.956+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.224 seconds
[2025-07-17T21:38:38.554+0000] {processor.py:186} INFO - Started process (PID=1379) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:38:38.555+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:38:38.557+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.557+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:38:38.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.631+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:38.641+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:38:38.745+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.745+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:38.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:38.756+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:38:38.779+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.231 seconds
[2025-07-17T21:39:09.008+0000] {processor.py:186} INFO - Started process (PID=1515) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:39:09.009+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:39:09.011+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.011+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:39:09.084+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.084+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:09.093+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:39:09.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:09.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.206+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:39:09.228+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.226 seconds
[2025-07-17T21:39:39.551+0000] {processor.py:186} INFO - Started process (PID=1651) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:39:39.552+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:39:39.555+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.555+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:39:39.642+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.642+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:39.653+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:39:39.778+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.777+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:39.789+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:39.788+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:39:39.813+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.269 seconds
[2025-07-17T21:40:10.470+0000] {processor.py:186} INFO - Started process (PID=1787) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:40:10.472+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:40:10.474+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.474+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:40:10.585+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.585+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:10.599+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:40:10.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.727+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:10.739+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.739+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:40:10.765+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.303 seconds
[2025-07-17T21:40:41.556+0000] {processor.py:186} INFO - Started process (PID=1923) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:40:41.557+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:40:41.560+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.559+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:40:41.654+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.654+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:41.663+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:40:41.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.779+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:41.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:41.792+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:40:41.815+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.267 seconds
[2025-07-17T21:42:59.174+0000] {processor.py:186} INFO - Started process (PID=297) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:42:59.175+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:42:59.180+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.179+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:42:59.259+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.259+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:59.268+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:42:59.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.520+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:59.529+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.529+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:42:59.548+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.380 seconds
[2025-07-17T21:43:30.292+0000] {processor.py:186} INFO - Started process (PID=433) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:43:30.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:43:30.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.295+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:43:30.533+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.532+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:30.542+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:43:30.657+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.656+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:30.674+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.674+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:43:30.694+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.409 seconds
[2025-07-17T21:44:00.974+0000] {processor.py:186} INFO - Started process (PID=571) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:44:00.976+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:44:00.979+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.978+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:44:01.072+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.072+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:01.080+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:44:01.185+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.184+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:01.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.195+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:44:01.215+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.248 seconds
[2025-07-17T21:44:31.439+0000] {processor.py:186} INFO - Started process (PID=707) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:44:31.440+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:44:31.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.443+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:44:31.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.524+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:31.534+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:44:31.652+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.651+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:31.667+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.667+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:44:31.689+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.256 seconds
[2025-07-17T21:45:02.385+0000] {processor.py:186} INFO - Started process (PID=841) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:45:02.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:45:02.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:45:02.480+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.479+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:02.488+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:45:02.609+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.608+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:02.621+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.621+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:45:02.644+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.265 seconds
[2025-07-17T21:55:26.932+0000] {processor.py:186} INFO - Started process (PID=298) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:55:26.933+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:55:26.935+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.935+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:55:27.011+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.011+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:27.019+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:55:27.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.318+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:27.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:55:27.342+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.417 seconds
[2025-07-17T21:55:57.742+0000] {processor.py:186} INFO - Started process (PID=434) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:55:57.743+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:55:57.746+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.745+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:55:57.951+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.951+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:57.958+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:55:58.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.057+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:58.068+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.067+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:55:58.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.351 seconds
[2025-07-17T21:56:28.394+0000] {processor.py:186} INFO - Started process (PID=573) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:56:28.395+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:56:28.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.398+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:56:28.472+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.472+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:28.481+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:56:28.574+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.574+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:28.585+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.585+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:56:28.605+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.218 seconds
[2025-07-17T21:56:58.731+0000] {processor.py:186} INFO - Started process (PID=709) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:56:58.732+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:56:58.734+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:56:58.801+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.801+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:58.809+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:56:58.920+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.920+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:58.932+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.932+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:56:58.952+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.227 seconds
[2025-07-17T21:57:29.200+0000] {processor.py:186} INFO - Started process (PID=843) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:57:29.201+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:57:29.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.203+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:57:29.267+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.267+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:29.275+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:57:29.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.367+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:29.378+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.378+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:57:29.396+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.201 seconds
[2025-07-17T21:57:59.942+0000] {processor.py:186} INFO - Started process (PID=979) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:57:59.943+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:57:59.945+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.945+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:58:00.029+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.028+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:00.036+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:58:00.151+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.150+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:00.161+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.161+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:58:00.187+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.252 seconds
[2025-07-17T21:58:30.254+0000] {processor.py:186} INFO - Started process (PID=1115) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:58:30.255+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:58:30.257+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.257+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:58:30.329+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.329+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:30.337+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:58:30.439+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.439+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:30.452+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.452+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:58:30.473+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.225 seconds
[2025-07-17T22:00:38.380+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:00:38.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:00:38.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.383+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:00:38.448+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.448+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:38.455+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:00:38.682+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.682+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:38.691+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.691+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:00:38.707+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.332 seconds
[2025-07-17T22:01:09.700+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:01:09.701+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:01:09.703+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.703+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:01:09.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.929+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:09.939+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:01:10.048+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.048+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:10.061+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.060+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:01:10.085+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.392 seconds
[2025-07-17T22:01:41.010+0000] {processor.py:186} INFO - Started process (PID=565) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:01:41.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:01:41.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.014+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:01:41.094+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.094+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:41.102+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:01:41.218+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.217+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:41.229+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.229+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:01:41.252+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.248 seconds
[2025-07-17T22:02:11.466+0000] {processor.py:186} INFO - Started process (PID=701) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:02:11.467+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:02:11.469+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:11.469+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:02:11.536+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:11.535+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:11.545+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:02:11.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:11.643+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:11.655+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:11.655+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:02:11.674+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.213 seconds
[2025-07-17T22:02:41.857+0000] {processor.py:186} INFO - Started process (PID=837) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:02:41.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:02:41.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:41.860+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:02:41.931+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:41.930+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:41.939+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:02:42.036+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.036+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:42.048+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.048+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:02:42.067+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.216 seconds
[2025-07-17T22:03:12.263+0000] {processor.py:186} INFO - Started process (PID=973) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:03:12.263+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:03:12.265+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.265+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:03:12.332+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.332+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:12.343+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:03:12.453+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.453+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:12.467+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.467+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:03:12.487+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.231 seconds
[2025-07-17T22:03:43.204+0000] {processor.py:186} INFO - Started process (PID=1107) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:03:43.207+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:03:43.211+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.210+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:03:43.356+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.356+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:43.371+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:03:43.553+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.552+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:43.571+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.570+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:03:43.608+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.413 seconds
[2025-07-17T22:04:13.757+0000] {processor.py:186} INFO - Started process (PID=1245) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:04:13.758+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:04:13.760+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.760+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:04:13.828+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.828+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:13.835+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:04:13.929+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.929+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:13.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:13.939+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:04:13.960+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.208 seconds
[2025-07-17T22:04:44.165+0000] {processor.py:186} INFO - Started process (PID=1381) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:04:44.166+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:04:44.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.167+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:04:44.238+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.238+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:44.247+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:04:44.334+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.334+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:44.343+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.342+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:04:44.359+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.200 seconds
[2025-07-17T22:05:14.467+0000] {processor.py:186} INFO - Started process (PID=1515) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:05:14.468+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:05:14.470+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.470+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:05:14.549+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.549+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:14.559+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:05:14.648+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.648+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:14.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:05:14.674+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.213 seconds
[2025-07-17T22:05:45.199+0000] {processor.py:186} INFO - Started process (PID=1653) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:05:45.200+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:05:45.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.202+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:05:45.283+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.282+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:45.291+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:05:45.397+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.397+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:45.409+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.408+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:05:45.432+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.240 seconds
[2025-07-17T22:06:15.624+0000] {processor.py:186} INFO - Started process (PID=1789) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:06:15.625+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:06:15.627+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.627+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:06:15.702+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.702+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:15.709+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:06:15.802+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.801+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:15.811+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.811+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:06:15.827+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.209 seconds
[2025-07-17T22:07:29.945+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:07:29.947+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:07:29.950+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.950+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:07:30.025+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.024+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:30.034+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:07:30.253+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.253+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:30.262+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.262+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:07:30.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.346 seconds
[2025-07-17T22:08:01.629+0000] {processor.py:186} INFO - Started process (PID=427) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:08:01.630+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:08:01.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.632+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:08:01.842+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.842+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:01.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:08:01.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.937+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:01.946+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.946+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:08:01.961+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.337 seconds
[2025-07-17T22:08:32.718+0000] {processor.py:186} INFO - Started process (PID=563) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:08:32.723+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:08:32.727+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:32.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:08:32.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:32.805+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:32.813+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:08:32.926+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:32.925+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:32.940+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:32.940+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:08:32.959+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.247 seconds
[2025-07-17T22:09:03.390+0000] {processor.py:186} INFO - Started process (PID=699) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:09:03.391+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:09:03.394+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:03.393+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:09:03.473+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:03.473+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:03.482+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:09:03.577+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:03.577+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:03.590+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:03.590+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:09:03.610+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.226 seconds
