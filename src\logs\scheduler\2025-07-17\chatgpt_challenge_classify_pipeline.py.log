[2025-07-17T21:34:32.354+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:34:32.355+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:34:32.359+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.358+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:34:32.435+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.434+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.445+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:34:32.708+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.708+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:32.722+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.721+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:34:32.744+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.396 seconds
[2025-07-17T21:35:04.280+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:04.282+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:35:04.284+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.283+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:04.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.486+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:04.493+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:04.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.582+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:04.596+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.596+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:35:04.616+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.341 seconds
[2025-07-17T21:35:35.311+0000] {processor.py:186} INFO - Started process (PID=565) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:35.312+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:35:35.315+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.314+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:35.392+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.391+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:35.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:35:35.509+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.509+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:35.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.519+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:35:35.541+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.235 seconds
[2025-07-17T21:36:05.628+0000] {processor.py:186} INFO - Started process (PID=701) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:05.629+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:36:05.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.632+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:05.729+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.729+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:05.739+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:05.863+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.863+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:05.881+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.881+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:36:05.901+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.279 seconds
[2025-07-17T21:36:36.001+0000] {processor.py:186} INFO - Started process (PID=835) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:36.002+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T21:36:36.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.005+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:36.085+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.085+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.092+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T21:36:36.196+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.208+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.208+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T21:36:36.229+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.235 seconds
