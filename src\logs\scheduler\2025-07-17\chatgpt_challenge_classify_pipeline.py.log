[2025-07-17T22:41:31.597+0000] {processor.py:186} INFO - Started process (PID=278) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:41:31.598+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:41:31.600+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.599+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:41:31.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.673+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:31.682+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:41:31.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.914+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.924+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:41:31.942+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.351 seconds
[2025-07-17T22:42:02.274+0000] {processor.py:186} INFO - Started process (PID=407) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:02.275+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:42:02.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:02.345+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.345+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:02.485+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:02.576+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.576+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:02.587+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.587+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:42:02.609+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.340 seconds
[2025-07-17T22:42:33.466+0000] {processor.py:186} INFO - Started process (PID=540) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:33.467+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:42:33.469+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.468+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:33.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.550+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:33.557+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:33.657+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:33.668+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.667+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:42:33.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.227 seconds
[2025-07-17T22:43:03.984+0000] {processor.py:186} INFO - Started process (PID=671) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:03.985+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:43:03.986+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.986+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:04.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.057+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:04.066+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:04.164+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.164+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:04.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.176+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:43:04.193+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.215 seconds
[2025-07-17T22:43:35.235+0000] {processor.py:186} INFO - Started process (PID=802) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:35.236+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:43:35.238+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.237+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:35.310+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.309+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:35.318+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:35.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.423+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:35.436+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.436+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:43:35.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.228 seconds
[2025-07-17T22:44:06.012+0000] {processor.py:186} INFO - Started process (PID=933) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:06.013+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:44:06.016+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:06.090+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.090+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:06.099+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:06.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.199+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:06.210+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:44:06.228+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.222 seconds
[2025-07-17T22:44:36.985+0000] {processor.py:186} INFO - Started process (PID=1064) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:36.986+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:44:36.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:36.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:37.065+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.065+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:37.074+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:37.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:37.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:44:37.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.221 seconds
