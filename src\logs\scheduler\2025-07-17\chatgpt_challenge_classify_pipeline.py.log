[2025-07-17T22:41:31.597+0000] {processor.py:186} INFO - Started process (PID=278) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:41:31.598+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:41:31.600+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.599+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:41:31.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.673+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:31.682+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:41:31.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.914+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.924+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:41:31.942+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.351 seconds
[2025-07-17T22:42:02.274+0000] {processor.py:186} INFO - Started process (PID=407) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:02.275+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:42:02.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:02.345+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.345+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:02.485+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:02.576+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.576+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:02.587+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.587+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:42:02.609+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.340 seconds
[2025-07-17T22:42:33.466+0000] {processor.py:186} INFO - Started process (PID=540) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:33.467+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:42:33.469+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.468+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:33.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.550+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:33.557+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:33.657+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:33.668+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.667+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:42:33.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.227 seconds
[2025-07-17T22:43:03.984+0000] {processor.py:186} INFO - Started process (PID=671) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:03.985+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:43:03.986+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.986+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:04.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.057+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:04.066+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:04.164+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.164+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:04.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.176+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:43:04.193+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.215 seconds
[2025-07-17T22:43:35.235+0000] {processor.py:186} INFO - Started process (PID=802) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:35.236+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:43:35.238+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.237+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:35.310+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.309+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:35.318+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:43:35.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.423+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:35.436+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.436+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:43:35.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.228 seconds
[2025-07-17T22:44:06.012+0000] {processor.py:186} INFO - Started process (PID=933) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:06.013+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:44:06.016+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:06.090+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.090+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:06.099+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:06.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.199+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:06.210+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:44:06.228+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.222 seconds
[2025-07-17T22:44:36.985+0000] {processor.py:186} INFO - Started process (PID=1064) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:36.986+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:44:36.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:36.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:37.065+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.065+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:37.074+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:44:37.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:37.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.179+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:44:37.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.221 seconds
[2025-07-17T22:45:07.461+0000] {processor.py:186} INFO - Started process (PID=1195) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:45:07.462+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:45:07.463+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.463+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:45:07.556+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.555+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:07.565+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:45:07.677+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.677+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:07.690+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.690+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:45:07.711+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.257 seconds
[2025-07-17T22:45:38.753+0000] {processor.py:186} INFO - Started process (PID=1326) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:45:38.754+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:45:38.755+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:38.755+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:45:38.849+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:38.848+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:38.856+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:45:38.964+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:38.964+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:38.976+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:38.976+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:45:38.998+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.252 seconds
[2025-07-17T22:46:09.764+0000] {processor.py:186} INFO - Started process (PID=1457) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:46:09.765+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:46:09.767+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:09.767+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:46:09.849+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:09.848+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:09.857+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:46:09.961+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:09.960+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:09.974+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:09.974+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:46:09.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.238 seconds
[2025-07-17T22:46:40.100+0000] {processor.py:186} INFO - Started process (PID=1594) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:46:40.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:46:40.102+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.102+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:46:40.167+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.167+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:40.177+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:46:40.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.306+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:40.316+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.316+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:46:40.333+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.239 seconds
[2025-07-17T22:47:11.023+0000] {processor.py:186} INFO - Started process (PID=1725) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:47:11.023+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:47:11.025+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.024+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:47:11.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.101+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:11.112+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:47:11.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.216+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:11.229+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.229+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:47:11.250+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.233 seconds
[2025-07-17T22:47:42.013+0000] {processor.py:186} INFO - Started process (PID=1862) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:47:42.014+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:47:42.016+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:47:42.086+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.086+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:42.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:47:42.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.203+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:42.216+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.216+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:47:42.242+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.235 seconds
[2025-07-17T22:48:13.071+0000] {processor.py:186} INFO - Started process (PID=1993) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:48:13.072+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:48:13.073+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.073+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:48:13.138+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.137+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:13.148+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:48:13.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.246+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:13.258+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.258+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:48:13.278+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.212 seconds
[2025-07-17T22:48:44.054+0000] {processor.py:186} INFO - Started process (PID=2124) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:48:44.057+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:48:44.060+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.059+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:48:44.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.335+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:44.351+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:48:44.508+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.507+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:44.525+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.525+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:48:44.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.512 seconds
[2025-07-17T22:49:15.029+0000] {processor.py:186} INFO - Started process (PID=2255) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:49:15.031+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:49:15.033+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:49:15.112+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.112+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:15.122+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:49:15.244+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:15.259+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.258+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:49:15.288+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.267 seconds
[2025-07-17T22:49:45.621+0000] {processor.py:186} INFO - Started process (PID=2386) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:49:45.621+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:49:45.623+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:45.622+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:49:45.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:45.699+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:45.710+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:49:45.821+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:45.821+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:45.833+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:45.833+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:49:45.851+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.236 seconds
[2025-07-17T22:50:16.202+0000] {processor.py:186} INFO - Started process (PID=2517) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:50:16.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:50:16.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.204+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:50:16.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.274+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:16.285+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:50:16.378+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.378+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:16.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.389+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:50:16.409+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.213 seconds
[2025-07-17T22:50:46.931+0000] {processor.py:186} INFO - Started process (PID=2648) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:50:46.932+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:50:46.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:46.933+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:50:47.003+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.003+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:47.011+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:50:47.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.104+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:47.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.117+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:50:47.138+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.213 seconds
[2025-07-17T22:51:17.473+0000] {processor.py:186} INFO - Started process (PID=2779) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:51:17.474+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:51:17.475+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.475+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:51:17.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.540+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:17.549+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:51:17.642+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.642+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:17.652+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.652+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:51:17.670+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.203 seconds
[2025-07-17T22:51:48.355+0000] {processor.py:186} INFO - Started process (PID=2910) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:51:48.356+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:51:48.358+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.357+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:51:48.425+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.425+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:48.433+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:51:48.532+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.531+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:48.542+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.542+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:51:48.559+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.210 seconds
[2025-07-17T22:52:19.007+0000] {processor.py:186} INFO - Started process (PID=3041) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:52:19.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:52:19.009+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:52:19.085+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.085+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:19.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:52:19.207+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.207+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:19.221+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.221+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:52:19.241+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.239 seconds
