[2025-07-17T22:41:31.597+0000] {processor.py:186} INFO - Started process (PID=278) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:41:31.598+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:41:31.600+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.599+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:41:31.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.673+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:31.682+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:41:31.915+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.914+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.924+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:41:31.942+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.351 seconds
[2025-07-17T22:42:02.274+0000] {processor.py:186} INFO - Started process (PID=407) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:02.275+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:42:02.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:02.345+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.345+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:02.485+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:02.576+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.576+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:02.587+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.587+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:42:02.609+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.340 seconds
[2025-07-17T22:42:33.466+0000] {processor.py:186} INFO - Started process (PID=540) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:33.467+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-17T22:42:33.469+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.468+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:33.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.550+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:33.557+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-17T22:42:33.657+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:33.668+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.667+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-17T22:42:33.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.227 seconds
