[2025-07-17T21:34:29.577+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:34:29.578+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:34:29.581+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:34:29.669+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.669+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:29.677+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:34:29.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.779+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:29.944+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.944+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:34:29.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.396 seconds
[2025-07-17T21:35:00.695+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:00.696+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:35:00.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.698+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:00.774+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.774+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:00.782+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:01.023+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.023+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:01.035+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.035+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:35:01.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.369 seconds
[2025-07-17T21:35:31.475+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:31.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:35:31.479+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:31.553+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.552+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:31.562+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:31.668+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.667+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:31.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.680+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:35:31.699+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.230 seconds
[2025-07-17T21:36:01.814+0000] {processor.py:186} INFO - Started process (PID=634) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:01.815+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:36:01.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:01.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.893+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.901+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:01.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:02.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.005+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:36:02.025+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.218 seconds
[2025-07-17T21:36:32.327+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:32.328+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:36:32.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.330+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:32.407+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.406+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:32.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:32.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.502+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:32.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.513+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:36:32.531+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.210 seconds
