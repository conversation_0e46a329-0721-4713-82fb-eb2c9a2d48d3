[2025-07-17T22:41:31.985+0000] {processor.py:186} INFO - Started process (PID=288) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:41:31.986+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:41:31.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:41:31.999+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.998+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:41:32.000+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:41:32.021+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-17T22:42:02.655+0000] {processor.py:186} INFO - Started process (PID=417) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:02.656+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:42:02.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.658+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:02.670+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.669+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:42:02.671+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:02.688+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-17T22:42:33.740+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:33.741+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:42:33.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:33.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.755+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:42:33.757+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:33.775+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
