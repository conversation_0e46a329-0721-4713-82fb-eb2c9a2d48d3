[2025-07-17T22:41:31.985+0000] {processor.py:186} INFO - Started process (PID=288) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:41:31.986+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:41:31.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:41:31.999+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.998+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:41:32.000+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:41:32.021+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-17T22:42:02.655+0000] {processor.py:186} INFO - Started process (PID=417) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:02.656+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:42:02.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.658+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:02.670+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.669+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:42:02.671+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:02.688+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-17T22:42:33.740+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:33.741+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:42:33.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:33.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.755+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:42:33.757+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:42:33.775+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-17T22:43:03.992+0000] {processor.py:186} INFO - Started process (PID=674) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:43:03.993+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:43:03.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.994+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:43:04.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.004+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:43:04.006+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:43:04.024+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.037 seconds
[2025-07-17T22:43:35.498+0000] {processor.py:186} INFO - Started process (PID=812) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:43:35.498+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:43:35.500+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.500+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:43:35.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.512+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:43:35.515+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:43:35.532+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-17T22:44:06.281+0000] {processor.py:186} INFO - Started process (PID=941) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:44:06.282+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:44:06.284+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.284+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:44:06.298+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.296+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:44:06.299+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:44:06.320+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-17T22:44:37.250+0000] {processor.py:186} INFO - Started process (PID=1072) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:44:37.251+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:44:37.253+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.253+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:44:37.266+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.265+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:44:37.267+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:44:37.284+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-17T22:45:07.762+0000] {processor.py:186} INFO - Started process (PID=1200) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:45:07.763+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:45:07.765+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:45:07.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.778+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:45:07.781+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:45:07.799+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-17T22:45:38.762+0000] {processor.py:186} INFO - Started process (PID=1329) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:45:38.764+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:45:38.766+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:38.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:45:38.778+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:38.777+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:45:38.780+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:45:38.799+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-17T22:46:10.039+0000] {processor.py:186} INFO - Started process (PID=1467) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:46:10.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:46:10.042+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.041+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:46:10.055+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.053+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:46:10.055+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:46:10.075+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.043 seconds
[2025-07-17T22:46:40.367+0000] {processor.py:186} INFO - Started process (PID=1604) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:46:40.368+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:46:40.370+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.369+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:46:40.381+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.380+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:46:40.382+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:46:40.398+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.036 seconds
[2025-07-17T22:47:11.291+0000] {processor.py:186} INFO - Started process (PID=1735) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:47:11.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:47:11.294+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:47:11.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.305+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:47:11.307+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:47:11.323+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.037 seconds
[2025-07-17T22:47:42.286+0000] {processor.py:186} INFO - Started process (PID=1872) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:47:42.287+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:47:42.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.288+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:47:42.301+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.300+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:47:42.302+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:47:42.319+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-17T22:48:13.318+0000] {processor.py:186} INFO - Started process (PID=2003) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:48:13.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:48:13.320+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.320+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:48:13.333+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.332+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:48:13.333+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:48:13.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-17T22:48:44.605+0000] {processor.py:186} INFO - Started process (PID=2134) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:48:44.606+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:48:44.608+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.607+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:48:44.622+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.621+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:48:44.623+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:48:44.646+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.048 seconds
[2025-07-17T22:49:15.340+0000] {processor.py:186} INFO - Started process (PID=2265) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:49:15.342+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:49:15.344+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.343+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:49:15.359+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.358+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:49:15.360+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:49:15.378+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-17T22:49:45.885+0000] {processor.py:186} INFO - Started process (PID=2396) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:49:45.886+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:49:45.887+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:45.887+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:49:45.898+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:45.897+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:49:45.899+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:49:45.915+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.035 seconds
[2025-07-17T22:50:16.453+0000] {processor.py:186} INFO - Started process (PID=2527) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:50:16.454+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:50:16.456+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.456+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:50:16.467+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.466+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:50:16.468+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:50:16.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-17T22:50:47.180+0000] {processor.py:186} INFO - Started process (PID=2658) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:50:47.181+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:50:47.182+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.182+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:50:47.194+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.193+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:50:47.195+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:50:47.212+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.038 seconds
[2025-07-17T22:51:17.711+0000] {processor.py:186} INFO - Started process (PID=2789) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:51:17.712+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:51:17.714+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.713+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:51:17.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.727+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:51:17.729+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:51:17.745+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-17T22:51:48.598+0000] {processor.py:186} INFO - Started process (PID=2920) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:51:48.599+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:51:48.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.600+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:51:48.613+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.612+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:51:48.614+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:51:48.631+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-17T22:52:19.276+0000] {processor.py:186} INFO - Started process (PID=3051) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:52:19.276+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:52:19.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.277+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:52:19.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.288+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:52:19.289+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:52:19.305+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.035 seconds
[2025-07-17T22:52:49.723+0000] {processor.py:186} INFO - Started process (PID=3182) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:52:49.724+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:52:49.725+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:49.725+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:52:49.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:49.735+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:52:49.737+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:52:49.753+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.035 seconds
[2025-07-17T22:53:20.623+0000] {processor.py:186} INFO - Started process (PID=3319) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:53:20.624+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:53:20.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:20.625+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:53:20.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:20.636+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:53:20.638+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:53:20.654+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.037 seconds
[2025-07-17T22:53:51.560+0000] {processor.py:186} INFO - Started process (PID=3450) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:53:51.561+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:53:51.562+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:51.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:53:51.575+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:51.574+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:53:51.575+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:53:51.592+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.038 seconds
[2025-07-17T22:54:22.278+0000] {processor.py:186} INFO - Started process (PID=3587) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:54:22.279+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:54:22.280+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:22.280+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:54:22.292+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:22.291+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-17T22:54:22.292+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:54:22.308+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.035 seconds
