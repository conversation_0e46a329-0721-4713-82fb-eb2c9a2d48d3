[2025-07-17T21:34:29.577+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:34:29.578+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:34:29.581+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:34:29.669+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.669+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:29.677+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:34:29.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.779+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:29.944+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.944+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:34:29.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.396 seconds
[2025-07-17T21:35:00.695+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:00.696+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:35:00.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.698+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:00.774+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.774+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:00.782+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:01.023+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.023+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:01.035+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.035+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:35:01.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.369 seconds
[2025-07-17T21:35:31.475+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:31.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:35:31.479+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:31.553+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.552+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:31.562+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:31.668+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.667+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:31.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.680+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:35:31.699+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.230 seconds
[2025-07-17T21:36:01.814+0000] {processor.py:186} INFO - Started process (PID=634) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:01.815+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:36:01.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:01.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.893+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.901+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:01.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:02.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.005+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:36:02.025+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.218 seconds
[2025-07-17T21:36:32.327+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:32.328+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:36:32.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.330+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:32.407+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.406+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:32.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:32.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.502+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:32.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.513+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:36:32.531+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.210 seconds
[2025-07-17T21:37:02.761+0000] {processor.py:186} INFO - Started process (PID=908) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:02.763+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:37:02.766+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:02.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.845+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:02.854+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:02.958+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.958+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:02.969+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.968+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:37:02.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.234 seconds
[2025-07-17T21:37:33.162+0000] {processor.py:186} INFO - Started process (PID=1042) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:33.163+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:37:33.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.165+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:33.245+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.245+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:33.255+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:33.357+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.357+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:33.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.368+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:37:33.387+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.233 seconds
[2025-07-17T21:38:03.660+0000] {processor.py:186} INFO - Started process (PID=1178) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:03.661+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:38:03.664+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.663+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:03.754+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.753+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:03.763+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:03.886+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.885+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:03.896+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.896+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:38:03.918+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.266 seconds
[2025-07-17T21:38:34.191+0000] {processor.py:186} INFO - Started process (PID=1314) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:34.192+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:38:34.194+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.194+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:34.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.273+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:34.282+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:34.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.395+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:34.407+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.407+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:38:34.430+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.246 seconds
[2025-07-17T21:39:05.044+0000] {processor.py:186} INFO - Started process (PID=1450) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:05.045+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:39:05.048+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.048+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:05.128+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.128+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:05.137+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:05.245+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.245+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:05.256+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.256+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:39:05.273+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.234 seconds
[2025-07-17T21:39:35.609+0000] {processor.py:186} INFO - Started process (PID=1586) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:35.610+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:39:35.612+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.612+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:35.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.681+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.690+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:35.785+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.785+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:35.794+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.794+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:39:35.811+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.208 seconds
[2025-07-17T21:40:06.461+0000] {processor.py:186} INFO - Started process (PID=1722) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:06.463+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:40:06.465+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.465+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:06.554+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.554+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:06.563+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:06.687+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.687+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:06.703+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.702+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:40:06.728+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.274 seconds
[2025-07-17T21:40:37.249+0000] {processor.py:186} INFO - Started process (PID=1858) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:37.250+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:40:37.252+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.252+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:37.343+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.343+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:37.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:37.468+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.468+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:37.480+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.480+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:40:37.504+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.262 seconds
[2025-07-17T21:42:56.366+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:42:56.367+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:42:56.370+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.369+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:42:56.446+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.446+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:56.454+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:42:56.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.598+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:56.747+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.747+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:42:56.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.407 seconds
[2025-07-17T21:43:27.238+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:27.239+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:43:27.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.242+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:27.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.330+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:27.341+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:27.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.626+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:27.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.638+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:43:27.657+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.424 seconds
[2025-07-17T21:43:58.515+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:58.516+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:43:58.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.519+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:58.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.606+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:58.619+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:58.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.743+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:58.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.757+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:43:58.779+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.270 seconds
[2025-07-17T21:44:29.092+0000] {processor.py:186} INFO - Started process (PID=642) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:29.093+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:44:29.096+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.096+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:29.180+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.180+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:29.189+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:29.299+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:29.310+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.310+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:44:29.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.245 seconds
[2025-07-17T21:44:59.506+0000] {processor.py:186} INFO - Started process (PID=776) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:59.508+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:44:59.510+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.509+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:59.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.602+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:59.612+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:59.733+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.732+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:59.744+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.744+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:44:59.760+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.261 seconds
