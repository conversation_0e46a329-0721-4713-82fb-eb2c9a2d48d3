[2025-07-17T21:34:29.577+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:34:29.578+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:34:29.581+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:34:29.669+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.669+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:29.677+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:34:29.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.779+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:29.944+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.944+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:34:29.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.396 seconds
[2025-07-17T21:35:00.695+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:00.696+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:35:00.699+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.698+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:00.774+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.774+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:00.782+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:01.023+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.023+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:01.035+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.035+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:35:01.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.369 seconds
[2025-07-17T21:35:31.475+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:31.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:35:31.479+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:31.553+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.552+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:31.562+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:35:31.668+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.667+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:31.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.680+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:35:31.699+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.230 seconds
[2025-07-17T21:36:01.814+0000] {processor.py:186} INFO - Started process (PID=634) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:01.815+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:36:01.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:01.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.893+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.901+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:01.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:02.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.005+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:36:02.025+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.218 seconds
[2025-07-17T21:36:32.327+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:32.328+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:36:32.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.330+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:32.407+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.406+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:32.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:36:32.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.502+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:32.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.513+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:36:32.531+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.210 seconds
[2025-07-17T21:37:02.761+0000] {processor.py:186} INFO - Started process (PID=908) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:02.763+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:37:02.766+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:02.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.845+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:02.854+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:02.958+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.958+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:02.969+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.968+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:37:02.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.234 seconds
[2025-07-17T21:37:33.162+0000] {processor.py:186} INFO - Started process (PID=1042) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:33.163+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:37:33.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.165+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:33.245+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.245+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:33.255+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:37:33.357+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.357+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:33.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.368+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:37:33.387+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.233 seconds
[2025-07-17T21:38:03.660+0000] {processor.py:186} INFO - Started process (PID=1178) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:03.661+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:38:03.664+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.663+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:03.754+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.753+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:03.763+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:03.886+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.885+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:03.896+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.896+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:38:03.918+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.266 seconds
[2025-07-17T21:38:34.191+0000] {processor.py:186} INFO - Started process (PID=1314) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:34.192+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:38:34.194+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.194+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:34.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.273+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:34.282+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:38:34.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.395+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:34.407+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.407+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:38:34.430+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.246 seconds
[2025-07-17T21:39:05.044+0000] {processor.py:186} INFO - Started process (PID=1450) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:05.045+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:39:05.048+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.048+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:05.128+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.128+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:05.137+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:05.245+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.245+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:05.256+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.256+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:39:05.273+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.234 seconds
[2025-07-17T21:39:35.609+0000] {processor.py:186} INFO - Started process (PID=1586) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:35.610+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:39:35.612+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.612+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:35.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.681+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.690+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:39:35.785+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.785+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:35.794+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.794+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:39:35.811+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.208 seconds
[2025-07-17T21:40:06.461+0000] {processor.py:186} INFO - Started process (PID=1722) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:06.463+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:40:06.465+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.465+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:06.554+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.554+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:06.563+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:06.687+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.687+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:06.703+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.702+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:40:06.728+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.274 seconds
[2025-07-17T21:40:37.249+0000] {processor.py:186} INFO - Started process (PID=1858) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:37.250+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:40:37.252+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.252+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:37.343+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.343+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:37.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:40:37.468+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.468+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:37.480+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.480+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:40:37.504+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.262 seconds
[2025-07-17T21:42:56.366+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:42:56.367+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:42:56.370+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.369+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:42:56.446+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.446+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:56.454+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:42:56.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.598+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:56.747+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.747+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:42:56.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.407 seconds
[2025-07-17T21:43:27.238+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:27.239+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:43:27.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.242+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:27.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.330+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:27.341+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:27.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.626+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:27.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.638+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:43:27.657+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.424 seconds
[2025-07-17T21:43:58.515+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:58.516+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:43:58.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.519+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:58.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.606+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:58.619+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:43:58.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.743+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:58.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.757+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:43:58.779+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.270 seconds
[2025-07-17T21:44:29.092+0000] {processor.py:186} INFO - Started process (PID=642) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:29.093+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:44:29.096+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.096+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:29.180+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.180+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:29.189+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:29.299+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:29.310+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.310+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:44:29.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.245 seconds
[2025-07-17T21:44:59.506+0000] {processor.py:186} INFO - Started process (PID=776) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:59.508+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:44:59.510+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.509+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:59.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.602+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:59.612+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:44:59.733+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.732+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:59.744+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.744+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:44:59.760+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.261 seconds
[2025-07-17T21:55:24.312+0000] {processor.py:186} INFO - Started process (PID=227) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:55:24.313+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:55:24.315+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.314+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:55:24.388+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.388+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:24.395+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:55:24.493+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.493+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:24.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.644+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:55:24.662+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.357 seconds
[2025-07-17T21:55:55.070+0000] {processor.py:186} INFO - Started process (PID=363) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:55:55.072+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:55:55.075+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.075+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:55:55.157+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.157+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:55.170+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:55:55.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.442+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:55.451+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.451+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:55:55.471+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.408 seconds
[2025-07-17T21:56:25.817+0000] {processor.py:186} INFO - Started process (PID=501) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:56:25.818+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:56:25.820+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.820+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:56:25.892+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.891+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:25.898+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:56:25.996+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:26.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.006+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:56:26.026+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.215 seconds
[2025-07-17T21:56:56.104+0000] {processor.py:186} INFO - Started process (PID=638) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:56:56.105+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:56:56.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.107+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:56:56.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.176+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:56.184+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:56:56.284+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.284+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:56.294+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.294+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:56:56.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.217 seconds
[2025-07-17T21:57:26.490+0000] {processor.py:186} INFO - Started process (PID=774) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:57:26.491+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:57:26.493+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.493+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:57:26.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.558+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:26.566+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:57:26.653+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.653+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:26.663+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:57:26.681+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.196 seconds
[2025-07-17T21:57:56.929+0000] {processor.py:186} INFO - Started process (PID=910) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:57:56.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:57:56.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.933+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:57:57.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.004+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:57.011+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:57:57.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:57.124+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:57:57.143+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.220 seconds
[2025-07-17T21:58:27.411+0000] {processor.py:186} INFO - Started process (PID=1052) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:58:27.412+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T21:58:27.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.414+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:58:27.501+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.500+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:27.507+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T21:58:27.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.600+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:27.611+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.610+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T21:58:27.630+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.226 seconds
[2025-07-17T22:00:35.899+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:00:35.900+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:00:35.903+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.902+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:00:35.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.977+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:35.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:00:36.081+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.080+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:36.215+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.215+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:00:36.234+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.341 seconds
[2025-07-17T22:01:06.485+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:01:06.486+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:01:06.488+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.488+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:01:06.572+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.571+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:06.773+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:01:06.883+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.883+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:06.895+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.894+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:01:06.914+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.435 seconds
[2025-07-17T22:01:37.165+0000] {processor.py:186} INFO - Started process (PID=500) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:01:37.166+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:01:37.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.168+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:01:37.236+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.235+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:37.244+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:01:37.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.336+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:37.347+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:01:37.364+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.205 seconds
[2025-07-17T22:02:07.460+0000] {processor.py:186} INFO - Started process (PID=634) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:02:07.461+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:02:07.463+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.463+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:02:07.527+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.526+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:07.536+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:02:07.630+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.630+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:07.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:02:07.658+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.203 seconds
[2025-07-17T22:02:37.877+0000] {processor.py:186} INFO - Started process (PID=770) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:02:37.878+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:02:37.881+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.881+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:02:37.950+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.949+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:37.956+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:02:38.043+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.043+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:38.054+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.054+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:02:38.073+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.203 seconds
[2025-07-17T22:03:08.203+0000] {processor.py:186} INFO - Started process (PID=906) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:03:08.204+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:03:08.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:03:08.272+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.272+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:08.280+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:03:08.371+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.370+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:08.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.382+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:03:08.402+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.206 seconds
[2025-07-17T22:03:38.696+0000] {processor.py:186} INFO - Started process (PID=1042) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:03:38.697+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:03:38.700+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.700+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:03:38.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.791+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:38.802+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:03:38.904+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.904+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:38.916+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.916+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:03:38.936+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.247 seconds
[2025-07-17T22:04:09.776+0000] {processor.py:186} INFO - Started process (PID=1178) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:04:09.777+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:04:09.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:04:09.858+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.858+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:09.866+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:04:09.972+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.972+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:09.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.995+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:04:10.021+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.251 seconds
[2025-07-17T22:04:40.106+0000] {processor.py:186} INFO - Started process (PID=1314) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:04:40.107+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:04:40.109+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.108+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:04:40.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.174+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:40.184+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:04:40.280+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.280+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:40.292+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.292+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:04:40.309+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.209 seconds
[2025-07-17T22:05:10.553+0000] {processor.py:186} INFO - Started process (PID=1450) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:05:10.554+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:05:10.556+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:10.556+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:05:10.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:10.626+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:10.637+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:05:10.738+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:10.738+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:10.748+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:10.748+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:05:10.768+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.221 seconds
[2025-07-17T22:05:41.017+0000] {processor.py:186} INFO - Started process (PID=1586) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:05:41.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:05:41.020+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.020+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:05:41.095+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.094+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:41.103+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:05:41.200+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.200+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:41.210+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:05:41.229+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.218 seconds
[2025-07-17T22:06:11.512+0000] {processor.py:186} INFO - Started process (PID=1724) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:06:11.513+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:06:11.515+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:11.514+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:06:11.581+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:11.580+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:11.588+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:06:11.685+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:11.685+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:11.697+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:11.696+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:06:11.715+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.209 seconds
[2025-07-17T22:07:27.405+0000] {processor.py:186} INFO - Started process (PID=220) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:07:27.406+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:07:27.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:27.408+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:07:27.475+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:27.475+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:27.482+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:07:27.605+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:27.605+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:27.786+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:27.786+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:07:27.805+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.406 seconds
[2025-07-17T22:07:58.956+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:07:58.957+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:07:58.960+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:58.959+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:07:59.030+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.030+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:59.039+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:07:59.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.293+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:59.301+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.301+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:07:59.318+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.367 seconds
[2025-07-17T22:08:29.636+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:08:29.637+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:08:29.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:29.639+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:08:29.711+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:29.711+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:29.719+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:08:29.821+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:29.821+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:29.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:29.834+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:08:29.856+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.227 seconds
[2025-07-17T22:09:00.045+0000] {processor.py:186} INFO - Started process (PID=634) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:09:00.046+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:09:00.048+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.048+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:09:00.121+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.121+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:00.132+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:09:00.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.239+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:00.252+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.252+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:09:00.275+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.237 seconds
[2025-07-17T22:09:30.949+0000] {processor.py:186} INFO - Started process (PID=770) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:09:30.950+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:09:30.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.952+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:09:31.031+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.031+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:31.041+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:09:31.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.142+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:31.152+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.152+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:09:31.168+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.225 seconds
[2025-07-17T22:10:01.546+0000] {processor.py:186} INFO - Started process (PID=908) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:10:01.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:10:01.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.549+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:10:01.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.627+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:01.635+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:10:01.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.740+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:01.754+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.753+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:10:01.774+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.233 seconds
[2025-07-17T22:10:31.833+0000] {processor.py:186} INFO - Started process (PID=1042) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:10:31.834+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:10:31.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.835+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:10:31.902+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.902+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:31.911+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:10:32.001+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:32.001+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:32.010+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:32.010+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:10:32.029+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.202 seconds
[2025-07-17T22:11:02.762+0000] {processor.py:186} INFO - Started process (PID=1180) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:11:02.763+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:11:02.765+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:11:02.840+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.840+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:02.848+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:11:02.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.939+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:02.949+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.948+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:11:02.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.211 seconds
[2025-07-17T22:11:33.762+0000] {processor.py:186} INFO - Started process (PID=1314) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:11:33.764+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:11:33.766+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.766+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:11:33.841+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.841+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:33.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:11:33.948+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.948+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:33.959+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.959+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:11:33.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.220 seconds
[2025-07-17T22:12:04.668+0000] {processor.py:186} INFO - Started process (PID=1445) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:12:04.669+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:12:04.670+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.670+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:12:04.747+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.747+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:04.756+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:12:04.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.843+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:04.854+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.853+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:12:04.872+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.210 seconds
[2025-07-17T22:12:35.851+0000] {processor.py:186} INFO - Started process (PID=1576) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:12:35.852+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-17T22:12:35.853+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:35.853+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:12:35.934+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:35.934+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:35.944+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_ask_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-17T22:12:36.050+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:36.050+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:36.065+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:36.065+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_ask_pipeline to None, run_after=None
[2025-07-17T22:12:36.083+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.240 seconds
