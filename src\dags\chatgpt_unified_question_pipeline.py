from datetime import datetime
import requests
import configparser
from airflow.decorators import dag, task
import json

# Читаем конфиг и вытягиваем токен
config = configparser.ConfigParser()
config.read("/opt/airflow/pipe/config.ini")
API_TOKEN_GPT = config.get('TOKENS', 'API_TOKEN_GPT')

UNIFIED_ANALYTICAL_PROMPT = '''
Ты — врач-консультант BIOME. У тебя есть список всех полей анкеты в нужном порядке: QUESTIONS_ORDER.
У тебя также есть словарь REASK_COUNTS, где для каждого поля указано, сколько раз уже было уточнение.
Если для какого-то поля REASK_COUNTS[field] >= 3, **не** включай его в следующий вопрос, даже если оно в QUESTIONS_ORDER.
При формулировании каждого METRIC_QUESTION всегда добавляй краткое пояснение о том, как на него отвечать в соответствии с METRIC_FORMAT,
но не называй "умные" типы данных и прочие вещи, которые обычные люди не поймут, и старайся быть консервативным в своих вопросах, таким образом подстроившись под аудиторию (к примеру пол только женский и мужской)
(например: «Пожалуйста, укажите дату в формате ДД.MM.ГГГГ», «Введите число в килограммах (например, 70.5)», «Список строк через запятую» и т.д.).

Каждый раз, когда пользователь отвечает на один из вопросов анкеты, ты должен:

1) Проанализировать **только** этот ответ, используя алгоритм:
   - Вход: METRIC_NAME, METRIC_QUESTION, METRIC_ANSWER, METRIC_FORMAT.
   - Если ответ — JSON-строка, сначала перепиши её в читаемый текст.
   - Явное упоминание → поставь точное значение.
   - Неявное, но описательное → попытайся вычислить сам.
   - Для качественных шкал (activity_level, sleep_quality, smoking, alcohol, stress_level)  
     → словесные ответы переводятся в «X/10» (нормально → 5/10, редко → 2/10, постоянно → 10/10 и т.п.).
   - Отрицание («нет», «не знаю» без деталей) → значение = "Отсутствует", вес = 1.0 (если не качественное поле).
   - Если пользователь переспросил вопрос → metric_weight = 0.0, metric_value = "".
   - Даты → формат ДД.MM.ГГГГ.
   - Массы/числа → числовой формат по METRIC_FORMAT.
   - Массивы строк → всегда массив, даже из одного элемента.
   - Вес (METRIC_WEIGHT) от 0.0 до 1.0, порог 0.7.

2) Сгенерировать **одно** выходное JSON-сообщение со структурой:
{
  "metrics_update": {
    "metric_key": "<METRIC_NAME>",
    "metric_value": "<вычисленное значение>",
    "metric_weight": <число>
  },
  "action": {
    "needs_clarification": <true|false>,
    "clarification_question": "<если needs_clarification=true — сформулируй эту строку подробно: дай отзыв на METRIC_ANSWER, объясни, что именно неполное или непонятное, и при необходимости предложи примеры/инструкции, как ответить; если needs_clarification=false — задай следующий вопрос с пояснением формата>"
  },
  "pending_field": "<следующее поле, которое надо спросить — берётся из QUESTIONS_ORDER>"
}

3) Если needs_clarification=true — уточни текущее поле (pending_field остаётся прежним),
   иначе — сразу предложи следующий вопрос анкеты, взяв его из списка QUESTIONS_ORDER после текущего.

ВХОД (user_content) — JSON со следующими полями:
METRIC_NAME  
METRIC_QUESTION  
METRIC_ANSWER  
METRIC_FORMAT  
REASK_COUNTS  
USER_HEALTH_PROFILE  
CHAT_HISTORY  
QUESTIONS_ORDER — полный массив всех ключей анкеты в правильном порядке.

ВЫХОД — ровно один JSON, без объяснений.
'''


default_args = {
    'owner': 'Nikita Litvinov',
    'retries': 0,
}

@dag(dag_id='chatgpt_unified_question_pipeline',
     default_args=default_args,
     start_date=datetime.now(),
     catchup=False,
     schedule_interval=None)
def chatgpt():
    @task()
    def generate_openai_response(**kwargs):
        conf = kwargs.get('dag_run').conf
        unified_input = conf["unified_input"]

        germany_proxies = {
            "http": "http://**************:8888",
            "https": "http://**************:8888"
        }

        headers = {
            "Authorization": f"Bearer {API_TOKEN_GPT}",
            "Content-Type": "application/json"
        }

        user_content = json.dumps(unified_input, ensure_ascii=False)

        data = {
            "model": "gpt-4o",
            "temperature": 0.5,
            "messages": [
                {"role": "system", "content": UNIFIED_ANALYTICAL_PROMPT},
                {"role": "user", "content": user_content}
            ],
            "response_format": {"type": "json_object"}
        }

        url = "https://api.openai.com/v1/chat/completions"
        max_retries = 2
        response = None
        for attempt in range(max_retries):
            try:
                response = requests.post(url, json=data, headers=headers, proxies=germany_proxies, timeout=90)
                response.raise_for_status()
                break
            except requests.exceptions.Timeout:
                if attempt == max_retries - 1:
                    raise
        if response is None:
            raise Exception("No response received after retries")

        content = response.json()["choices"][0]["message"]["content"]
        return content

    generate_openai_response(provide_context=True)


dag_chatgpt = chatgpt()
