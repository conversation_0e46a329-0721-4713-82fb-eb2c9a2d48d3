[2025-07-17T22:41:27.254+0000] {processor.py:186} INFO - Started process (PID=192) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:27.255+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:41:27.258+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.257+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:27.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.328+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:27.335+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:27.426+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:27.436+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.436+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:41:27.636+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.389 seconds
[2025-07-17T22:41:58.551+0000] {processor.py:186} INFO - Started process (PID=329) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:58.553+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:41:58.556+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.555+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:58.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.649+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:58.659+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:58.934+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.934+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:58.946+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.945+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:41:58.964+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.419 seconds
[2025-07-17T22:42:29.140+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:42:29.142+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:42:29.143+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.143+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:42:29.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.338+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:29.344+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:42:29.433+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.433+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:29.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.442+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:42:29.458+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.324 seconds
[2025-07-17T22:43:00.090+0000] {processor.py:186} INFO - Started process (PID=591) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:00.091+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:43:00.092+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.092+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:00.164+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.164+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.172+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:00.275+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.274+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:00.287+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.287+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:43:00.306+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.221 seconds
[2025-07-17T22:43:30.475+0000] {processor.py:186} INFO - Started process (PID=722) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:30.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:43:30.477+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.477+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:30.549+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.549+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:30.558+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:30.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.658+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:30.670+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.669+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:43:30.690+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.221 seconds
[2025-07-17T22:44:01.598+0000] {processor.py:186} INFO - Started process (PID=853) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:01.599+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:44:01.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.601+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:01.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.681+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:01.689+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:01.790+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.790+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:01.802+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.801+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:44:01.821+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.230 seconds
[2025-07-17T22:44:32.609+0000] {processor.py:186} INFO - Started process (PID=984) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:32.610+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:44:32.611+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.611+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:32.683+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.683+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:32.692+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:32.787+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.787+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:32.797+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.797+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:44:32.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.214 seconds
[2025-07-17T22:45:03.499+0000] {processor.py:186} INFO - Started process (PID=1115) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:45:03.501+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:45:03.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.502+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:45:03.577+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.576+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:03.586+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:45:03.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.684+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:03.694+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.694+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:45:03.712+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.218 seconds
[2025-07-17T22:45:34.196+0000] {processor.py:186} INFO - Started process (PID=1246) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:45:34.197+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:45:34.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:45:34.271+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.271+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:34.279+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:45:34.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.383+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:34.396+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.395+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:45:34.416+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.227 seconds
[2025-07-17T22:46:05.137+0000] {processor.py:186} INFO - Started process (PID=1377) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:46:05.137+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:46:05.139+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.138+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:46:05.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.209+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:05.218+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:46:05.311+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:05.322+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.321+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:46:05.338+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.207 seconds
[2025-07-17T22:46:35.465+0000] {processor.py:186} INFO - Started process (PID=1508) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:46:35.466+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:46:35.467+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.467+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:46:35.533+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.533+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:35.541+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:46:35.629+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.629+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:35.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.638+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:46:35.657+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.197 seconds
[2025-07-17T22:47:06.551+0000] {processor.py:186} INFO - Started process (PID=1645) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:47:06.551+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:47:06.553+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:47:06.627+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.627+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:06.635+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:47:06.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.728+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:06.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.740+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:47:06.759+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.214 seconds
[2025-07-17T22:47:37.660+0000] {processor.py:186} INFO - Started process (PID=1782) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:47:37.661+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:47:37.663+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.662+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:47:37.741+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.741+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:37.749+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:47:37.853+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.853+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:37.866+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.866+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:47:37.885+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.232 seconds
[2025-07-17T22:48:08.520+0000] {processor.py:186} INFO - Started process (PID=1913) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:48:08.521+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:48:08.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.522+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:48:08.587+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.587+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:08.596+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:48:08.725+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.724+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:08.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.735+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:48:08.755+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.241 seconds
[2025-07-17T22:48:39.506+0000] {processor.py:186} INFO - Started process (PID=2044) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:48:39.507+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:48:39.509+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.508+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:48:39.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.583+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:39.592+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:48:39.698+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.698+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:39.710+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.710+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:48:39.729+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.230 seconds
[2025-07-17T22:49:09.939+0000] {processor.py:186} INFO - Started process (PID=2175) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:49:09.940+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:49:09.942+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:09.941+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:49:10.024+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.024+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:10.033+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:49:10.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.141+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:10.153+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.153+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:49:10.178+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.247 seconds
[2025-07-17T22:49:40.416+0000] {processor.py:186} INFO - Started process (PID=2306) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:49:40.417+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:49:40.419+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.418+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:49:40.483+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.483+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:40.491+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:49:40.578+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.578+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:40.589+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.589+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:49:40.606+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.194 seconds
