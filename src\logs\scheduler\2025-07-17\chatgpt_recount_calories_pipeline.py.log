[2025-07-17T22:41:27.254+0000] {processor.py:186} INFO - Started process (PID=192) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:27.255+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:41:27.258+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.257+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:27.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.328+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:27.335+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:27.426+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:27.436+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.436+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:41:27.636+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.389 seconds
[2025-07-17T22:41:58.551+0000] {processor.py:186} INFO - Started process (PID=329) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:58.553+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:41:58.556+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.555+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:58.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.649+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:58.659+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:41:58.934+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.934+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:58.946+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:58.945+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:41:58.964+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.419 seconds
[2025-07-17T22:42:29.140+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:42:29.142+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:42:29.143+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.143+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:42:29.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.338+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:29.344+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:42:29.433+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.433+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:29.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.442+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:42:29.458+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.324 seconds
[2025-07-17T22:43:00.090+0000] {processor.py:186} INFO - Started process (PID=591) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:00.091+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:43:00.092+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.092+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:00.164+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.164+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.172+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:00.275+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.274+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:00.287+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.287+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:43:00.306+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.221 seconds
[2025-07-17T22:43:30.475+0000] {processor.py:186} INFO - Started process (PID=722) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:30.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:43:30.477+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.477+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:30.549+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.549+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:30.558+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:43:30.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.658+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:30.670+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.669+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:43:30.690+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.221 seconds
[2025-07-17T22:44:01.598+0000] {processor.py:186} INFO - Started process (PID=853) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:01.599+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:44:01.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.601+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:01.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.681+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:01.689+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:01.790+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.790+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:01.802+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.801+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:44:01.821+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.230 seconds
[2025-07-17T22:44:32.609+0000] {processor.py:186} INFO - Started process (PID=984) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:32.610+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:44:32.611+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.611+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:32.683+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.683+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:32.692+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:44:32.787+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.787+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:32.797+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.797+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:44:32.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.214 seconds
