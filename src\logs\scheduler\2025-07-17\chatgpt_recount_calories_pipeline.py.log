[2025-07-17T21:34:30.386+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:34:30.387+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:34:30.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.389+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:34:30.487+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.487+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:30.498+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:34:30.787+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.787+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:30.798+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.797+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:34:30.822+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.441 seconds
[2025-07-17T21:35:01.732+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:01.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:35:01.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.736+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:01.829+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.828+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:01.997+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:02.093+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.093+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:02.102+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.102+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:35:02.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.396 seconds
[2025-07-17T21:35:32.332+0000] {processor.py:186} INFO - Started process (PID=518) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:32.333+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:35:32.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.335+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:32.420+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.419+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:32.429+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:32.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.557+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:35:32.576+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.250 seconds
[2025-07-17T21:36:02.814+0000] {processor.py:186} INFO - Started process (PID=656) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:02.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:36:02.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:02.890+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.890+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:02.897+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:03.003+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.003+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.014+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.013+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:36:03.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.226 seconds
[2025-07-17T21:36:33.101+0000] {processor.py:186} INFO - Started process (PID=792) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:33.102+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:36:33.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:33.173+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.173+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:33.182+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:33.279+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.279+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:33.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.289+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:36:33.305+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.210 seconds
[2025-07-17T21:37:03.572+0000] {processor.py:186} INFO - Started process (PID=928) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:03.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:37:03.576+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.576+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:03.653+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.653+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:03.662+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:03.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:03.768+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.768+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:37:03.787+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.221 seconds
[2025-07-17T21:37:33.936+0000] {processor.py:186} INFO - Started process (PID=1064) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:33.937+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:37:33.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.939+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:34.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:34.007+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:34.015+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:34.111+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:34.111+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:34.121+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:34.120+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:37:34.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.208 seconds
[2025-07-17T21:38:04.432+0000] {processor.py:186} INFO - Started process (PID=1198) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:04.433+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:38:04.435+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.435+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:04.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.521+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:04.531+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:04.641+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.641+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:04.653+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.652+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:38:04.672+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.245 seconds
[2025-07-17T21:38:35.010+0000] {processor.py:186} INFO - Started process (PID=1334) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:35.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:38:35.014+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:35.088+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.087+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:35.098+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:35.197+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.197+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:35.210+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:38:35.231+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.227 seconds
[2025-07-17T21:39:05.801+0000] {processor.py:186} INFO - Started process (PID=1472) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:05.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:39:05.806+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.805+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:05.879+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.878+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:05.888+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:05.981+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.981+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:05.990+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.990+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:39:06.008+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.213 seconds
[2025-07-17T21:39:36.136+0000] {processor.py:186} INFO - Started process (PID=1606) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:36.138+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:39:36.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.140+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:36.230+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.230+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:36.238+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:36.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.336+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:36.347+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.347+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:39:36.369+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.241 seconds
[2025-07-17T21:40:07.086+0000] {processor.py:186} INFO - Started process (PID=1742) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:07.087+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:40:07.091+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.091+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:07.191+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.190+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:07.201+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:07.348+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:07.364+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.363+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:40:07.390+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.311 seconds
[2025-07-17T21:40:38.167+0000] {processor.py:186} INFO - Started process (PID=1878) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:38.168+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:40:38.170+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.169+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:38.256+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.255+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:38.265+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:38.378+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.378+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:38.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.389+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:40:38.409+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.248 seconds
[2025-07-17T21:42:57.243+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:42:57.244+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:42:57.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.246+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:42:57.332+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.331+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:57.341+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:42:57.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:57.645+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.645+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:42:57.664+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.426 seconds
[2025-07-17T21:43:28.202+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:28.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:43:28.207+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:28.291+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.290+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:28.449+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:28.564+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:28.575+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.575+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:43:28.599+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.403 seconds
[2025-07-17T21:43:59.137+0000] {processor.py:186} INFO - Started process (PID=526) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:59.139+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:43:59.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.141+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:59.217+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.217+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:59.223+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:59.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.352+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.351+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:43:59.372+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.241 seconds
[2025-07-17T21:44:29.964+0000] {processor.py:186} INFO - Started process (PID=662) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:44:29.965+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:44:29.968+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.967+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:44:30.065+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.065+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.078+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:44:30.205+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.204+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:30.218+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.218+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:44:30.240+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.283 seconds
[2025-07-17T21:45:01.135+0000] {processor.py:186} INFO - Started process (PID=798) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:45:01.136+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:45:01.139+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.139+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:45:01.235+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.235+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:01.244+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:45:01.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:01.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.384+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:45:01.404+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.274 seconds
