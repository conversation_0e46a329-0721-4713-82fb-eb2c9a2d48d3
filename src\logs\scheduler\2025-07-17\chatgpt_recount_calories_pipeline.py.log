[2025-07-17T21:34:30.386+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:34:30.387+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:34:30.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.389+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:34:30.487+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.487+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:30.498+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:34:30.787+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.787+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:30.798+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.797+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:34:30.822+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.441 seconds
[2025-07-17T21:35:01.732+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:01.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:35:01.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.736+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:01.829+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.828+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:01.997+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:02.093+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.093+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:02.102+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.102+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:35:02.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.396 seconds
[2025-07-17T21:35:32.332+0000] {processor.py:186} INFO - Started process (PID=518) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:32.333+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:35:32.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.335+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:32.420+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.419+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:32.429+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:35:32.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.557+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:35:32.576+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.250 seconds
[2025-07-17T21:36:02.814+0000] {processor.py:186} INFO - Started process (PID=656) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:02.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:36:02.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:02.890+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.890+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:02.897+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:03.003+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.003+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.014+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.013+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:36:03.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.226 seconds
[2025-07-17T21:36:33.101+0000] {processor.py:186} INFO - Started process (PID=792) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:33.102+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:36:33.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:33.173+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.173+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:33.182+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:36:33.279+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.279+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:33.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:33.289+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:36:33.305+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.210 seconds
[2025-07-17T21:37:03.572+0000] {processor.py:186} INFO - Started process (PID=928) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:03.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:37:03.576+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.576+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:03.653+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.653+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:03.662+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:03.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:03.768+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.768+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:37:03.787+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.221 seconds
[2025-07-17T21:37:33.936+0000] {processor.py:186} INFO - Started process (PID=1064) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:33.937+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:37:33.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.939+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:34.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:34.007+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:34.015+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:37:34.111+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:34.111+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:34.121+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:34.120+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:37:34.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.208 seconds
[2025-07-17T21:38:04.432+0000] {processor.py:186} INFO - Started process (PID=1198) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:04.433+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:38:04.435+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.435+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:04.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.521+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:04.531+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:04.641+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.641+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:04.653+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.652+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:38:04.672+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.245 seconds
[2025-07-17T21:38:35.010+0000] {processor.py:186} INFO - Started process (PID=1334) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:35.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:38:35.014+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:35.088+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.087+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:35.098+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:38:35.197+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.197+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:35.210+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:35.210+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:38:35.231+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.227 seconds
[2025-07-17T21:39:05.801+0000] {processor.py:186} INFO - Started process (PID=1472) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:05.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:39:05.806+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.805+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:05.879+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.878+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:05.888+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:05.981+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.981+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:05.990+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.990+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:39:06.008+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.213 seconds
[2025-07-17T21:39:36.136+0000] {processor.py:186} INFO - Started process (PID=1606) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:36.138+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:39:36.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.140+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:36.230+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.230+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:36.238+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:39:36.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.336+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:36.347+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:36.347+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:39:36.369+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.241 seconds
[2025-07-17T21:40:07.086+0000] {processor.py:186} INFO - Started process (PID=1742) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:07.087+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:40:07.091+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.091+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:07.191+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.190+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:07.201+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:07.348+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:07.364+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.363+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:40:07.390+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.311 seconds
[2025-07-17T21:40:38.167+0000] {processor.py:186} INFO - Started process (PID=1878) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:38.168+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:40:38.170+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.169+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:38.256+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.255+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:38.265+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:40:38.378+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.378+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:38.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:38.389+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:40:38.409+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.248 seconds
[2025-07-17T21:42:57.243+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:42:57.244+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:42:57.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.246+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:42:57.332+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.331+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:57.341+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:42:57.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:57.645+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.645+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:42:57.664+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.426 seconds
[2025-07-17T21:43:28.202+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:28.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:43:28.207+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:28.291+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.290+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:28.449+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:28.564+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:28.575+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.575+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:43:28.599+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.403 seconds
[2025-07-17T21:43:59.137+0000] {processor.py:186} INFO - Started process (PID=526) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:59.139+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:43:59.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.141+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:59.217+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.217+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:59.223+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:43:59.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.352+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.351+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:43:59.372+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.241 seconds
[2025-07-17T21:44:29.964+0000] {processor.py:186} INFO - Started process (PID=662) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:44:29.965+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:44:29.968+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.967+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:44:30.065+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.065+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.078+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:44:30.205+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.204+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:30.218+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.218+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:44:30.240+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.283 seconds
[2025-07-17T21:45:01.135+0000] {processor.py:186} INFO - Started process (PID=798) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:45:01.136+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:45:01.139+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.139+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:45:01.235+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.235+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:01.244+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:45:01.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:01.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.384+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:45:01.404+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.274 seconds
[2025-07-17T21:55:25.111+0000] {processor.py:186} INFO - Started process (PID=247) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:55:25.112+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:55:25.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.115+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:55:25.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.195+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:25.202+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:55:25.497+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.497+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:25.507+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.507+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:55:25.527+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.422 seconds
[2025-07-17T21:55:55.926+0000] {processor.py:186} INFO - Started process (PID=383) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:55:55.927+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:55:55.929+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.929+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:55:56.000+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.000+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:56.132+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:55:56.222+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.222+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:56.232+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.232+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:55:56.250+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.331 seconds
[2025-07-17T21:56:26.336+0000] {processor.py:186} INFO - Started process (PID=519) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:56:26.337+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:56:26.339+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.339+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:56:26.413+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.413+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:26.422+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:56:26.533+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.533+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:26.543+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.543+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:56:26.559+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.228 seconds
[2025-07-17T21:56:56.812+0000] {processor.py:186} INFO - Started process (PID=658) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:56:56.813+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:56:56.816+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.815+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:56:56.886+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.886+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:56.894+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:56:57.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.004+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:57.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.014+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:56:57.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.228 seconds
[2025-07-17T21:57:27.174+0000] {processor.py:186} INFO - Started process (PID=800) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:57:27.174+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:57:27.177+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:27.176+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:57:27.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:27.239+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:27.247+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:57:27.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:27.335+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:27.345+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:27.345+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:57:27.367+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.199 seconds
[2025-07-17T21:57:57.766+0000] {processor.py:186} INFO - Started process (PID=936) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:57:57.767+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:57:57.769+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.769+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:57:57.850+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.850+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:57.858+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:57:57.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.962+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:57.974+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.974+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:57:57.996+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.237 seconds
[2025-07-17T21:58:28.165+0000] {processor.py:186} INFO - Started process (PID=1072) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:58:28.165+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T21:58:28.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:28.167+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:58:28.234+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:28.233+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:28.242+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T21:58:28.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:28.340+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:28.352+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:28.352+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T21:58:28.372+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.213 seconds
[2025-07-17T22:00:36.670+0000] {processor.py:186} INFO - Started process (PID=248) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:00:36.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:00:36.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:00:36.739+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.739+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:36.746+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:00:36.998+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.998+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:37.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.007+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:00:37.024+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.359 seconds
[2025-07-17T22:01:07.414+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:01:07.415+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:01:07.418+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.417+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:01:07.663+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.663+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:07.668+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:01:07.786+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.785+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:07.800+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.800+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:01:07.826+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.417 seconds
[2025-07-17T22:01:37.896+0000] {processor.py:186} INFO - Started process (PID=520) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:01:37.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:01:37.899+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:01:37.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.965+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:37.973+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:01:38.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:38.064+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:38.074+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:38.074+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:01:38.093+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.202 seconds
[2025-07-17T22:02:08.170+0000] {processor.py:186} INFO - Started process (PID=656) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:02:08.171+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:02:08.173+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:08.172+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:02:08.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:08.239+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:08.248+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:02:08.334+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:08.334+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:08.343+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:08.343+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:02:08.359+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.195 seconds
[2025-07-17T22:02:38.564+0000] {processor.py:186} INFO - Started process (PID=790) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:02:38.565+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:02:38.567+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.567+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:02:38.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.632+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:38.641+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:02:38.724+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.724+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:38.734+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.734+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:02:38.750+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.192 seconds
[2025-07-17T22:03:08.897+0000] {processor.py:186} INFO - Started process (PID=926) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:03:08.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:03:08.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:03:08.964+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.964+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:08.972+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:03:09.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:09.057+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:09.067+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:09.067+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:03:09.084+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.193 seconds
[2025-07-17T22:03:39.444+0000] {processor.py:186} INFO - Started process (PID=1064) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:03:39.445+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:03:39.447+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.446+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:03:39.516+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.515+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:39.524+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:03:39.620+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.620+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:39.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.631+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:03:39.648+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.210 seconds
[2025-07-17T22:04:10.317+0000] {processor.py:186} INFO - Started process (PID=1198) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:04:10.318+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:04:10.320+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.320+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:04:10.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.395+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:10.403+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:04:10.508+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.508+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:10.518+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.517+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:04:10.535+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.224 seconds
[2025-07-17T22:04:40.839+0000] {processor.py:186} INFO - Started process (PID=1334) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:04:40.840+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:04:40.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.842+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:04:40.912+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.912+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:40.919+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:04:41.014+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:41.014+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:41.025+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:41.025+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:04:41.040+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.206 seconds
[2025-07-17T22:05:11.302+0000] {processor.py:186} INFO - Started process (PID=1472) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:05:11.303+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:05:11.305+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.305+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:05:11.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.374+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:11.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:05:11.497+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.496+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:11.506+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:11.506+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:05:11.525+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.229 seconds
[2025-07-17T22:05:41.611+0000] {processor.py:186} INFO - Started process (PID=1606) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:05:41.612+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:05:41.615+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.614+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:05:41.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.742+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:41.753+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:05:41.868+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.867+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:41.877+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:41.877+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:05:41.900+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.296 seconds
[2025-07-17T22:06:12.202+0000] {processor.py:186} INFO - Started process (PID=1742) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:06:12.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:06:12.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:06:12.272+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.272+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:12.280+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:06:12.366+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:12.376+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:12.375+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:06:12.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.196 seconds
[2025-07-17T22:07:28.259+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:07:28.260+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:07:28.263+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.262+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:07:28.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.330+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:28.336+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:07:28.576+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.576+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:28.586+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.586+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:07:28.603+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.349 seconds
[2025-07-17T22:07:59.722+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:07:59.723+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:07:59.725+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.725+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:07:59.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:59.805+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:59.950+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:08:00.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.044+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:00.052+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.052+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:08:00.071+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.355 seconds
[2025-07-17T22:08:30.431+0000] {processor.py:186} INFO - Started process (PID=518) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:08:30.432+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:08:30.435+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.435+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:08:30.516+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.515+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:30.524+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:08:30.651+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.651+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:30.662+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.662+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:08:30.681+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.256 seconds
[2025-07-17T22:09:00.856+0000] {processor.py:186} INFO - Started process (PID=654) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:09:00.857+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-17T22:09:00.859+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.859+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:09:00.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:00.923+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:00.932+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-17T22:09:01.028+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.027+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:01.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.037+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-17T22:09:01.052+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.202 seconds
