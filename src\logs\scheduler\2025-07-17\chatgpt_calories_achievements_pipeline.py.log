[2025-07-17T21:34:33.048+0000] {processor.py:186} INFO - Started process (PID=306) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:34:33.049+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:34:33.052+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.051+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:34:33.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.131+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:33.139+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:34:33.363+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.363+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:33.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.375+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:34:33.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.354 seconds
[2025-07-17T21:35:05.037+0000] {processor.py:186} INFO - Started process (PID=444) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:05.038+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:35:05.040+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:05.244+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.244+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:05.250+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:05.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.338+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:05.346+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:35:05.364+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.333 seconds
[2025-07-17T21:35:35.875+0000] {processor.py:186} INFO - Started process (PID=580) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:35.876+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:35:35.879+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.879+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:35.950+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.950+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:35.959+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:36.061+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:36.061+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:36.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:36.081+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:35:36.105+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.237 seconds
[2025-07-17T21:36:06.239+0000] {processor.py:186} INFO - Started process (PID=716) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:06.240+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:36:06.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.242+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:06.322+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.321+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:06.330+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:06.431+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.431+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:06.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.441+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:36:06.466+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.233 seconds
[2025-07-17T21:36:36.564+0000] {processor.py:186} INFO - Started process (PID=855) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:36.565+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:36:36.568+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.568+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:36.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.643+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.652+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:36.778+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.778+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.792+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:36:36.813+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.255 seconds
