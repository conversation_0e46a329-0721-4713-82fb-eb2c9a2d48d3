[2025-07-17T21:34:33.048+0000] {processor.py:186} INFO - Started process (PID=306) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:34:33.049+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:34:33.052+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.051+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:34:33.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.131+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:33.139+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:34:33.363+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.363+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:33.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.375+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:34:33.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.354 seconds
[2025-07-17T21:35:05.037+0000] {processor.py:186} INFO - Started process (PID=444) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:05.038+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:35:05.040+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:05.244+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.244+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:05.250+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:05.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.338+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:05.346+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:35:05.364+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.333 seconds
[2025-07-17T21:35:35.875+0000] {processor.py:186} INFO - Started process (PID=580) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:35.876+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:35:35.879+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.879+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:35.950+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.950+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:35.959+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:36.061+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:36.061+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:36.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:36.081+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:35:36.105+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.237 seconds
[2025-07-17T21:36:06.239+0000] {processor.py:186} INFO - Started process (PID=716) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:06.240+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:36:06.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.242+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:06.322+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.321+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:06.330+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:06.431+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.431+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:06.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.441+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:36:06.466+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.233 seconds
[2025-07-17T21:36:36.564+0000] {processor.py:186} INFO - Started process (PID=855) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:36.565+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:36:36.568+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.568+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:36.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.643+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.652+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:36.778+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.778+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.792+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:36:36.813+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.255 seconds
[2025-07-17T21:37:07.156+0000] {processor.py:186} INFO - Started process (PID=988) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:07.158+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:37:07.161+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.161+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:07.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.243+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:07.252+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:07.357+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.356+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:07.369+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:37:07.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.250 seconds
[2025-07-17T21:37:37.681+0000] {processor.py:186} INFO - Started process (PID=1122) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:37.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:37:37.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:37.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.762+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:37.771+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:37.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.884+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:37.897+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.897+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:37:37.919+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.245 seconds
[2025-07-17T21:38:08.231+0000] {processor.py:186} INFO - Started process (PID=1258) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:08.232+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:38:08.234+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.234+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:08.305+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.305+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:08.314+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:08.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.417+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:08.428+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.427+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:38:08.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.222 seconds
[2025-07-17T21:38:39.085+0000] {processor.py:186} INFO - Started process (PID=1394) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:39.086+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:38:39.089+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.089+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:39.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.174+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:39.182+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:39.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.289+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:39.301+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.301+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:38:39.321+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.241 seconds
[2025-07-17T21:39:09.542+0000] {processor.py:186} INFO - Started process (PID=1530) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:09.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:39:09.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.544+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:09.621+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.621+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:09.631+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:09.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:09.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.742+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:39:09.764+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.228 seconds
[2025-07-17T21:39:40.174+0000] {processor.py:186} INFO - Started process (PID=1666) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:40.175+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:39:40.178+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.177+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:40.266+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.265+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:40.274+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:40.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:40.397+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.397+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:39:40.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.254 seconds
[2025-07-17T21:40:11.148+0000] {processor.py:186} INFO - Started process (PID=1802) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:11.149+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:40:11.153+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.152+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:11.244+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.243+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:11.251+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:11.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.373+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:11.385+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.384+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:40:11.405+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.265 seconds
[2025-07-17T21:40:42.146+0000] {processor.py:186} INFO - Started process (PID=1938) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:42.147+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:40:42.150+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.149+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:42.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.237+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:42.246+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:42.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.359+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:42.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.384+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:40:42.422+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.283 seconds
[2025-07-17T21:42:59.925+0000] {processor.py:186} INFO - Started process (PID=312) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:42:59.926+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:42:59.928+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.928+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:43:00.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:00.005+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:00.013+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:43:00.253+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:00.252+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:00.266+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:00.265+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:43:00.289+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.371 seconds
[2025-07-17T21:43:31.129+0000] {processor.py:186} INFO - Started process (PID=453) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:43:31.130+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:43:31.133+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.132+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:43:31.331+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.330+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:31.337+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:43:31.440+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.440+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:31.450+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.449+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:43:31.468+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.345 seconds
[2025-07-17T21:44:01.739+0000] {processor.py:186} INFO - Started process (PID=591) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:01.741+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:44:01.745+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.744+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:01.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.843+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:01.851+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:01.960+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.960+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:01.973+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.972+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:44:01.991+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.261 seconds
[2025-07-17T21:44:32.209+0000] {processor.py:186} INFO - Started process (PID=727) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:32.210+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:44:32.214+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:32.213+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:32.321+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:32.321+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:32.329+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:32.444+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:32.444+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:32.455+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:32.455+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:44:32.475+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.273 seconds
[2025-07-17T21:45:02.868+0000] {processor.py:186} INFO - Started process (PID=856) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:45:02.870+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:45:02.872+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.872+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:45:02.974+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.974+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:02.982+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:45:03.093+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:03.093+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:03.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:03.107+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:45:03.127+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.265 seconds
[2025-07-17T21:55:27.607+0000] {processor.py:186} INFO - Started process (PID=313) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:55:27.608+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:55:27.610+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.610+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:55:27.678+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.678+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:27.687+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:55:27.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.906+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:27.918+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.917+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:55:27.939+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.338 seconds
[2025-07-17T21:55:58.241+0000] {processor.py:186} INFO - Started process (PID=449) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:55:58.242+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:55:58.245+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.245+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:55:58.446+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.445+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:58.453+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:55:58.547+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.547+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:58.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:55:58.575+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.340 seconds
[2025-07-17T21:56:28.930+0000] {processor.py:186} INFO - Started process (PID=586) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:56:28.931+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:56:28.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:56:29.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:29.007+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:29.015+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:56:29.134+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:29.134+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:29.147+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:29.147+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:56:29.166+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.242 seconds
[2025-07-17T21:56:59.300+0000] {processor.py:186} INFO - Started process (PID=722) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:56:59.301+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:56:59.304+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:59.304+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:56:59.382+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:59.382+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:59.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:56:59.514+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:59.513+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:59.525+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:59.525+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:56:59.545+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.254 seconds
[2025-07-17T21:57:29.779+0000] {processor.py:186} INFO - Started process (PID=858) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:57:29.780+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:57:29.782+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.781+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:57:29.846+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.846+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:29.855+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:57:29.968+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.968+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:29.979+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.979+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:57:29.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.222 seconds
[2025-07-17T21:58:00.267+0000] {processor.py:186} INFO - Started process (PID=994) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:58:00.268+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:58:00.270+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.270+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:58:00.339+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.338+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:00.345+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:58:00.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.443+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:00.453+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.453+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:58:00.473+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.212 seconds
[2025-07-17T21:58:30.567+0000] {processor.py:186} INFO - Started process (PID=1130) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:58:30.568+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:58:30.570+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.570+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:58:30.642+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.642+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:30.650+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:58:30.754+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:30.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.763+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:58:30.780+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.219 seconds
[2025-07-17T22:00:39.136+0000] {processor.py:186} INFO - Started process (PID=306) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:00:39.138+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:00:39.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:39.141+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:00:39.222+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:39.221+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:39.229+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:00:39.444+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:39.444+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:39.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:39.454+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:00:39.472+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.344 seconds
[2025-07-17T22:01:10.577+0000] {processor.py:186} INFO - Started process (PID=444) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:01:10.579+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:01:10.582+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.581+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:01:10.803+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.803+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:10.811+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:01:10.902+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.901+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:10.914+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.914+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:01:10.931+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.361 seconds
[2025-07-17T22:01:41.327+0000] {processor.py:186} INFO - Started process (PID=578) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:01:41.328+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:01:41.331+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.330+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:01:41.411+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.411+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:41.419+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:01:41.523+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.522+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:41.535+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.535+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:01:41.556+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.235 seconds
[2025-07-17T22:02:11.965+0000] {processor.py:186} INFO - Started process (PID=716) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:02:11.966+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:02:11.968+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:11.968+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:02:12.037+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:12.036+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:12.046+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:02:12.147+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:12.147+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:12.156+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:12.156+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:02:12.174+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.215 seconds
[2025-07-17T22:02:42.357+0000] {processor.py:186} INFO - Started process (PID=852) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:02:42.358+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:02:42.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.360+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:02:42.424+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.424+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:42.433+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:02:42.519+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.519+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:42.528+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.528+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:02:42.545+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.193 seconds
[2025-07-17T22:03:12.773+0000] {processor.py:186} INFO - Started process (PID=988) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:03:12.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:03:12.776+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.776+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:03:12.842+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.842+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:12.850+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:03:12.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.939+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:12.949+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.949+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:03:12.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.200 seconds
[2025-07-17T22:03:43.832+0000] {processor.py:186} INFO - Started process (PID=1122) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:03:43.833+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:03:43.837+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.836+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:03:43.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.930+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:43.937+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:03:44.061+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:44.061+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:44.076+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:44.076+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:03:44.100+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.276 seconds
[2025-07-17T22:04:14.273+0000] {processor.py:186} INFO - Started process (PID=1258) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:04:14.275+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:04:14.277+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:14.277+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:04:14.350+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:14.350+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:14.358+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:04:14.457+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:14.456+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:14.468+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:14.468+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:04:14.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.219 seconds
[2025-07-17T22:04:44.663+0000] {processor.py:186} INFO - Started process (PID=1394) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:04:44.664+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:04:44.667+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.666+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:04:44.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.743+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:44.757+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:04:44.869+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.869+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:44.882+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.882+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:04:44.907+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.249 seconds
[2025-07-17T22:05:15.201+0000] {processor.py:186} INFO - Started process (PID=1532) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:05:15.202+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:05:15.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:15.204+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:05:15.267+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:15.266+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:15.275+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:05:15.364+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:15.364+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:15.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:15.374+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:05:15.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.196 seconds
[2025-07-17T22:05:45.754+0000] {processor.py:186} INFO - Started process (PID=1668) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:05:45.756+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:05:45.759+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.758+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:05:45.833+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.833+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:45.842+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:05:45.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.937+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:45.947+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.947+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:05:45.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.219 seconds
[2025-07-17T22:06:16.129+0000] {processor.py:186} INFO - Started process (PID=1804) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:06:16.130+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:06:16.132+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:16.132+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:06:16.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:16.203+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:16.211+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:06:16.308+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:16.307+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:16.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:16.318+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:06:16.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.220 seconds
[2025-07-17T22:07:30.560+0000] {processor.py:186} INFO - Started process (PID=306) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:07:30.561+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:07:30.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.563+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:07:30.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.639+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:30.646+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:07:30.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.851+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:30.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.860+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:07:30.877+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.322 seconds
[2025-07-17T22:08:02.338+0000] {processor.py:186} INFO - Started process (PID=442) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:08:02.339+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:08:02.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:02.341+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:08:02.537+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:02.537+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:02.543+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:08:02.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:02.627+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:02.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:02.635+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:08:02.652+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.319 seconds
[2025-07-17T22:08:33.274+0000] {processor.py:186} INFO - Started process (PID=578) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:08:33.275+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:08:33.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:33.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:08:33.369+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:33.369+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:33.376+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:08:33.499+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:33.498+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:33.515+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:33.514+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:08:33.540+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.274 seconds
[2025-07-17T22:09:04.169+0000] {processor.py:186} INFO - Started process (PID=716) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:09:04.170+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:09:04.173+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:04.173+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:09:04.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:04.246+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:04.256+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:09:04.351+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:04.350+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:04.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:04.360+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:09:04.378+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.216 seconds
