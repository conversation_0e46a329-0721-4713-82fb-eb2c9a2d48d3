[2025-07-17T22:41:27.689+0000] {processor.py:186} INFO - Started process (PID=200) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:27.690+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:41:27.692+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.692+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:27.774+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.774+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:27.781+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:27.914+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.913+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:28.160+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.160+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:41:28.185+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.502 seconds
[2025-07-17T22:41:59.008+0000] {processor.py:186} INFO - Started process (PID=337) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:59.009+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:41:59.012+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.011+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:59.087+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.087+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:59.313+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.312+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:59.325+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.325+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:41:59.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.349 seconds
[2025-07-17T22:42:29.870+0000] {processor.py:186} INFO - Started process (PID=470) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:42:29.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:42:29.873+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.872+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:42:30.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.082+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.090+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:42:30.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.187+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.197+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.197+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:42:30.217+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.352 seconds
[2025-07-17T22:43:00.367+0000] {processor.py:186} INFO - Started process (PID=599) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:00.368+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:43:00.370+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.370+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:00.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.441+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.451+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:00.553+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.553+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:00.565+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.565+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:43:00.584+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.223 seconds
[2025-07-17T22:43:30.754+0000] {processor.py:186} INFO - Started process (PID=730) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:30.755+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:43:30.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.756+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:30.829+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.829+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:30.839+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:30.945+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.945+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:30.956+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.956+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:43:30.977+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.229 seconds
[2025-07-17T22:44:01.872+0000] {processor.py:186} INFO - Started process (PID=861) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:01.873+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:44:01.875+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.875+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:01.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.953+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:01.960+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:02.073+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.072+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.084+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.083+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:44:02.104+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.238 seconds
[2025-07-17T22:44:32.870+0000] {processor.py:186} INFO - Started process (PID=992) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:32.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:44:32.872+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.872+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:32.940+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.939+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:32.948+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:33.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.040+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.053+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.053+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:44:33.074+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.210 seconds
[2025-07-17T22:45:03.770+0000] {processor.py:186} INFO - Started process (PID=1123) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:45:03.771+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:45:03.772+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.772+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:45:03.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.843+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:03.853+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:45:03.955+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.954+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:03.967+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:03.967+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:45:03.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.223 seconds
[2025-07-17T22:45:34.481+0000] {processor.py:186} INFO - Started process (PID=1254) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:45:34.482+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:45:34.483+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:45:34.559+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.559+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:34.567+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:45:34.665+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.665+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:34.678+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:34.678+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:45:34.701+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.227 seconds
[2025-07-17T22:46:05.384+0000] {processor.py:186} INFO - Started process (PID=1385) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:46:05.385+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:46:05.386+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.385+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:46:05.457+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.457+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:05.465+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:46:05.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.557+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:05.570+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.569+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:46:05.589+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.211 seconds
[2025-07-17T22:46:35.712+0000] {processor.py:186} INFO - Started process (PID=1516) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:46:35.713+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:46:35.714+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.713+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:46:35.783+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.783+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:35.792+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:46:35.889+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.889+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:35.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:35.900+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:46:35.918+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.212 seconds
[2025-07-17T22:47:06.804+0000] {processor.py:186} INFO - Started process (PID=1653) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:47:06.805+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:47:06.807+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.806+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:47:06.873+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.873+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:06.881+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:47:06.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.975+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:06.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:06.988+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:47:07.007+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.208 seconds
[2025-07-17T22:47:37.934+0000] {processor.py:186} INFO - Started process (PID=1790) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:47:37.935+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:47:37.936+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:37.936+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:47:38.012+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.012+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:38.020+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:47:38.130+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.130+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:38.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.142+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:47:38.164+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.237 seconds
[2025-07-17T22:48:08.802+0000] {processor.py:186} INFO - Started process (PID=1921) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:48:08.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:48:08.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.805+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:48:08.878+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.878+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:08.886+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:48:08.978+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.978+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:08.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:08.988+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:48:09.007+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.211 seconds
[2025-07-17T22:48:39.778+0000] {processor.py:186} INFO - Started process (PID=2052) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:48:39.779+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:48:39.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.780+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:48:39.850+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.850+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:39.858+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:48:39.960+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.960+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:39.972+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:39.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:48:39.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.220 seconds
[2025-07-17T22:49:10.248+0000] {processor.py:186} INFO - Started process (PID=2183) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:49:10.249+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:49:10.250+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.250+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:49:10.344+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.343+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:10.352+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:49:10.478+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.477+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:10.490+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.490+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:49:10.512+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.272 seconds
[2025-07-17T22:49:40.661+0000] {processor.py:186} INFO - Started process (PID=2314) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:49:40.662+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:49:40.664+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.663+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:49:40.729+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.729+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:40.737+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:49:40.820+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.819+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:40.830+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:40.830+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:49:40.847+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.192 seconds
[2025-07-17T22:50:11.250+0000] {processor.py:186} INFO - Started process (PID=2445) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:50:11.252+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:50:11.253+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:11.253+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:50:11.324+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:11.324+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:11.330+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:50:11.424+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:11.423+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:11.434+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:11.434+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:50:11.452+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.208 seconds
[2025-07-17T22:50:42.207+0000] {processor.py:186} INFO - Started process (PID=2578) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:50:42.208+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:50:42.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:42.209+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:50:42.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:42.288+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:42.295+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:50:42.397+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:42.396+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:42.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:42.408+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:50:42.425+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.224 seconds
[2025-07-17T22:51:12.704+0000] {processor.py:186} INFO - Started process (PID=2709) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:51:12.705+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:51:12.707+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.706+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:51:12.777+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.777+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:12.784+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:51:12.888+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.888+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:12.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:12.899+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:51:12.921+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.223 seconds
[2025-07-17T22:51:43.523+0000] {processor.py:186} INFO - Started process (PID=2840) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:51:43.524+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:51:43.526+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:43.525+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:51:43.597+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:43.597+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:43.607+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:51:43.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:43.715+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:43.729+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:43.728+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:51:43.751+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.234 seconds
[2025-07-17T22:52:13.992+0000] {processor.py:186} INFO - Started process (PID=2969) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:52:13.993+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:52:13.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:13.994+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:52:14.063+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:14.063+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:14.072+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:52:14.163+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:14.163+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:14.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:14.173+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:52:14.192+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.206 seconds
