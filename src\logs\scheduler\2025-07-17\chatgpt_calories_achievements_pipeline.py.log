[2025-07-17T21:34:33.048+0000] {processor.py:186} INFO - Started process (PID=306) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:34:33.049+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:34:33.052+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.051+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:34:33.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.131+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:33.139+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:34:33.363+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.363+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:33.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.375+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:34:33.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.354 seconds
[2025-07-17T21:35:05.037+0000] {processor.py:186} INFO - Started process (PID=444) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:05.038+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:35:05.040+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:05.244+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.244+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:05.250+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:05.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.338+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:05.346+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:35:05.364+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.333 seconds
[2025-07-17T21:35:35.875+0000] {processor.py:186} INFO - Started process (PID=580) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:35.876+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:35:35.879+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.879+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:35.950+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.950+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:35.959+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:35:36.061+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:36.061+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:36.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:36.081+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:35:36.105+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.237 seconds
[2025-07-17T21:36:06.239+0000] {processor.py:186} INFO - Started process (PID=716) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:06.240+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:36:06.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.242+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:06.322+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.321+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:06.330+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:06.431+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.431+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:06.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.441+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:36:06.466+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.233 seconds
[2025-07-17T21:36:36.564+0000] {processor.py:186} INFO - Started process (PID=855) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:36.565+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:36:36.568+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.568+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:36.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.643+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.652+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:36:36.778+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.778+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.792+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:36:36.813+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.255 seconds
[2025-07-17T21:37:07.156+0000] {processor.py:186} INFO - Started process (PID=988) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:07.158+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:37:07.161+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.161+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:07.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.243+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:07.252+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:07.357+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.356+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:07.369+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:37:07.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.250 seconds
[2025-07-17T21:37:37.681+0000] {processor.py:186} INFO - Started process (PID=1122) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:37.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:37:37.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:37.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.762+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:37.771+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:37:37.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.884+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:37.897+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.897+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:37:37.919+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.245 seconds
[2025-07-17T21:38:08.231+0000] {processor.py:186} INFO - Started process (PID=1258) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:08.232+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:38:08.234+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.234+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:08.305+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.305+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:08.314+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:08.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.417+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:08.428+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.427+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:38:08.448+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.222 seconds
[2025-07-17T21:38:39.085+0000] {processor.py:186} INFO - Started process (PID=1394) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:39.086+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:38:39.089+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.089+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:39.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.174+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:39.182+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:38:39.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.289+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:39.301+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.301+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:38:39.321+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.241 seconds
[2025-07-17T21:39:09.542+0000] {processor.py:186} INFO - Started process (PID=1530) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:09.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:39:09.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.544+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:09.621+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.621+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:09.631+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:09.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:09.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.742+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:39:09.764+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.228 seconds
[2025-07-17T21:39:40.174+0000] {processor.py:186} INFO - Started process (PID=1666) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:40.175+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:39:40.178+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.177+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:40.266+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.265+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:40.274+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:39:40.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:40.397+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.397+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:39:40.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.254 seconds
[2025-07-17T21:40:11.148+0000] {processor.py:186} INFO - Started process (PID=1802) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:11.149+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:40:11.153+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.152+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:11.244+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.243+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:11.251+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:11.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.373+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:11.385+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.384+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:40:11.405+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.265 seconds
[2025-07-17T21:40:42.146+0000] {processor.py:186} INFO - Started process (PID=1938) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:42.147+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:40:42.150+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.149+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:42.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.237+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:42.246+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:40:42.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.359+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:42.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.384+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:40:42.422+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.283 seconds
[2025-07-17T21:42:59.925+0000] {processor.py:186} INFO - Started process (PID=312) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:42:59.926+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:42:59.928+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.928+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:43:00.005+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:00.005+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:00.013+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:43:00.253+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:00.252+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:00.266+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:00.265+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:43:00.289+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.371 seconds
[2025-07-17T21:43:31.129+0000] {processor.py:186} INFO - Started process (PID=453) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:43:31.130+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:43:31.133+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.132+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:43:31.331+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.330+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:31.337+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:43:31.440+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.440+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:31.450+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.449+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:43:31.468+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.345 seconds
[2025-07-17T21:44:01.739+0000] {processor.py:186} INFO - Started process (PID=591) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:01.741+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:44:01.745+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.744+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:01.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.843+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:01.851+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:01.960+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.960+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:01.973+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.972+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:44:01.991+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.261 seconds
[2025-07-17T21:44:32.209+0000] {processor.py:186} INFO - Started process (PID=727) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:32.210+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:44:32.214+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:32.213+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:32.321+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:32.321+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:32.329+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:44:32.444+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:32.444+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:32.455+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:32.455+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:44:32.475+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.273 seconds
[2025-07-17T21:45:02.868+0000] {processor.py:186} INFO - Started process (PID=856) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:45:02.870+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T21:45:02.872+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.872+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:45:02.974+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.974+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:02.982+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T21:45:03.093+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:03.093+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:03.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:03.107+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T21:45:03.127+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.265 seconds
