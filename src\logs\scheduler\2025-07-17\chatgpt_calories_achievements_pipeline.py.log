[2025-07-17T22:41:27.689+0000] {processor.py:186} INFO - Started process (PID=200) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:27.690+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:41:27.692+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.692+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:27.774+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.774+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:27.781+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:27.914+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:27.913+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:28.160+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.160+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:41:28.185+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.502 seconds
[2025-07-17T22:41:59.008+0000] {processor.py:186} INFO - Started process (PID=337) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:59.009+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:41:59.012+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.011+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:59.087+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.087+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:41:59.313+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.312+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:59.325+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.325+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:41:59.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.349 seconds
[2025-07-17T22:42:29.870+0000] {processor.py:186} INFO - Started process (PID=470) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:42:29.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:42:29.873+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:29.872+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:42:30.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.082+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.090+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:42:30.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.187+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.197+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.197+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:42:30.217+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.352 seconds
[2025-07-17T22:43:00.367+0000] {processor.py:186} INFO - Started process (PID=599) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:00.368+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:43:00.370+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.370+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:00.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.441+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.451+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:00.553+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.553+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:00.565+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.565+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:43:00.584+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.223 seconds
[2025-07-17T22:43:30.754+0000] {processor.py:186} INFO - Started process (PID=730) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:30.755+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:43:30.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.756+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:30.829+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.829+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:30.839+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:43:30.945+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.945+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:30.956+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:30.956+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:43:30.977+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.229 seconds
[2025-07-17T22:44:01.872+0000] {processor.py:186} INFO - Started process (PID=861) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:01.873+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:44:01.875+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.875+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:01.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:01.953+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:01.960+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:02.073+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.072+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.084+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.083+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:44:02.104+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.238 seconds
[2025-07-17T22:44:32.870+0000] {processor.py:186} INFO - Started process (PID=992) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:32.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-17T22:44:32.872+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.872+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:32.940+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:32.939+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:32.948+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-17T22:44:33.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.040+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.053+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.053+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-17T22:44:33.074+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.210 seconds
