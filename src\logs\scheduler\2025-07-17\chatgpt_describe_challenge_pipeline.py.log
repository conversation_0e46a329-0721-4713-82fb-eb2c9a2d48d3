[2025-07-17T21:34:31.471+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:34:31.472+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:34:31.474+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.474+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:34:31.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.562+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:31.572+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:34:31.824+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.824+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.833+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:34:31.860+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.395 seconds
[2025-07-17T21:35:03.052+0000] {processor.py:186} INFO - Started process (PID=409) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:03.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:35:03.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:03.309+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.309+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:03.316+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:03.409+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.409+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.418+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.418+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:35:03.439+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.393 seconds
[2025-07-17T21:35:33.692+0000] {processor.py:186} INFO - Started process (PID=545) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:33.693+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:35:33.696+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:33.784+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.784+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:33.793+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:33.911+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:33.922+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:35:33.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.261 seconds
[2025-07-17T21:36:04.017+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:04.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:36:04.021+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.020+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:04.098+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.098+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:04.108+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:04.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.203+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:04.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:36:04.232+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.221 seconds
[2025-07-17T21:36:34.657+0000] {processor.py:186} INFO - Started process (PID=815) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:34.658+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:36:34.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.660+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:34.733+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.732+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.739+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:34.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.834+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.846+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.846+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:36:34.865+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.214 seconds
