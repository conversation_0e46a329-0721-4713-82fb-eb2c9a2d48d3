[2025-07-17T22:41:30.018+0000] {processor.py:186} INFO - Started process (PID=248) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:41:30.019+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:41:30.021+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:41:30.091+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.090+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:30.099+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:41:30.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.328+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:30.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.336+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:41:30.357+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.344 seconds
[2025-07-17T22:42:01.033+0000] {processor.py:186} INFO - Started process (PID=379) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:01.034+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:42:01.037+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.037+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:01.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.123+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:01.134+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:01.379+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.379+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:01.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.387+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:42:01.404+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.377 seconds
[2025-07-17T22:42:31.726+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:31.727+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:42:31.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:31.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.937+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:31.943+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:32.052+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.052+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:32.065+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:42:32.085+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.365 seconds
[2025-07-17T22:43:02.212+0000] {processor.py:186} INFO - Started process (PID=641) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:02.213+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:43:02.215+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.215+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:02.290+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.290+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:02.299+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:02.406+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.406+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:02.418+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.418+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:43:02.441+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.236 seconds
[2025-07-17T22:43:32.625+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:32.626+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:43:32.627+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.627+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:32.705+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.705+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:32.717+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:32.824+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.824+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:32.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.836+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:43:32.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.239 seconds
[2025-07-17T22:44:03.195+0000] {processor.py:186} INFO - Started process (PID=901) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:03.195+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:44:03.197+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.197+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:03.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.264+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:03.273+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:03.369+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.369+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:03.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.383+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:44:03.405+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.216 seconds
[2025-07-17T22:44:34.182+0000] {processor.py:186} INFO - Started process (PID=1032) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:34.183+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:44:34.184+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:34.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.255+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:34.264+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:34.366+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.365+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:34.378+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.378+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:44:34.398+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.222 seconds
[2025-07-17T22:45:05.310+0000] {processor.py:186} INFO - Started process (PID=1165) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:45:05.311+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:45:05.312+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.312+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:45:05.380+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.380+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:05.389+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:45:05.488+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.488+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:05.499+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.498+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:45:05.517+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.213 seconds
[2025-07-17T22:45:36.129+0000] {processor.py:186} INFO - Started process (PID=1296) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:45:36.130+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:45:36.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.131+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:45:36.207+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.207+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:36.215+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:45:36.316+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.316+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:36.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:45:36.348+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.225 seconds
[2025-07-17T22:46:06.964+0000] {processor.py:186} INFO - Started process (PID=1427) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:46:06.965+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:46:06.967+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.967+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:46:07.039+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.039+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:07.048+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:46:07.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.140+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:07.150+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:07.150+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:46:07.168+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.210 seconds
[2025-07-17T22:46:37.258+0000] {processor.py:186} INFO - Started process (PID=1558) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:46:37.259+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:46:37.261+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.261+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:46:37.339+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.339+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:37.349+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:46:37.447+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.447+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:37.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.458+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:46:37.479+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.228 seconds
[2025-07-17T22:47:08.254+0000] {processor.py:186} INFO - Started process (PID=1695) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:47:08.255+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:47:08.256+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.256+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:47:08.321+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.321+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:08.331+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:47:08.424+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.424+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:08.434+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.434+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:47:08.453+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.205 seconds
[2025-07-17T22:47:39.313+0000] {processor.py:186} INFO - Started process (PID=1830) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:47:39.314+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:47:39.316+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.316+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:47:39.432+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.432+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:39.450+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:47:39.560+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.560+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:39.570+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.570+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:47:39.589+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.283 seconds
[2025-07-17T22:48:10.263+0000] {processor.py:186} INFO - Started process (PID=1963) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:48:10.264+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:48:10.265+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.265+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:48:10.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.337+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:10.345+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:48:10.441+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.441+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:10.452+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.452+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:48:10.471+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.215 seconds
[2025-07-17T22:48:41.090+0000] {processor.py:186} INFO - Started process (PID=2092) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:48:41.092+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:48:41.093+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.093+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:48:41.170+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.170+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:41.178+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:48:41.281+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.281+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:41.292+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.292+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:48:41.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.230 seconds
[2025-07-17T22:49:12.087+0000] {processor.py:186} INFO - Started process (PID=2225) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:49:12.088+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:49:12.089+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.089+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:49:12.177+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.176+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:12.187+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:49:12.299+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:12.314+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.313+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:49:12.336+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.255 seconds
[2025-07-17T22:49:42.859+0000] {processor.py:186} INFO - Started process (PID=2356) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:49:42.860+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:49:42.861+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.861+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:49:42.928+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.927+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:42.936+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:49:43.022+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:43.022+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:43.033+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:43.033+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:49:43.052+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.199 seconds
