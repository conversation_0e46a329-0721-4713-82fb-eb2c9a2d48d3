[2025-07-17T22:41:30.018+0000] {processor.py:186} INFO - Started process (PID=248) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:41:30.019+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:41:30.021+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:41:30.091+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.090+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:30.099+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:41:30.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.328+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:30.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:30.336+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:41:30.357+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.344 seconds
[2025-07-17T22:42:01.033+0000] {processor.py:186} INFO - Started process (PID=379) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:01.034+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:42:01.037+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.037+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:01.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.123+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:01.134+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:01.379+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.379+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:01.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.387+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:42:01.404+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.377 seconds
[2025-07-17T22:42:31.726+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:31.727+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:42:31.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:31.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.937+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:31.943+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:42:32.052+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.052+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:32.065+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:42:32.085+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.365 seconds
[2025-07-17T22:43:02.212+0000] {processor.py:186} INFO - Started process (PID=641) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:02.213+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:43:02.215+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.215+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:02.290+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.290+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:02.299+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:02.406+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.406+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:02.418+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.418+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:43:02.441+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.236 seconds
[2025-07-17T22:43:32.625+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:32.626+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:43:32.627+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.627+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:32.705+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.705+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:32.717+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:43:32.824+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.824+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:32.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.836+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:43:32.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.239 seconds
[2025-07-17T22:44:03.195+0000] {processor.py:186} INFO - Started process (PID=901) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:03.195+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:44:03.197+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.197+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:03.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.264+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:03.273+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:03.369+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.369+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:03.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.383+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:44:03.405+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.216 seconds
[2025-07-17T22:44:34.182+0000] {processor.py:186} INFO - Started process (PID=1032) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:34.183+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:44:34.184+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:34.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.255+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:34.264+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:44:34.366+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.365+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:34.378+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.378+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:44:34.398+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.222 seconds
