[2025-07-17T21:34:31.471+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:34:31.472+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:34:31.474+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.474+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:34:31.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.562+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:31.572+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:34:31.824+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.824+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.833+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:34:31.860+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.395 seconds
[2025-07-17T21:35:03.052+0000] {processor.py:186} INFO - Started process (PID=409) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:03.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:35:03.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:03.309+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.309+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:03.316+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:03.409+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.409+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.418+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.418+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:35:03.439+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.393 seconds
[2025-07-17T21:35:33.692+0000] {processor.py:186} INFO - Started process (PID=545) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:33.693+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:35:33.696+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:33.784+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.784+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:33.793+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:33.911+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:33.922+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:35:33.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.261 seconds
[2025-07-17T21:36:04.017+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:04.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:36:04.021+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.020+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:04.098+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.098+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:04.108+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:04.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.203+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:04.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:36:04.232+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.221 seconds
[2025-07-17T21:36:34.657+0000] {processor.py:186} INFO - Started process (PID=815) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:34.658+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:36:34.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.660+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:34.733+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.732+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.739+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:34.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.834+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.846+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.846+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:36:34.865+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.214 seconds
[2025-07-17T21:37:05.198+0000] {processor.py:186} INFO - Started process (PID=951) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:05.199+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:37:05.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.201+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:05.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.289+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:05.299+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:05.396+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.396+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:05.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.407+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:37:05.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.235 seconds
[2025-07-17T21:37:35.579+0000] {processor.py:186} INFO - Started process (PID=1087) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:35.580+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:37:35.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.583+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:35.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.656+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.667+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:35.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.770+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:35.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.780+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:37:35.799+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.227 seconds
[2025-07-17T21:38:06.184+0000] {processor.py:186} INFO - Started process (PID=1223) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:06.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:38:06.188+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.188+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:06.277+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.276+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:06.284+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:06.390+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.390+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.404+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.403+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:38:06.425+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.247 seconds
[2025-07-17T21:38:36.994+0000] {processor.py:186} INFO - Started process (PID=1359) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:36.995+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:38:36.998+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.998+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:37.075+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.075+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:37.083+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:37.186+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.186+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:37.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.195+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:38:37.216+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.227 seconds
[2025-07-17T21:39:07.430+0000] {processor.py:186} INFO - Started process (PID=1495) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:07.430+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:39:07.432+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.432+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:07.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.513+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:07.521+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:07.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.626+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.637+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:39:07.657+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.233 seconds
[2025-07-17T21:39:38.098+0000] {processor.py:186} INFO - Started process (PID=1631) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:38.099+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:39:38.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.101+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:38.188+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.188+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:38.196+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:38.312+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.311+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:38.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.322+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:39:38.353+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.264 seconds
[2025-07-17T21:40:09.034+0000] {processor.py:186} INFO - Started process (PID=1767) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:09.035+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:40:09.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.037+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:09.124+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.124+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:09.132+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:09.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.239+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:09.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.251+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:40:09.271+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.244 seconds
[2025-07-17T21:40:40.117+0000] {processor.py:186} INFO - Started process (PID=1903) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:40.118+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:40:40.121+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.121+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:40.211+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.211+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:40.219+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:40.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:40.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.336+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:40:40.358+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.248 seconds
[2025-07-17T21:42:58.298+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:42:58.299+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:42:58.301+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.301+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:42:58.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.383+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:58.392+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:42:58.682+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.682+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:58.695+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.695+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:42:58.717+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.425 seconds
[2025-07-17T21:43:29.392+0000] {processor.py:186} INFO - Started process (PID=413) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:43:29.394+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:43:29.397+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.397+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:43:29.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.635+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:29.642+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:43:29.761+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.761+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:29.776+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.775+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:43:29.797+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.414 seconds
[2025-07-17T21:43:59.899+0000] {processor.py:186} INFO - Started process (PID=549) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:43:59.901+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:43:59.904+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.903+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:43:59.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.994+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:00.002+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:44:00.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:00.126+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:44:00.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.254 seconds
[2025-07-17T21:44:30.675+0000] {processor.py:186} INFO - Started process (PID=685) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:44:30.676+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:44:30.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:44:30.759+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.759+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.769+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:44:30.883+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.883+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:30.894+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.894+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:44:30.913+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.245 seconds
[2025-07-17T21:45:01.835+0000] {processor.py:186} INFO - Started process (PID=821) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:45:01.836+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:45:01.840+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.839+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:45:01.920+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.920+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:01.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:45:02.036+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.036+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:02.047+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.047+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:45:02.068+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.240 seconds
[2025-07-17T21:55:26.130+0000] {processor.py:186} INFO - Started process (PID=272) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:55:26.131+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:55:26.134+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:55:26.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.201+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:26.210+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:55:26.437+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.437+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:26.446+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.446+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:55:26.463+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.340 seconds
[2025-07-17T21:55:56.985+0000] {processor.py:186} INFO - Started process (PID=414) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:55:56.987+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:55:56.989+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:55:57.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.195+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:57.203+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:55:57.311+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.311+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:57.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:57.325+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:55:57.342+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.362 seconds
[2025-07-17T21:56:27.561+0000] {processor.py:186} INFO - Started process (PID=553) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:56:27.563+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:56:27.567+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.566+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:56:27.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.643+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:27.652+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:56:27.747+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.747+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:27.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.756+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:56:27.773+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.220 seconds
[2025-07-17T21:56:57.939+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:56:57.940+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:56:57.942+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.942+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:56:58.010+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.009+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:58.017+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:56:58.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.115+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:58.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:58.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:56:58.144+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.212 seconds
[2025-07-17T21:57:28.698+0000] {processor.py:186} INFO - Started process (PID=823) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:57:28.699+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:57:28.701+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.701+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:57:28.771+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.771+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:28.778+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:57:28.874+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.873+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:28.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:57:28.901+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.209 seconds
[2025-07-17T21:57:59.339+0000] {processor.py:186} INFO - Started process (PID=959) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:57:59.340+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:57:59.343+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.343+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:57:59.425+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.425+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:59.433+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:57:59.544+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.543+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:59.555+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.554+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:57:59.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.243 seconds
[2025-07-17T21:58:29.704+0000] {processor.py:186} INFO - Started process (PID=1095) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:58:29.705+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:58:29.707+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.707+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:58:29.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.779+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:29.787+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:58:29.890+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.889+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:29.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.906+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:58:29.932+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.234 seconds
[2025-07-17T22:00:37.627+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:00:37.628+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:00:37.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.630+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:00:37.704+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.704+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:37.712+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:00:37.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.953+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:37.961+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:00:37.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.356 seconds
[2025-07-17T22:01:08.765+0000] {processor.py:186} INFO - Started process (PID=407) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:01:08.767+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:01:08.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.770+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:01:09.020+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.020+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:09.029+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:01:09.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.127+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:09.138+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:09.138+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:01:09.160+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.402 seconds
[2025-07-17T22:01:39.461+0000] {processor.py:186} INFO - Started process (PID=543) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:01:39.462+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:01:39.464+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.464+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:01:39.536+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.536+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:39.546+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:01:39.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.638+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:39.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.648+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:01:39.668+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.212 seconds
[2025-07-17T22:02:09.937+0000] {processor.py:186} INFO - Started process (PID=679) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:02:09.938+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:02:09.940+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.940+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:02:10.008+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.008+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:10.017+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:02:10.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:10.125+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:10.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:02:10.144+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.212 seconds
[2025-07-17T22:02:40.325+0000] {processor.py:186} INFO - Started process (PID=815) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:02:40.326+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:02:40.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.328+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:02:40.404+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.404+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:40.412+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:02:40.501+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.501+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:40.511+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.511+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:02:40.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.215 seconds
[2025-07-17T22:03:10.745+0000] {processor.py:186} INFO - Started process (PID=951) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:03:10.746+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:03:10.749+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.749+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:03:10.822+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.821+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:10.831+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:03:10.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.923+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:10.932+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.931+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:03:10.948+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.208 seconds
[2025-07-17T22:03:41.274+0000] {processor.py:186} INFO - Started process (PID=1087) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:03:41.276+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:03:41.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:41.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:03:41.355+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:41.354+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:41.362+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:03:41.702+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:41.702+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:41.735+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:41.735+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:03:41.804+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.536 seconds
[2025-07-17T22:04:11.985+0000] {processor.py:186} INFO - Started process (PID=1223) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:04:11.986+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:04:11.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:04:12.058+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:12.058+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:12.066+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:04:12.153+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:12.153+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:12.164+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:12.164+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:04:12.183+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.204 seconds
[2025-07-17T22:04:42.561+0000] {processor.py:186} INFO - Started process (PID=1359) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:04:42.562+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:04:42.565+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.564+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:04:42.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.635+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:42.641+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:04:42.729+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.728+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:42.738+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.738+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:04:42.754+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.199 seconds
[2025-07-17T22:05:12.936+0000] {processor.py:186} INFO - Started process (PID=1495) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:05:12.937+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:05:12.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:12.939+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:05:13.022+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:13.022+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:13.030+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:05:13.132+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:13.132+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:13.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:13.142+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:05:13.159+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.230 seconds
[2025-07-17T22:05:43.595+0000] {processor.py:186} INFO - Started process (PID=1631) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:05:43.596+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:05:43.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:43.598+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:05:43.677+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:43.677+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:43.686+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:05:43.788+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:43.787+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:43.799+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:43.799+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:05:43.820+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.231 seconds
[2025-07-17T22:06:14.043+0000] {processor.py:186} INFO - Started process (PID=1767) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:06:14.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:06:14.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:14.046+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:06:14.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:14.110+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:14.121+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:06:14.218+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:14.218+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:14.230+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:14.229+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:06:14.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.211 seconds
[2025-07-17T22:07:29.199+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:07:29.200+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:07:29.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.202+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:07:29.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.278+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:29.287+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:07:29.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.512+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:29.521+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:29.521+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:07:29.538+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.345 seconds
[2025-07-17T22:08:00.896+0000] {processor.py:186} INFO - Started process (PID=409) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:08:00.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:08:00.899+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:08:01.099+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.098+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:01.107+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:08:01.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:01.205+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:01.204+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:08:01.223+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.332 seconds
[2025-07-17T22:08:31.370+0000] {processor.py:186} INFO - Started process (PID=543) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:08:31.371+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:08:31.373+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.373+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:08:31.457+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.457+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:31.464+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:08:31.561+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.561+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:31.573+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:31.572+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:08:31.592+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.229 seconds
[2025-07-17T22:09:01.872+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:09:01.872+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:09:01.875+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.874+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:09:01.944+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.944+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:01.951+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:09:02.039+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:02.038+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:02.049+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:02.049+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:09:02.068+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.202 seconds
[2025-07-17T22:09:32.173+0000] {processor.py:186} INFO - Started process (PID=815) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:09:32.174+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:09:32.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:32.176+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:09:32.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:32.246+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:32.254+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:09:32.364+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:32.364+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:32.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:32.375+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:09:32.394+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.228 seconds
[2025-07-17T22:10:02.928+0000] {processor.py:186} INFO - Started process (PID=951) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:10:02.929+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:10:02.931+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:02.931+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:10:03.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:03.004+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:03.011+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:10:03.106+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:03.106+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:03.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:03.115+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:10:03.137+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.215 seconds
[2025-07-17T22:10:33.877+0000] {processor.py:186} INFO - Started process (PID=1087) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:10:33.877+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:10:33.880+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:33.879+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:10:33.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:33.951+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:33.961+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:10:34.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:34.056+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:34.068+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:34.068+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:10:34.087+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.216 seconds
[2025-07-17T22:11:04.919+0000] {processor.py:186} INFO - Started process (PID=1223) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:11:04.920+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:11:04.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:04.922+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:11:04.997+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:04.996+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:05.006+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:11:05.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:05.107+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:05.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:05.117+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:11:05.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.220 seconds
[2025-07-17T22:11:36.044+0000] {processor.py:186} INFO - Started process (PID=1359) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:11:36.045+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:11:36.048+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:36.048+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:11:36.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:36.119+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:36.129+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:11:36.227+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:36.226+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:36.238+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:36.238+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:11:36.258+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.221 seconds
[2025-07-17T22:12:06.874+0000] {processor.py:186} INFO - Started process (PID=1490) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:12:06.875+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:12:06.876+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:06.876+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:12:06.946+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:06.946+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:06.954+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:12:07.048+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:07.048+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:07.058+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:07.058+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:12:07.076+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.208 seconds
[2025-07-17T22:12:37.375+0000] {processor.py:186} INFO - Started process (PID=1621) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:12:37.376+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T22:12:37.377+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:37.377+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:12:37.453+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:37.452+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:37.461+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T22:12:37.565+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:37.565+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:37.579+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:37.578+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T22:12:37.598+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.229 seconds
