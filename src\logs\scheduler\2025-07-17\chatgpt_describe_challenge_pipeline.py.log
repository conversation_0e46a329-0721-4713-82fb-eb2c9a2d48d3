[2025-07-17T21:34:31.471+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:34:31.472+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:34:31.474+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.474+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:34:31.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.562+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:31.572+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:34:31.824+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.824+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.833+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:34:31.860+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.395 seconds
[2025-07-17T21:35:03.052+0000] {processor.py:186} INFO - Started process (PID=409) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:03.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:35:03.056+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:03.309+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.309+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:03.316+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:03.409+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.409+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.418+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.418+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:35:03.439+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.393 seconds
[2025-07-17T21:35:33.692+0000] {processor.py:186} INFO - Started process (PID=545) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:33.693+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:35:33.696+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:33.784+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.784+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:33.793+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:35:33.911+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:33.922+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:35:33.945+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.261 seconds
[2025-07-17T21:36:04.017+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:04.018+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:36:04.021+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.020+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:04.098+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.098+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:04.108+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:04.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.203+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:04.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:04.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:36:04.232+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.221 seconds
[2025-07-17T21:36:34.657+0000] {processor.py:186} INFO - Started process (PID=815) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:34.658+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:36:34.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.660+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:34.733+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.732+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.739+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:36:34.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.834+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.846+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.846+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:36:34.865+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.214 seconds
[2025-07-17T21:37:05.198+0000] {processor.py:186} INFO - Started process (PID=951) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:05.199+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:37:05.202+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.201+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:05.289+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.289+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:05.299+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:05.396+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.396+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:05.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.407+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:37:05.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.235 seconds
[2025-07-17T21:37:35.579+0000] {processor.py:186} INFO - Started process (PID=1087) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:35.580+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:37:35.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.583+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:35.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.656+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.667+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:37:35.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.770+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:35.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.780+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:37:35.799+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.227 seconds
[2025-07-17T21:38:06.184+0000] {processor.py:186} INFO - Started process (PID=1223) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:06.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:38:06.188+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.188+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:06.277+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.276+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:06.284+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:06.390+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.390+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.404+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.403+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:38:06.425+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.247 seconds
[2025-07-17T21:38:36.994+0000] {processor.py:186} INFO - Started process (PID=1359) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:36.995+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:38:36.998+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.998+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:37.075+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.075+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:37.083+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:38:37.186+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.186+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:37.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.195+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:38:37.216+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.227 seconds
[2025-07-17T21:39:07.430+0000] {processor.py:186} INFO - Started process (PID=1495) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:07.430+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:39:07.432+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.432+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:07.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.513+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:07.521+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:07.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.626+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.637+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:39:07.657+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.233 seconds
[2025-07-17T21:39:38.098+0000] {processor.py:186} INFO - Started process (PID=1631) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:38.099+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:39:38.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.101+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:38.188+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.188+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:38.196+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:39:38.312+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.311+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:38.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.322+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:39:38.353+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.264 seconds
[2025-07-17T21:40:09.034+0000] {processor.py:186} INFO - Started process (PID=1767) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:09.035+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:40:09.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.037+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:09.124+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.124+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:09.132+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:09.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.239+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:09.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.251+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:40:09.271+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.244 seconds
[2025-07-17T21:40:40.117+0000] {processor.py:186} INFO - Started process (PID=1903) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:40.118+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:40:40.121+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.121+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:40.211+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.211+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:40.219+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:40:40.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:40.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.336+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:40:40.358+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.248 seconds
[2025-07-17T21:42:58.298+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:42:58.299+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:42:58.301+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.301+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:42:58.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.383+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:58.392+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:42:58.682+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.682+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:58.695+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.695+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:42:58.717+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.425 seconds
[2025-07-17T21:43:29.392+0000] {processor.py:186} INFO - Started process (PID=413) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:43:29.394+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:43:29.397+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.397+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:43:29.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.635+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:29.642+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:43:29.761+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.761+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:29.776+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.775+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:43:29.797+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.414 seconds
[2025-07-17T21:43:59.899+0000] {processor.py:186} INFO - Started process (PID=549) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:43:59.901+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:43:59.904+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.903+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:43:59.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.994+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:00.002+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:44:00.114+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:00.126+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:00.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:44:00.146+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.254 seconds
[2025-07-17T21:44:30.675+0000] {processor.py:186} INFO - Started process (PID=685) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:44:30.676+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:44:30.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:44:30.759+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.759+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.769+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:44:30.883+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.883+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:30.894+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.894+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:44:30.913+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.245 seconds
[2025-07-17T21:45:01.835+0000] {processor.py:186} INFO - Started process (PID=821) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:45:01.836+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-17T21:45:01.840+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.839+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:45:01.920+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.920+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:01.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-17T21:45:02.036+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.036+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:02.047+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.047+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-17T21:45:02.068+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.240 seconds
