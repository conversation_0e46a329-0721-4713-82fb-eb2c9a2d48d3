"""
Скрипт для автоматического обновления всех DAG файлов
Добавляет централизованную систему мониторинга затрат
БЕЗОПАСНОЕ ОБНОВЛЕНИЕ - СОЗДАЕТ РЕЗЕРВНЫЕ КОПИИ
"""

import os
import shutil
import re
from datetime import datetime
from typing import List, Dict

# Маппинг DAG файлов к типам операций
DAG_OPERATION_MAPPING = {
    "chatgpt_analyze_food_pipeline.py": "OperationType.FOOD_ANALYSIS",
    "chatgpt_calories_achievements_pipeline.py": "OperationType.RECOMMENDATION_GENERATION",
    "chatgpt_calories_big_trend_pipeline.py": "OperationType.RECOMMENDATION_GENERATION",
    "chatgpt_calories_small_trend_pipeline.py": "OperationType.RECOMMENDATION_GENERATION",
    "chatgpt_challenge_ask_pipeline.py": "OperationType.CHALLENGE_GENERATION",
    "chatgpt_challenge_classify_pipeline.py": "OperationType.CHALLENGE_GENERATION",
    "chatgpt_chat_open_pipeline.py": "OperationType.GENERAL_CHAT",
    "chatgpt_chat_open_rec_risk_pipeline.py": "OperationType.GENERAL_CHAT",
    "chatgpt_clarify_pipeline.py": "OperationType.GENERAL_CHAT",
    "chatgpt_classify_pipeline.py": "OperationType.GENERAL_CHAT",
    "chatgpt_describe_challenge_pipeline.py": "OperationType.CHALLENGE_GENERATION",
    "chatgpt_generate_challenges_pipeline.py": "OperationType.CHALLENGE_GENERATION",
    "chatgpt_goals_pipeline.py": "OperationType.ONBOARDING",
    "chatgpt_image_object_pipeline.py": "OperationType.IMAGE_ANALYSIS",
    "chatgpt_image_pipeline.py": "OperationType.IMAGE_ANALYSIS",
    "chatgpt_message_pipeline.py": "OperationType.GENERAL_CHAT",
    "chatgpt_message_recommendation_pipeline.py": "OperationType.RECOMMENDATION_GENERATION",
    "chatgpt_message_risk_pipeline.py": "OperationType.RISK_ASSESSMENT",
    "chatgpt_metrics_pipeline.py": "OperationType.GENERAL_CHAT",
    "chatgpt_pdf_pipeline.py": "OperationType.GENERAL_CHAT",
    "chatgpt_recount_calories_pipeline.py": "OperationType.FOOD_ANALYSIS",
    "chatgpt_unified_question_pipeline.py": "OperationType.GENERAL_CHAT",
    "chatgpt_weights_pipeline.py": "OperationType.RECOMMENDATION_GENERATION",
    "perplexity_pipeline.py": "OperationType.PERPLEXITY_SEARCH"
}

def create_backup(file_path: str) -> str:
    """
    Создает резервную копию файла
    
    Args:
        file_path: Путь к файлу
        
    Returns:
        Путь к резервной копии
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    shutil.copy2(file_path, backup_path)
    print(f"📁 Создана резервная копия: {backup_path}")
    return backup_path

def update_imports(content: str) -> str:
    """
    Обновляет импорты в файле
    
    Args:
        content: Содержимое файла
        
    Returns:
        Обновленное содержимое
    """
    # Ищем строку с импортом tiktoken
    tiktoken_pattern = r'import tiktoken'
    
    if re.search(tiktoken_pattern, content):
        # Добавляем импорт нашей системы мониторинга после tiktoken
        new_import = "from cost_tracking import track_openai_request, OperationType"
        content = re.sub(
            tiktoken_pattern,
            f"import tiktoken\n{new_import}",
            content
        )
    
    return content

def remove_old_analyze_function(content: str) -> str:
    """
    Удаляет старую функцию analyze_token_usage
    
    Args:
        content: Содержимое файла
        
    Returns:
        Обновленное содержимое
    """
    # Паттерн для поиска функции analyze_token_usage
    pattern = r'def analyze_token_usage\(.*?\n.*?return \{[^}]*\}'
    
    # Удаляем функцию (многострочный поиск)
    content = re.sub(pattern, '', content, flags=re.DOTALL)
    
    return content

def add_cost_tracking(content: str, operation_type: str, dag_id: str) -> str:
    """
    Добавляет отслеживание затрат в функцию
    
    Args:
        content: Содержимое файла
        operation_type: Тип операции
        dag_id: ID DAG
        
    Returns:
        Обновленное содержимое
    """
    # Ищем строку с получением content из response
    content_pattern = r'content = response\.json\(\)\["choices"\]\[0\]\["message"\]\["content"\]'
    
    # Ищем закомментированные строки со stats
    stats_pattern = r'# stats = analyze_token_usage\(.*?\n.*?# print\(stats\)'
    
    # Новый код для отслеживания затрат
    tracking_code = f'''
        # Отслеживаем затраты с помощью централизованной системы
        try:
            # Получаем user_id из конфигурации DAG, если доступен
            user_id = conf.get("user_id") if conf else None
            session_id = conf.get("session_id") if conf else None
            
            # Отслеживаем операцию
            cost_analysis = track_openai_request(
                operation_type={operation_type},
                input_prompt=prompt,
                output_response=content,
                model="gpt-4o",
                user_id=user_id,
                session_id=session_id,
                additional_metadata={{
                    "dag_id": "{dag_id}",
                    "task_id": "generate_openai_response"
                }}
            )
            
            print(f"💰 Стоимость операции: ${{cost_analysis.get('total_cost', 0):.6f}}")
            print(f"🔢 Токены: {{cost_analysis.get('total_tokens', 0)}}")
            
        except Exception as e:
            print(f"⚠️ Ошибка отслеживания затрат: {{e}}")'''
    
    # Заменяем закомментированные строки на новый код
    if re.search(stats_pattern, content, re.DOTALL):
        content = re.sub(stats_pattern, tracking_code, content, flags=re.DOTALL)
    else:
        # Если закомментированных строк нет, добавляем после получения content
        content = re.sub(
            content_pattern,
            f'content = response.json()["choices"][0]["message"]["content"]\n{tracking_code}',
            content
        )
    
    return content

def update_dag_file(file_path: str) -> bool:
    """
    Обновляет один DAG файл
    
    Args:
        file_path: Путь к файлу
        
    Returns:
        True если успешно обновлен, False в противном случае
    """
    try:
        filename = os.path.basename(file_path)
        
        # Пропускаем файлы, которые не нужно обновлять
        if filename in ["cost_tracking.py", "test_cost_tracking.py", "update_all_dags.py"]:
            return True
            
        if filename not in DAG_OPERATION_MAPPING:
            print(f"⚠️ Пропускаем {filename} - не найден в маппинге операций")
            return True
        
        print(f"🔄 Обновляем {filename}...")
        
        # Создаем резервную копию
        create_backup(file_path)
        
        # Читаем содержимое файла
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Проверяем, не обновлен ли уже файл
        if "from cost_tracking import" in content:
            print(f"✅ {filename} уже обновлен")
            return True
        
        # Получаем тип операции и DAG ID
        operation_type = DAG_OPERATION_MAPPING[filename]
        dag_id = filename.replace('.py', '')
        
        # Применяем обновления
        content = update_imports(content)
        content = remove_old_analyze_function(content)
        content = add_cost_tracking(content, operation_type, dag_id)
        
        # Сохраняем обновленный файл
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ {filename} успешно обновлен")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка обновления {filename}: {e}")
        return False

def update_all_dag_files(dags_directory: str = ".") -> Dict[str, bool]:
    """
    Обновляет все DAG файлы в директории
    
    Args:
        dags_directory: Путь к директории с DAG файлами
        
    Returns:
        Словарь с результатами обновления
    """
    results = {}
    
    print("🚀 НАЧАЛО ОБНОВЛЕНИЯ DAG ФАЙЛОВ")
    print("=" * 50)
    
    # Получаем список всех Python файлов в директории
    for filename in os.listdir(dags_directory):
        if filename.endswith('.py'):
            file_path = os.path.join(dags_directory, filename)
            results[filename] = update_dag_file(file_path)
    
    print("\n" + "=" * 50)
    print("📊 РЕЗУЛЬТАТЫ ОБНОВЛЕНИЯ:")
    
    successful = sum(1 for success in results.values() if success)
    total = len(results)
    
    print(f"✅ Успешно обновлено: {successful}/{total}")
    
    if successful < total:
        print("❌ Файлы с ошибками:")
        for filename, success in results.items():
            if not success:
                print(f"   - {filename}")
    
    return results

if __name__ == "__main__":
    # Запускаем обновление всех DAG файлов
    results = update_all_dag_files()
    
    if all(results.values()):
        print("\n🎉 ВСЕ DAG ФАЙЛЫ УСПЕШНО ОБНОВЛЕНЫ!")
        print("Система мониторинга затрат интегрирована во все пайплайны.")
    else:
        print("\n⚠️ НЕКОТОРЫЕ ФАЙЛЫ НЕ УДАЛОСЬ ОБНОВИТЬ!")
        print("Проверьте ошибки и повторите обновление.")
