import asyncio
import redis.asyncio as aioredis
import json
import base64


async def backup_data(user_id: str, backup_filename: str):
    # Подключаемся к Redis (на локальном хосте)
    r = await aioredis.from_url("redis://redis:6379", decode_responses=False)

    data = {}
    # Определяем паттерны для поиска ключей по user_id
    key_patterns = [f"*:{user_id}:*", f"*:{user_id}"]
    all_keys = set()
    for pattern in key_patterns:
        keys = await r.keys(pattern)
        all_keys.update(keys)

    print(f"Найдено ключей для user_id {user_id}: {len(all_keys)}")

    # Для каждого найденного ключа сохраняем его тип и данные
    for key_bytes in all_keys:
        key = key_bytes.decode('utf-8', 'replace')
        key_type_bytes = await r.type(key_bytes)
        key_type = key_type_bytes.decode('utf-8')

        value = None
        if key_type == "string":
            val_bytes = await r.get(key_bytes)
            try:
                value = val_bytes.decode('utf-8')
            except (UnicodeDecodeError, AttributeError):
                value = f"base64:{base64.b64encode(val_bytes).decode('ascii')}"
        elif key_type == "hash":
            value_raw = await r.hgetall(key_bytes)
            value = {}
            for k, v in value_raw.items():
                try:
                    k_str = k.decode('utf-8')
                except (UnicodeDecodeError, AttributeError):
                    k_str = f"base64:{base64.b64encode(k).decode('ascii')}"
                try:
                    v_str = v.decode('utf-8')
                except (UnicodeDecodeError, AttributeError):
                    v_str = f"base64:{base64.b64encode(v).decode('ascii')}"
                value[k_str] = v_str
        elif key_type == "list":
            value_raw = await r.lrange(key_bytes, 0, -1)
            value = []
            for item in value_raw:
                try:
                    value.append(item.decode('utf-8'))
                except (UnicodeDecodeError, AttributeError):
                    value.append(f"base64:{base64.b64encode(item).decode('ascii')}")
        elif key_type == "set":
            value_raw = await r.smembers(key_bytes)
            value = []
            for item in value_raw:
                try:
                    value.append(item.decode('utf-8'))
                except (UnicodeDecodeError, AttributeError):
                    value.append(f"base64:{base64.b64encode(item).decode('ascii')}")
        elif key_type == "zset":
            value_raw = await r.zrange(key_bytes, 0, -1, withscores=True)
            value = []
            for member, score in value_raw:
                try:
                    decoded_member = member.decode('utf-8')
                    value.append((decoded_member, score))
                except (UnicodeDecodeError, AttributeError):
                    value.append((f"base64:{base64.b64encode(member).decode('ascii')}", score))
        else:
            value = None
        data[key] = {"type": key_type, "value": value}

    # Сохраняем данные в JSON-файл
    with open(backup_filename, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    print(f"Резервная копия сохранена в файл: {backup_filename}")


async def restore_data(backup_filename: str):
    # Подключаемся к Redis (здесь используется другой URL, при необходимости измените его)
    r = await aioredis.from_url("redis://redis:6379")

    # Загружаем данные из файла
    with open(backup_filename, "r", encoding="utf-8") as f:
        data = json.load(f)

    # Восстанавливаем данные для каждого ключа
    for key, entry in data.items():
        key_type = entry["type"]
        value = entry["value"]

        if value is None:
            continue

        if key_type == "string":
            if isinstance(value, str) and value.startswith("base64:"):
                await r.set(key, base64.b64decode(value[7:]))
            else:
                await r.set(key, value)
        elif key_type == "hash":
            restored_hash = {}
            for k, v in value.items():
                k_final = base64.b64decode(k[7:]) if k.startswith("base64:") else k
                v_final = base64.b64decode(v[7:]) if v.startswith("base64:") else v
                restored_hash[k_final] = v_final
            await r.hset(key, mapping=restored_hash)
        elif key_type == "list":
            await r.delete(key)
            for item in value:
                if isinstance(item, str) and item.startswith("base64:"):
                    await r.rpush(key, base64.b64decode(item[7:]))
                else:
                    await r.rpush(key, item)
        elif key_type == "set":
            await r.delete(key)
            for item in value:
                if isinstance(item, str) and item.startswith("base64:"):
                    await r.sadd(key, base64.b64decode(item[7:]))
                else:
                    await r.sadd(key, item)
        elif key_type == "zset":
            await r.delete(key)
            mapping = {}
            for member, score in value:
                if isinstance(member, str) and member.startswith("base64:"):
                    mapping[base64.b64decode(member[7:])] = score
                else:
                    mapping[member] = score
            if mapping:
                await r.zadd(key, mapping)
        else:
            print(f"Неизвестный тип для ключа {key}, пропуск.")

    print("Восстановление данных завершено.")


async def main():
    mode = input("Введите режим (backup/restore): ").strip().lower()
    if mode == "backup":
        user_id = input("Введите user_id: ").strip()
        backup_filename = f"backup_{user_id}.json"
        await backup_data(user_id, backup_filename)
    elif mode == "restore":
        backup_filename = input("Введите имя файла с резервной копией: ").strip()
        await restore_data(backup_filename)
    else:
        print("Неверный режим. Введите 'backup' или 'restore'.")


if __name__ == "__main__":
    asyncio.run(main())