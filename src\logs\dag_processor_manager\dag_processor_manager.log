[2025-07-17T21:34:27.765+0000] {manager.py:483} INFO - Processing files using up to 2 processes at a time 
[2025-07-17T21:34:27.766+0000] {manager.py:484} INFO - Process each file at most once every 30 seconds
[2025-07-17T21:34:27.766+0000] {manager.py:485} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-07-17T21:34:27.767+0000] {manager.py:821} INFO - Searching for files in /opt/airflow/dags
[2025-07-17T21:34:27.879+0000] {manager.py:824} INFO - There are 26 files in /opt/airflow/dags
[2025-07-17T21:34:28.018+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run      Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  ----------  ----------------------
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            0           0                                                   0
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         0           0                                                   0
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       188  0.03s             0           0                                                   0
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            0           0                                                   0
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_image_pipeline.py                     191  0.02s             0           0                                                   0
/opt/airflow/dags/chatgpt_message_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           0           0                                                   0
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           0                                                   0
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/enable_cost_tracking.py                                              0           0                                                   0
/opt/airflow/dags/perplexity_pipeline.py                                               0           0                                                   0
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           0                                                   0
================================================================================
