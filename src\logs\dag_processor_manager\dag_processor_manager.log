[2025-07-17T21:34:27.765+0000] {manager.py:483} INFO - Processing files using up to 2 processes at a time 
[2025-07-17T21:34:27.766+0000] {manager.py:484} INFO - Process each file at most once every 30 seconds
[2025-07-17T21:34:27.766+0000] {manager.py:485} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-07-17T21:34:27.767+0000] {manager.py:821} INFO - Searching for files in /opt/airflow/dags
[2025-07-17T21:34:27.879+0000] {manager.py:824} INFO - There are 26 files in /opt/airflow/dags
[2025-07-17T21:34:28.018+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run      Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  ----------  ----------------------
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            0           0                                                   0
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         0           0                                                   0
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       188  0.03s             0           0                                                   0
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            0           0                                                   0
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_image_pipeline.py                     191  0.02s             0           0                                                   0
/opt/airflow/dags/chatgpt_message_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           0           0                                                   0
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           0                                                   0
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/enable_cost_tracking.py                                              0           0                                                   0
/opt/airflow/dags/perplexity_pipeline.py                                               0           0                                                   0
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           0                                                   0
================================================================================
[2025-07-17T21:34:58.887+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.61s           2025-07-17T21:34:31                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                     327  0.00s             1           0  0.58s           2025-07-17T21:34:28                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.47s           2025-07-17T21:34:30                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.45s           2025-07-17T21:34:31                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.44s           2025-07-17T21:34:29                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.44s           2025-07-17T21:34:28                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.44s           2025-07-17T21:34:29                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.44s           2025-07-17T21:34:32                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.44s           2025-07-17T21:34:32                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.43s           2025-07-17T21:34:29                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.43s           2025-07-17T21:34:31                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.42s           2025-07-17T21:34:32                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.42s           2025-07-17T21:34:33                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.42s           2025-07-17T21:34:29                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       324  0.01s             1           0  0.42s           2025-07-17T21:34:28                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.42s           2025-07-17T21:34:31                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.40s           2025-07-17T21:34:31                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.40s           2025-07-17T21:34:29                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.40s           2025-07-17T21:34:32                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.39s           2025-07-17T21:34:30                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.39s           2025-07-17T21:34:30                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.38s           2025-07-17T21:34:33                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.38s           2025-07-17T21:34:33                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.22s           2025-07-17T21:34:30                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.13s           2025-07-17T21:34:33                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:34:29                       3
================================================================================
[2025-07-17T21:35:29.834+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.57s           2025-07-17T21:35:01                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.57s           2025-07-17T21:35:01                      32
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.53s           2025-07-17T21:35:02                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                     463  0.00s             1           0  0.45s           2025-07-17T21:34:59                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.44s           2025-07-17T21:35:03                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       460  0.01s             1           0  0.44s           2025-07-17T21:34:59                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.44s           2025-07-17T21:35:01                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.43s           2025-07-17T21:35:03                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.43s           2025-07-17T21:35:00                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.42s           2025-07-17T21:35:03                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.42s           2025-07-17T21:35:02                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.42s           2025-07-17T21:35:02                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.41s           2025-07-17T21:35:03                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.41s           2025-07-17T21:35:03                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.40s           2025-07-17T21:35:01                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.40s           2025-07-17T21:35:00                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.39s           2025-07-17T21:34:59                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.38s           2025-07-17T21:35:00                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.38s           2025-07-17T21:35:02                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.37s           2025-07-17T21:35:04                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.37s           2025-07-17T21:35:04                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.37s           2025-07-17T21:35:05                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.36s           2025-07-17T21:35:05                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.35s           2025-07-17T21:35:04                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.24s           2025-07-17T21:35:05                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:35:00                       3
================================================================================
[2025-07-17T21:36:00.468+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.64s           2025-07-17T21:35:30                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       596  0.00s             1           0  0.47s           2025-07-17T21:35:30                      32
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.41s           2025-07-17T21:35:33                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.32s           2025-07-17T21:35:30                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.31s           2025-07-17T21:35:33                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.31s           2025-07-17T21:35:32                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.29s           2025-07-17T21:35:31                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.29s           2025-07-17T21:35:33                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.29s           2025-07-17T21:35:33                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.28s           2025-07-17T21:35:32                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.28s           2025-07-17T21:35:32                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.28s           2025-07-17T21:35:33                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.27s           2025-07-17T21:35:34                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.27s           2025-07-17T21:35:31                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.26s           2025-07-17T21:35:36                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.26s           2025-07-17T21:35:35                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.26s           2025-07-17T21:35:31                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.26s           2025-07-17T21:35:35                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.25s           2025-07-17T21:35:31                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.25s           2025-07-17T21:35:31                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.25s           2025-07-17T21:35:34                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.25s           2025-07-17T21:35:31                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.25s           2025-07-17T21:35:35                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.22s           2025-07-17T21:35:32                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-17T21:35:35                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:35:31                       3
================================================================================
[2025-07-17T21:36:30.885+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.36s           2025-07-17T21:36:03                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.32s           2025-07-17T21:36:05                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.29s           2025-07-17T21:36:03                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       732  0.00s             1           0  0.29s           2025-07-17T21:36:00                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.29s           2025-07-17T21:36:05                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.28s           2025-07-17T21:36:00                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.28s           2025-07-17T21:36:03                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.27s           2025-07-17T21:36:05                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.27s           2025-07-17T21:36:06                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.26s           2025-07-17T21:36:01                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.26s           2025-07-17T21:36:02                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.26s           2025-07-17T21:36:03                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.26s           2025-07-17T21:36:06                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.26s           2025-07-17T21:36:04                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.25s           2025-07-17T21:36:02                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.25s           2025-07-17T21:36:04                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.25s           2025-07-17T21:36:03                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.25s           2025-07-17T21:36:01                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.25s           2025-07-17T21:36:01                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.25s           2025-07-17T21:36:04                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T21:36:01                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.24s           2025-07-17T21:36:02                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.24s           2025-07-17T21:36:02                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.20s           2025-07-17T21:36:02                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-17T21:36:06                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:36:01                       3
================================================================================
[2025-07-17T21:37:01.152+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.33s           2025-07-17T21:36:34                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.28s           2025-07-17T21:36:36                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.26s           2025-07-17T21:36:36                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.26s           2025-07-17T21:36:36                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.26s           2025-07-17T21:36:35                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.26s           2025-07-17T21:36:31                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.25s           2025-07-17T21:36:34                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.25s           2025-07-17T21:36:32                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.25s           2025-07-17T21:36:36                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.24s           2025-07-17T21:36:36                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.24s           2025-07-17T21:36:31                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.24s           2025-07-17T21:36:31                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.23s           2025-07-17T21:36:34                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.23s           2025-07-17T21:36:33                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.23s           2025-07-17T21:36:34                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.23s           2025-07-17T21:36:34                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.23s           2025-07-17T21:36:33                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.23s           2025-07-17T21:36:32                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.23s           2025-07-17T21:36:34                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.23s           2025-07-17T21:36:31                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       868  0.00s             1           0  0.23s           2025-07-17T21:36:31                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.23s           2025-07-17T21:36:32                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.22s           2025-07-17T21:36:31                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.19s           2025-07-17T21:36:33                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-17T21:36:36                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:36:32                       3
================================================================================
[2025-07-17T21:37:31.993+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.37s           2025-07-17T21:37:04                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.30s           2025-07-17T21:37:06                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.30s           2025-07-17T21:37:06                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.29s           2025-07-17T21:37:02                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.27s           2025-07-17T21:37:07                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.27s           2025-07-17T21:37:03                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.27s           2025-07-17T21:37:04                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py      1004  0.02s             1           0  0.27s           2025-07-17T21:37:01                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                    1007  0.00s             1           0  0.26s           2025-07-17T21:37:01                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.26s           2025-07-17T21:37:05                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.26s           2025-07-17T21:37:02                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.26s           2025-07-17T21:37:05                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.26s           2025-07-17T21:37:02                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.26s           2025-07-17T21:37:05                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.25s           2025-07-17T21:37:01                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.25s           2025-07-17T21:37:05                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.25s           2025-07-17T21:37:05                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.25s           2025-07-17T21:37:02                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.25s           2025-07-17T21:37:03                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.25s           2025-07-17T21:37:07                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.24s           2025-07-17T21:37:03                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.24s           2025-07-17T21:37:07                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.23s           2025-07-17T21:37:02                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.23s           2025-07-17T21:37:03                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.15s           2025-07-17T21:37:07                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:37:02                       3
================================================================================
[2025-07-17T21:38:02.472+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.36s           2025-07-17T21:37:35                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.28s           2025-07-17T21:37:37                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.28s           2025-07-17T21:37:35                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.27s           2025-07-17T21:37:37                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                    1143  0.00s             1           0  0.27s           2025-07-17T21:37:32                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.27s           2025-07-17T21:37:37                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py      1140  0.01s             1           0  0.27s           2025-07-17T21:37:32                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.27s           2025-07-17T21:37:36                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.27s           2025-07-17T21:37:37                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.26s           2025-07-17T21:37:33                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.26s           2025-07-17T21:37:32                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.26s           2025-07-17T21:37:36                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.26s           2025-07-17T21:37:32                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.25s           2025-07-17T21:37:37                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.25s           2025-07-17T21:37:32                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.25s           2025-07-17T21:37:35                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.25s           2025-07-17T21:37:33                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.25s           2025-07-17T21:37:33                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.25s           2025-07-17T21:37:33                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T21:37:33                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.24s           2025-07-17T21:37:35                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.24s           2025-07-17T21:37:35                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.23s           2025-07-17T21:37:34                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.21s           2025-07-17T21:37:33                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.13s           2025-07-17T21:37:38                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:37:33                       3
================================================================================
[2025-07-17T21:38:33.008+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.42s           2025-07-17T21:38:06                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.31s           2025-07-17T21:38:03                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.31s           2025-07-17T21:38:06                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.31s           2025-07-17T21:38:03                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.30s           2025-07-17T21:38:06                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.29s           2025-07-17T21:38:03                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.28s           2025-07-17T21:38:04                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.28s           2025-07-17T21:38:06                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.27s           2025-07-17T21:38:06                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.27s           2025-07-17T21:38:03                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.27s           2025-07-17T21:38:06                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.27s           2025-07-17T21:38:04                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                    1279  0.00s             1           0  0.26s           2025-07-17T21:38:02                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.25s           2025-07-17T21:38:08                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.25s           2025-07-17T21:38:05                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.25s           2025-07-17T21:38:02                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py      1276  0.02s             1           0  0.25s           2025-07-17T21:38:02                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.25s           2025-07-17T21:38:07                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.25s           2025-07-17T21:38:07                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.25s           2025-07-17T21:38:08                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T21:38:03                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.23s           2025-07-17T21:38:04                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.22s           2025-07-17T21:38:08                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.20s           2025-07-17T21:38:04                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-17T21:38:08                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:38:03                       3
================================================================================
[2025-07-17T21:39:03.896+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.35s           2025-07-17T21:38:36                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.30s           2025-07-17T21:38:36                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.29s           2025-07-17T21:38:34                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.28s           2025-07-17T21:38:37                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.27s           2025-07-17T21:38:34                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.27s           2025-07-17T21:38:33                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.27s           2025-07-17T21:38:39                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.26s           2025-07-17T21:38:38                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.26s           2025-07-17T21:38:36                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                    1415  0.00s             1           0  0.26s           2025-07-17T21:38:33                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.26s           2025-07-17T21:38:37                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py      1412  0.02s             1           0  0.26s           2025-07-17T21:38:33                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.26s           2025-07-17T21:38:34                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.26s           2025-07-17T21:38:35                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.26s           2025-07-17T21:38:34                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.26s           2025-07-17T21:38:38                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.26s           2025-07-17T21:38:33                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.25s           2025-07-17T21:38:33                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.25s           2025-07-17T21:38:37                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.25s           2025-07-17T21:38:39                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.25s           2025-07-17T21:38:37                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.25s           2025-07-17T21:38:35                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.24s           2025-07-17T21:38:39                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.23s           2025-07-17T21:38:34                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-17T21:38:39                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:38:34                       3
================================================================================
[2025-07-17T21:39:28.160+0000] {manager.py:821} INFO - Searching for files in /opt/airflow/dags
[2025-07-17T21:39:28.347+0000] {manager.py:824} INFO - There are 26 files in /opt/airflow/dags
[2025-07-17T21:39:34.516+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.38s           2025-07-17T21:39:07                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.28s           2025-07-17T21:39:04                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.28s           2025-07-17T21:39:07                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.27s           2025-07-17T21:39:07                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                    1551  0.00s             1           0  0.27s           2025-07-17T21:39:04                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py      1548  0.01s             1           0  0.27s           2025-07-17T21:39:04                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.27s           2025-07-17T21:39:09                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.27s           2025-07-17T21:39:07                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.26s           2025-07-17T21:39:09                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.26s           2025-07-17T21:39:08                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.26s           2025-07-17T21:39:05                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.26s           2025-07-17T21:39:04                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.26s           2025-07-17T21:39:05                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.26s           2025-07-17T21:39:05                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.25s           2025-07-17T21:39:07                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.25s           2025-07-17T21:39:09                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.25s           2025-07-17T21:39:05                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.25s           2025-07-17T21:39:04                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.24s           2025-07-17T21:39:09                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.24s           2025-07-17T21:39:07                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T21:39:04                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.24s           2025-07-17T21:39:09                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.24s           2025-07-17T21:39:06                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.23s           2025-07-17T21:39:05                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-17T21:39:09                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:39:05                       3
================================================================================
[2025-07-17T21:40:05.089+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.49s           2025-07-17T21:39:37                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.42s           2025-07-17T21:39:38                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.34s           2025-07-17T21:39:38                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.33s           2025-07-17T21:39:37                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.30s           2025-07-17T21:39:38                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.30s           2025-07-17T21:39:39                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.30s           2025-07-17T21:39:39                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.30s           2025-07-17T21:39:38                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.29s           2025-07-17T21:39:40                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.28s           2025-07-17T21:39:40                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.28s           2025-07-17T21:39:38                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.28s           2025-07-17T21:39:40                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.27s           2025-07-17T21:39:36                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                    1687  0.00s             1           0  0.25s           2025-07-17T21:39:34                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py      1684  0.02s             1           0  0.24s           2025-07-17T21:39:34                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.24s           2025-07-17T21:39:34                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.24s           2025-07-17T21:39:35                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.24s           2025-07-17T21:39:35                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.23s           2025-07-17T21:39:35                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.23s           2025-07-17T21:39:35                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.23s           2025-07-17T21:39:36                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.23s           2025-07-17T21:39:36                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.23s           2025-07-17T21:39:35                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.23s           2025-07-17T21:39:35                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.13s           2025-07-17T21:39:40                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:39:35                       3
================================================================================
[2025-07-17T21:40:35.946+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.43s           2025-07-17T21:40:08                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.34s           2025-07-17T21:40:11                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.34s           2025-07-17T21:40:07                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.34s           2025-07-17T21:40:11                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.33s           2025-07-17T21:40:10                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.33s           2025-07-17T21:40:06                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.32s           2025-07-17T21:40:10                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                    1823  0.00s             1           0  0.32s           2025-07-17T21:40:05                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.31s           2025-07-17T21:40:08                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py      1820  0.01s             1           0  0.31s           2025-07-17T21:40:05                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.30s           2025-07-17T21:40:07                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.30s           2025-07-17T21:40:06                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.30s           2025-07-17T21:40:09                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.30s           2025-07-17T21:40:07                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.29s           2025-07-17T21:40:06                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.29s           2025-07-17T21:40:11                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.29s           2025-07-17T21:40:08                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.29s           2025-07-17T21:40:05                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.29s           2025-07-17T21:40:05                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.28s           2025-07-17T21:40:06                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.28s           2025-07-17T21:40:09                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.27s           2025-07-17T21:40:09                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.27s           2025-07-17T21:40:07                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.27s           2025-07-17T21:40:09                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.14s           2025-07-17T21:40:11                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:40:06                       3
================================================================================
[2025-07-17T21:41:06.071+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.46s           2025-07-17T21:40:39                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.35s           2025-07-17T21:40:39                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.32s           2025-07-17T21:40:42                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.31s           2025-07-17T21:40:42                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.31s           2025-07-17T21:40:36                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.31s           2025-07-17T21:40:41                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.31s           2025-07-17T21:40:36                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.31s           2025-07-17T21:40:41                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.30s           2025-07-17T21:40:40                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.29s           2025-07-17T21:40:37                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.29s           2025-07-17T21:40:40                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.29s           2025-07-17T21:40:37                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.29s           2025-07-17T21:40:36                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.29s           2025-07-17T21:40:38                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.28s           2025-07-17T21:40:37                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.28s           2025-07-17T21:40:40                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.28s           2025-07-17T21:40:36                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.28s           2025-07-17T21:40:36                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.28s           2025-07-17T21:40:40                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.27s           2025-07-17T21:40:38                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.27s           2025-07-17T21:40:40                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.27s           2025-07-17T21:40:42                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.26s           2025-07-17T21:40:37                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.23s           2025-07-17T21:40:38                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.15s           2025-07-17T21:40:42                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:40:37                       3
================================================================================
[2025-07-17T21:41:08.422+0000] {manager.py:465} INFO - Exiting gracefully upon receiving signal 15
[2025-07-17T21:41:08.423+0000] {manager.py:465} INFO - Exiting gracefully upon receiving signal 15
[2025-07-17T21:41:08.423+0000] {manager.py:465} INFO - Exiting gracefully upon receiving signal 15
[2025-07-17T21:41:08.747+0000] {manager.py:465} INFO - Exiting gracefully upon receiving signal 15
[2025-07-17T21:41:09.058+0000] {manager.py:465} INFO - Exiting gracefully upon receiving signal 15
[2025-07-17T21:41:09.754+0000] {manager.py:465} INFO - Exiting gracefully upon receiving signal 15
[2025-07-17T21:42:54.504+0000] {manager.py:483} INFO - Processing files using up to 2 processes at a time 
[2025-07-17T21:42:54.505+0000] {manager.py:484} INFO - Process each file at most once every 30 seconds
[2025-07-17T21:42:54.506+0000] {manager.py:485} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-07-17T21:42:54.507+0000] {manager.py:821} INFO - Searching for files in /opt/airflow/dags
[2025-07-17T21:42:54.731+0000] {manager.py:824} INFO - There are 26 files in /opt/airflow/dags
[2025-07-17T21:42:54.893+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run      Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  ----------  ----------------------
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            0           0                                                   0
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         0           0                                                   0
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       188  0.03s             0           0                                                   0
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            0           0                                                   0
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_image_pipeline.py                     191  0.02s             0           0                                                   0
/opt/airflow/dags/chatgpt_message_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           0           0                                                   0
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           0                                                   0
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/enable_cost_tracking.py                                              0           0                                                   0
/opt/airflow/dags/perplexity_pipeline.py                                               0           0                                                   0
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           0                                                   0
================================================================================
[2025-07-17T21:43:24.910+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.56s           2025-07-17T21:42:57                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.53s           2025-07-17T21:42:55                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.45s           2025-07-17T21:42:59                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.45s           2025-07-17T21:42:56                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.45s           2025-07-17T21:42:58                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.45s           2025-07-17T21:42:57                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.45s           2025-07-17T21:42:58                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.44s           2025-07-17T21:42:58                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.44s           2025-07-17T21:42:56                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.43s           2025-07-17T21:42:59                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.43s           2025-07-17T21:42:56                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.42s           2025-07-17T21:42:55                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.42s           2025-07-17T21:42:58                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.42s           2025-07-17T21:42:56                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.42s           2025-07-17T21:42:57                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.41s           2025-07-17T21:42:59                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.41s           2025-07-17T21:42:55                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.41s           2025-07-17T21:42:59                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.41s           2025-07-17T21:42:57                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.41s           2025-07-17T21:42:59                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.40s           2025-07-17T21:42:59                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.40s           2025-07-17T21:43:00                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.39s           2025-07-17T21:42:55                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.22s           2025-07-17T21:42:57                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-17T21:43:00                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:42:56                       3
================================================================================
[2025-07-17T21:43:55.042+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.52s           2025-07-17T21:43:28                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.46s           2025-07-17T21:43:28                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.46s           2025-07-17T21:43:26                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.45s           2025-07-17T21:43:26                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.45s           2025-07-17T21:43:27                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.44s           2025-07-17T21:43:29                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.44s           2025-07-17T21:43:30                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.44s           2025-07-17T21:43:26                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.44s           2025-07-17T21:43:26                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.44s           2025-07-17T21:43:27                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.43s           2025-07-17T21:43:28                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.43s           2025-07-17T21:43:27                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.43s           2025-07-17T21:43:30                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.43s           2025-07-17T21:43:30                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.42s           2025-07-17T21:43:27                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.42s           2025-07-17T21:43:27                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.42s           2025-07-17T21:43:31                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.42s           2025-07-17T21:43:29                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.42s           2025-07-17T21:43:30                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.41s           2025-07-17T21:43:29                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.41s           2025-07-17T21:43:29                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.41s           2025-07-17T21:43:29                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.40s           2025-07-17T21:43:28                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.37s           2025-07-17T21:43:31                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.27s           2025-07-17T21:43:31                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:43:27                       3
================================================================================
[2025-07-17T21:44:25.220+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.56s           2025-07-17T21:43:57                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.43s           2025-07-17T21:43:57                      32
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.40s           2025-07-17T21:43:59                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.31s           2025-07-17T21:44:02                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.31s           2025-07-17T21:43:58                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.30s           2025-07-17T21:44:01                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.30s           2025-07-17T21:43:58                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.30s           2025-07-17T21:43:58                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.30s           2025-07-17T21:44:00                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.28s           2025-07-17T21:43:59                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.28s           2025-07-17T21:44:00                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.28s           2025-07-17T21:43:59                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.28s           2025-07-17T21:43:59                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.28s           2025-07-17T21:44:01                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.28s           2025-07-17T21:44:00                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.28s           2025-07-17T21:43:59                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.28s           2025-07-17T21:43:57                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.27s           2025-07-17T21:43:59                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.27s           2025-07-17T21:43:58                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.27s           2025-07-17T21:44:00                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.27s           2025-07-17T21:43:58                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.27s           2025-07-17T21:43:57                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.26s           2025-07-17T21:44:01                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.22s           2025-07-17T21:43:59                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.13s           2025-07-17T21:44:01                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:43:58                       3
================================================================================
[2025-07-17T21:44:55.752+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.40s           2025-07-17T21:44:30                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.31s           2025-07-17T21:44:30                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.30s           2025-07-17T21:44:32                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.30s           2025-07-17T21:44:31                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.30s           2025-07-17T21:44:29                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.30s           2025-07-17T21:44:28                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.29s           2025-07-17T21:44:29                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.28s           2025-07-17T21:44:27                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.28s           2025-07-17T21:44:31                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.28s           2025-07-17T21:44:28                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.28s           2025-07-17T21:44:30                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.27s           2025-07-17T21:44:28                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.27s           2025-07-17T21:44:30                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.27s           2025-07-17T21:44:31                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.27s           2025-07-17T21:44:30                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.27s           2025-07-17T21:44:29                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.27s           2025-07-17T21:44:30                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.27s           2025-07-17T21:44:31                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.27s           2025-07-17T21:44:31                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.26s           2025-07-17T21:44:31                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.26s           2025-07-17T21:44:28                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.25s           2025-07-17T21:44:28                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.25s           2025-07-17T21:44:27                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.23s           2025-07-17T21:44:29                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.14s           2025-07-17T21:44:32                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:44:29                       3
================================================================================
[2025-07-17T21:45:26.706+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.39s           2025-07-17T21:45:01                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.33s           2025-07-17T21:44:59                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.32s           2025-07-17T21:44:59                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.31s           2025-07-17T21:44:59                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.31s           2025-07-17T21:44:58                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.31s           2025-07-17T21:44:58                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.30s           2025-07-17T21:45:01                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.29s           2025-07-17T21:44:59                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.29s           2025-07-17T21:45:02                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.29s           2025-07-17T21:45:03                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.29s           2025-07-17T21:45:00                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.29s           2025-07-17T21:44:59                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.28s           2025-07-17T21:44:58                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.28s           2025-07-17T21:45:02                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.27s           2025-07-17T21:45:00                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.27s           2025-07-17T21:45:02                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.27s           2025-07-17T21:45:01                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.27s           2025-07-17T21:45:01                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.27s           2025-07-17T21:45:02                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.26s           2025-07-17T21:45:01                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.26s           2025-07-17T21:45:02                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.26s           2025-07-17T21:45:02                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.26s           2025-07-17T21:45:02                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.20s           2025-07-17T21:45:00                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.13s           2025-07-17T21:45:03                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T21:44:59                       3
================================================================================
[2025-07-17T21:45:28.382+0000] {manager.py:465} INFO - Exiting gracefully upon receiving signal 15
[2025-07-17T21:55:22.312+0000] {manager.py:483} INFO - Processing files using up to 2 processes at a time 
[2025-07-17T21:55:22.313+0000] {manager.py:484} INFO - Process each file at most once every 30 seconds
[2025-07-17T21:55:22.314+0000] {manager.py:485} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-07-17T21:55:22.315+0000] {manager.py:821} INFO - Searching for files in /opt/airflow/dags
[2025-07-17T21:55:22.584+0000] {manager.py:824} INFO - There are 26 files in /opt/airflow/dags
[2025-07-17T21:55:22.759+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run      Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  ----------  ----------------------
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            0           0                                                   0
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         0           0                                                   0
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       189  0.04s             0           0                                                   0
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            0           0                                                   0
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_image_pipeline.py                     192  0.02s             0           0                                                   0
/opt/airflow/dags/chatgpt_message_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           0           0                                                   0
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           0                                                   0
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/enable_cost_tracking.py                                              0           0                                                   0
/opt/airflow/dags/perplexity_pipeline.py                                               0           0                                                   0
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           0                                                   0
================================================================================
[2025-07-17T21:55:53.370+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_image_pipeline.py                     328  0.00s             1           0  0.60s           2025-07-17T21:55:23                      32
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.55s           2025-07-17T21:55:25                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       325  0.01s             1           0  0.46s           2025-07-17T21:55:23                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.45s           2025-07-17T21:55:25                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.44s           2025-07-17T21:55:27                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.44s           2025-07-17T21:55:23                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.44s           2025-07-17T21:55:23                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.43s           2025-07-17T21:55:24                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.43s           2025-07-17T21:55:25                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.42s           2025-07-17T21:55:25                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.41s           2025-07-17T21:55:26                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.41s           2025-07-17T21:55:27                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.41s           2025-07-17T21:55:24                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.41s           2025-07-17T21:55:26                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.41s           2025-07-17T21:55:24                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.40s           2025-07-17T21:55:26                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.39s           2025-07-17T21:55:24                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.39s           2025-07-17T21:55:27                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.38s           2025-07-17T21:55:24                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.37s           2025-07-17T21:55:26                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.36s           2025-07-17T21:55:27                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.35s           2025-07-17T21:55:26                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.35s           2025-07-17T21:55:27                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.21s           2025-07-17T21:55:25                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-17T21:55:27                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:55:24                       3
================================================================================
[2025-07-17T21:56:24.117+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.45s           2025-07-17T21:55:56                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.43s           2025-07-17T21:55:55                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       461  0.01s             1           0  0.43s           2025-07-17T21:55:53                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.41s           2025-07-17T21:55:54                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.40s           2025-07-17T21:55:55                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.40s           2025-07-17T21:55:54                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.39s           2025-07-17T21:55:57                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                     464  0.00s             1           0  0.39s           2025-07-17T21:55:53                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.39s           2025-07-17T21:55:57                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.38s           2025-07-17T21:55:55                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.38s           2025-07-17T21:55:56                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.38s           2025-07-17T21:55:58                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.38s           2025-07-17T21:55:55                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.38s           2025-07-17T21:55:58                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.37s           2025-07-17T21:55:54                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.37s           2025-07-17T21:55:58                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.36s           2025-07-17T21:55:58                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.36s           2025-07-17T21:55:57                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.35s           2025-07-17T21:55:54                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.35s           2025-07-17T21:55:56                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.35s           2025-07-17T21:55:57                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.35s           2025-07-17T21:55:56                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.34s           2025-07-17T21:55:57                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.34s           2025-07-17T21:55:56                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.24s           2025-07-17T21:55:58                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:55:55                       3
================================================================================
[2025-07-17T21:56:54.704+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_image_pipeline.py                     601  0.00s             1           0  0.51s           2025-07-17T21:56:24                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       598  0.01s             1           0  0.39s           2025-07-17T21:56:24                      32
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.34s           2025-07-17T21:56:26                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.30s           2025-07-17T21:56:27                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.30s           2025-07-17T21:56:28                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.28s           2025-07-17T21:56:25                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.28s           2025-07-17T21:56:28                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.28s           2025-07-17T21:56:25                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.28s           2025-07-17T21:56:27                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.28s           2025-07-17T21:56:29                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.27s           2025-07-17T21:56:29                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.26s           2025-07-17T21:56:28                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.26s           2025-07-17T21:56:26                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.25s           2025-07-17T21:56:26                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.25s           2025-07-17T21:56:24                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.25s           2025-07-17T21:56:25                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.25s           2025-07-17T21:56:28                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.25s           2025-07-17T21:56:27                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.24s           2025-07-17T21:56:27                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.24s           2025-07-17T21:56:26                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.24s           2025-07-17T21:56:28                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T21:56:25                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.22s           2025-07-17T21:56:26                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.22s           2025-07-17T21:56:26                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-17T21:56:29                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:56:25                       3
================================================================================
[2025-07-17T21:57:25.116+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.32s           2025-07-17T21:56:57                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.30s           2025-07-17T21:56:59                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.28s           2025-07-17T21:56:59                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.28s           2025-07-17T21:56:58                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                     737  0.00s             1           0  0.27s           2025-07-17T21:56:54                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.26s           2025-07-17T21:56:56                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.26s           2025-07-17T21:56:58                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.26s           2025-07-17T21:56:57                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       734  0.02s             1           0  0.26s           2025-07-17T21:56:54                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.26s           2025-07-17T21:56:57                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.25s           2025-07-17T21:56:55                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.25s           2025-07-17T21:56:59                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.25s           2025-07-17T21:56:58                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.25s           2025-07-17T21:56:57                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.25s           2025-07-17T21:56:56                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.24s           2025-07-17T21:56:56                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.24s           2025-07-17T21:56:55                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T21:56:55                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.23s           2025-07-17T21:56:58                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.23s           2025-07-17T21:56:56                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.23s           2025-07-17T21:56:58                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.22s           2025-07-17T21:56:57                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.22s           2025-07-17T21:56:55                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.18s           2025-07-17T21:56:56                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-17T21:56:59                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:56:55                       3
================================================================================
[2025-07-17T21:57:55.541+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.31s           2025-07-17T21:57:28                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.31s           2025-07-17T21:57:29                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.28s           2025-07-17T21:57:25                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                     873  0.00s             1           0  0.28s           2025-07-17T21:57:25                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.27s           2025-07-17T21:57:30                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       870  0.01s             1           0  0.26s           2025-07-17T21:57:25                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.26s           2025-07-17T21:57:25                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.24s           2025-07-17T21:57:30                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.24s           2025-07-17T21:57:26                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.24s           2025-07-17T21:57:25                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.23s           2025-07-17T21:57:29                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.23s           2025-07-17T21:57:28                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.23s           2025-07-17T21:57:28                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.23s           2025-07-17T21:57:26                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.23s           2025-07-17T21:57:28                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.23s           2025-07-17T21:57:28                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.23s           2025-07-17T21:57:28                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.22s           2025-07-17T21:57:29                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.22s           2025-07-17T21:57:29                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.22s           2025-07-17T21:57:26                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.22s           2025-07-17T21:57:27                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.22s           2025-07-17T21:57:26                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.22s           2025-07-17T21:57:26                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.17s           2025-07-17T21:57:27                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-17T21:57:30                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:57:26                       3
================================================================================
[2025-07-17T21:58:26.032+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.34s           2025-07-17T21:57:59                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.30s           2025-07-17T21:57:59                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.30s           2025-07-17T21:57:59                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.28s           2025-07-17T21:58:00                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.28s           2025-07-17T21:58:00                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.27s           2025-07-17T21:57:57                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.27s           2025-07-17T21:57:59                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.27s           2025-07-17T21:57:57                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.27s           2025-07-17T21:57:57                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.26s           2025-07-17T21:57:58                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.26s           2025-07-17T21:57:56                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.26s           2025-07-17T21:57:56                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.25s           2025-07-17T21:57:59                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.25s           2025-07-17T21:57:59                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.25s           2025-07-17T21:57:59                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                    1009  0.00s             1           0  0.25s           2025-07-17T21:57:55                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.24s           2025-07-17T21:58:00                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.24s           2025-07-17T21:57:59                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.24s           2025-07-17T21:57:57                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T21:57:56                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.24s           2025-07-17T21:57:56                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.24s           2025-07-17T21:58:00                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py      1006  0.01s             1           0  0.24s           2025-07-17T21:57:55                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.23s           2025-07-17T21:57:56                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-17T21:58:00                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T21:57:56                       3
================================================================================
[2025-07-17T21:58:52.904+0000] {manager.py:465} INFO - Exiting gracefully upon receiving signal 15
[2025-07-17T22:00:33.915+0000] {manager.py:483} INFO - Processing files using up to 2 processes at a time 
[2025-07-17T22:00:33.915+0000] {manager.py:484} INFO - Process each file at most once every 30 seconds
[2025-07-17T22:00:33.916+0000] {manager.py:485} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-07-17T22:00:33.916+0000] {manager.py:821} INFO - Searching for files in /opt/airflow/dags
[2025-07-17T22:00:34.131+0000] {manager.py:824} INFO - There are 26 files in /opt/airflow/dags
[2025-07-17T22:00:34.269+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run      Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  ----------  ----------------------
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            0           0                                                   0
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         0           0                                                   0
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       182  0.03s             0           0                                                   0
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            0           0                                                   0
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_image_pipeline.py                     185  0.02s             0           0                                                   0
/opt/airflow/dags/chatgpt_message_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           0           0                                                   0
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           0                                                   0
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/enable_cost_tracking.py                                              0           0                                                   0
/opt/airflow/dags/perplexity_pipeline.py                                               0           0                                                   0
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           0                                                   0
================================================================================
[2025-07-17T22:01:04.852+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.65s           2025-07-17T22:00:34                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       324  0.00s             1           0  0.50s           2025-07-17T22:00:34                      32
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.50s           2025-07-17T22:00:37                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.46s           2025-07-17T22:00:36                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.45s           2025-07-17T22:00:39                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.44s           2025-07-17T22:00:35                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.44s           2025-07-17T22:00:37                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.43s           2025-07-17T22:00:39                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.41s           2025-07-17T22:00:37                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.41s           2025-07-17T22:00:35                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.41s           2025-07-17T22:00:35                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.40s           2025-07-17T22:00:36                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.40s           2025-07-17T22:00:35                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.39s           2025-07-17T22:00:38                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.39s           2025-07-17T22:00:36                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.39s           2025-07-17T22:00:37                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.38s           2025-07-17T22:00:37                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.38s           2025-07-17T22:00:37                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.38s           2025-07-17T22:00:39                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.36s           2025-07-17T22:00:36                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.36s           2025-07-17T22:00:38                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.36s           2025-07-17T22:00:38                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.35s           2025-07-17T22:00:38                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.19s           2025-07-17T22:00:36                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-17T22:00:39                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.09s           2025-07-17T22:00:35                       3
================================================================================
[2025-07-17T22:01:35.655+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.61s           2025-07-17T22:01:08                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.50s           2025-07-17T22:01:06                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.48s           2025-07-17T22:01:06                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.47s           2025-07-17T22:01:08                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.47s           2025-07-17T22:01:06                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.46s           2025-07-17T22:01:06                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.46s           2025-07-17T22:01:08                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.46s           2025-07-17T22:01:09                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.45s           2025-07-17T22:01:07                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.45s           2025-07-17T22:01:10                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.45s           2025-07-17T22:01:05                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.44s           2025-07-17T22:01:09                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.44s           2025-07-17T22:01:08                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                     463  0.00s             1           0  0.44s           2025-07-17T22:01:05                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.43s           2025-07-17T22:01:05                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.43s           2025-07-17T22:01:07                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.43s           2025-07-17T22:01:07                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.42s           2025-07-17T22:01:09                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.42s           2025-07-17T22:01:10                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.42s           2025-07-17T22:01:09                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.41s           2025-07-17T22:01:10                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.40s           2025-07-17T22:01:07                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.39s           2025-07-17T22:01:10                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       460  0.18s             1           0  0.38s           2025-07-17T22:01:05                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.25s           2025-07-17T22:01:11                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T22:01:06                       3
================================================================================
[2025-07-17T22:02:06.406+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       596  0.01s             1           0  0.37s           2025-07-17T22:01:35                      32
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.34s           2025-07-17T22:01:39                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.29s           2025-07-17T22:01:41                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.28s           2025-07-17T22:01:37                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.27s           2025-07-17T22:01:41                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.27s           2025-07-17T22:01:41                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.27s           2025-07-17T22:01:41                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.26s           2025-07-17T22:01:39                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                     599  0.00s             1           0  0.25s           2025-07-17T22:01:35                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.25s           2025-07-17T22:01:36                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.24s           2025-07-17T22:01:36                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.24s           2025-07-17T22:01:39                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T22:01:36                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.24s           2025-07-17T22:01:39                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.24s           2025-07-17T22:01:39                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.24s           2025-07-17T22:01:36                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.23s           2025-07-17T22:01:39                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.23s           2025-07-17T22:01:37                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.23s           2025-07-17T22:01:39                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.23s           2025-07-17T22:01:37                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.23s           2025-07-17T22:01:39                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.22s           2025-07-17T22:01:38                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.22s           2025-07-17T22:01:37                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.19s           2025-07-17T22:01:37                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.14s           2025-07-17T22:01:41                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T22:01:36                       3
================================================================================
[2025-07-17T22:02:36.807+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.31s           2025-07-17T22:02:09                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                     735  0.00s             1           0  0.25s           2025-07-17T22:02:06                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.25s           2025-07-17T22:02:11                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       732  0.01s             1           0  0.25s           2025-07-17T22:02:06                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.24s           2025-07-17T22:02:12                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.24s           2025-07-17T22:02:07                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T22:02:07                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.24s           2025-07-17T22:02:11                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.23s           2025-07-17T22:02:10                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.23s           2025-07-17T22:02:09                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.23s           2025-07-17T22:02:07                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.23s           2025-07-17T22:02:10                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.23s           2025-07-17T22:02:11                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.23s           2025-07-17T22:02:10                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.23s           2025-07-17T22:02:10                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.23s           2025-07-17T22:02:07                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.22s           2025-07-17T22:02:09                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.22s           2025-07-17T22:02:09                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.22s           2025-07-17T22:02:07                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.22s           2025-07-17T22:02:08                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.22s           2025-07-17T22:02:06                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.22s           2025-07-17T22:02:08                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.22s           2025-07-17T22:02:07                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.18s           2025-07-17T22:02:08                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-17T22:02:12                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T22:02:07                       3
================================================================================
[2025-07-17T22:03:07.107+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.32s           2025-07-17T22:02:40                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                     871  0.00s             1           0  0.26s           2025-07-17T22:02:37                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.25s           2025-07-17T22:02:42                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py       868  0.01s             1           0  0.25s           2025-07-17T22:02:37                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.24s           2025-07-17T22:02:40                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.24s           2025-07-17T22:02:42                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.24s           2025-07-17T22:02:38                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.24s           2025-07-17T22:02:40                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.23s           2025-07-17T22:02:37                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.23s           2025-07-17T22:02:37                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.23s           2025-07-17T22:02:38                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.23s           2025-07-17T22:02:40                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.23s           2025-07-17T22:02:40                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.22s           2025-07-17T22:02:37                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.22s           2025-07-17T22:02:40                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.22s           2025-07-17T22:02:38                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.22s           2025-07-17T22:02:37                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.22s           2025-07-17T22:02:42                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.22s           2025-07-17T22:02:38                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.22s           2025-07-17T22:02:40                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.22s           2025-07-17T22:02:40                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.21s           2025-07-17T22:02:42                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.21s           2025-07-17T22:02:38                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.18s           2025-07-17T22:02:38                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.10s           2025-07-17T22:02:42                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T22:02:37                       3
================================================================================
[2025-07-17T22:03:37.417+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.33s           2025-07-17T22:03:10                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py      1004  0.01s             1           0  0.27s           2025-07-17T22:03:07                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.27s           2025-07-17T22:03:12                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.26s           2025-07-17T22:03:10                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.24s           2025-07-17T22:03:12                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                    1007  0.00s             1           0  0.24s           2025-07-17T22:03:07                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.24s           2025-07-17T22:03:08                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.24s           2025-07-17T22:03:07                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T22:03:08                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.24s           2025-07-17T22:03:08                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.23s           2025-07-17T22:03:10                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.23s           2025-07-17T22:03:10                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.23s           2025-07-17T22:03:11                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.23s           2025-07-17T22:03:11                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.23s           2025-07-17T22:03:12                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.23s           2025-07-17T22:03:08                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.23s           2025-07-17T22:03:08                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.22s           2025-07-17T22:03:07                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.22s           2025-07-17T22:03:11                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.22s           2025-07-17T22:03:10                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.22s           2025-07-17T22:03:12                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.22s           2025-07-17T22:03:08                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.21s           2025-07-17T22:03:09                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.18s           2025-07-17T22:03:08                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.10s           2025-07-17T22:03:12                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T22:03:08                       3
================================================================================
[2025-07-17T22:04:07.610+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  1.20s           2025-07-17T22:03:43                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.93s           2025-07-17T22:03:42                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.85s           2025-07-17T22:03:43                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.57s           2025-07-17T22:03:41                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.45s           2025-07-17T22:03:43                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.44s           2025-07-17T22:03:43                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.35s           2025-07-17T22:03:43                      32
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.34s           2025-07-17T22:03:41                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.30s           2025-07-17T22:03:44                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.29s           2025-07-17T22:03:37                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.28s           2025-07-17T22:03:38                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.27s           2025-07-17T22:03:37                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.27s           2025-07-17T22:03:38                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.27s           2025-07-17T22:03:38                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.27s           2025-07-17T22:03:38                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.26s           2025-07-17T22:03:37                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.26s           2025-07-17T22:03:41                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.25s           2025-07-17T22:03:38                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.24s           2025-07-17T22:03:39                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.23s           2025-07-17T22:03:40                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.23s           2025-07-17T22:03:39                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.23s           2025-07-17T22:03:40                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.23s           2025-07-17T22:03:39                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.19s           2025-07-17T22:03:39                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.13s           2025-07-17T22:03:44                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T22:03:38                       3
================================================================================
[2025-07-17T22:04:37.991+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.33s           2025-07-17T22:04:11                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.27s           2025-07-17T22:04:10                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.26s           2025-07-17T22:04:09                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.26s           2025-07-17T22:04:08                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.26s           2025-07-17T22:04:08                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.26s           2025-07-17T22:04:10                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.25s           2025-07-17T22:04:09                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.25s           2025-07-17T22:04:09                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.25s           2025-07-17T22:04:11                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.24s           2025-07-17T22:04:10                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.24s           2025-07-17T22:04:14                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.24s           2025-07-17T22:04:10                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.24s           2025-07-17T22:04:12                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.23s           2025-07-17T22:04:14                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.23s           2025-07-17T22:04:11                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.23s           2025-07-17T22:04:13                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.23s           2025-07-17T22:04:14                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.23s           2025-07-17T22:04:13                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.23s           2025-07-17T22:04:13                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.23s           2025-07-17T22:04:12                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.23s           2025-07-17T22:04:09                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.23s           2025-07-17T22:04:09                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.22s           2025-07-17T22:04:13                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.20s           2025-07-17T22:04:10                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.10s           2025-07-17T22:04:14                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.08s           2025-07-17T22:04:09                       3
================================================================================
[2025-07-17T22:05:08.379+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.33s           2025-07-17T22:04:42                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.28s           2025-07-17T22:04:44                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.27s           2025-07-17T22:04:44                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.26s           2025-07-17T22:04:39                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.26s           2025-07-17T22:04:39                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.25s           2025-07-17T22:04:39                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.24s           2025-07-17T22:04:42                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.24s           2025-07-17T22:04:39                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.24s           2025-07-17T22:04:40                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.24s           2025-07-17T22:04:43                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.24s           2025-07-17T22:04:43                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.23s           2025-07-17T22:04:42                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.23s           2025-07-17T22:04:40                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.23s           2025-07-17T22:04:40                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.23s           2025-07-17T22:04:40                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.23s           2025-07-17T22:04:42                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.23s           2025-07-17T22:04:40                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.22s           2025-07-17T22:04:41                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.22s           2025-07-17T22:04:44                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.22s           2025-07-17T22:04:44                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.22s           2025-07-17T22:04:44                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.22s           2025-07-17T22:04:42                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.22s           2025-07-17T22:04:39                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.18s           2025-07-17T22:04:40                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.14s           2025-07-17T22:04:45                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T22:04:40                       3
================================================================================
[2025-07-17T22:05:34.662+0000] {manager.py:821} INFO - Searching for files in /opt/airflow/dags
[2025-07-17T22:05:34.805+0000] {manager.py:824} INFO - There are 26 files in /opt/airflow/dags
[2025-07-17T22:05:38.906+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.33s           2025-07-17T22:05:12                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.27s           2025-07-17T22:05:14                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.26s           2025-07-17T22:05:09                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.26s           2025-07-17T22:05:09                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.26s           2025-07-17T22:05:10                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.26s           2025-07-17T22:05:10                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.26s           2025-07-17T22:05:14                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.25s           2025-07-17T22:05:12                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.25s           2025-07-17T22:05:11                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.25s           2025-07-17T22:05:13                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.25s           2025-07-17T22:05:10                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.24s           2025-07-17T22:05:10                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.24s           2025-07-17T22:05:11                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.24s           2025-07-17T22:05:13                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.24s           2025-07-17T22:05:14                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.24s           2025-07-17T22:05:09                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.23s           2025-07-17T22:05:12                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.23s           2025-07-17T22:05:14                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.23s           2025-07-17T22:05:11                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.23s           2025-07-17T22:05:15                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.23s           2025-07-17T22:05:14                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.23s           2025-07-17T22:05:10                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.22s           2025-07-17T22:05:15                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.19s           2025-07-17T22:05:11                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-17T22:05:15                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.06s           2025-07-17T22:05:10                       3
================================================================================
[2025-07-17T22:06:09.437+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              1           0  0.33s           2025-07-17T22:05:43                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.32s           2025-07-17T22:05:41                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.31s           2025-07-17T22:05:44                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.30s           2025-07-17T22:05:44                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.30s           2025-07-17T22:05:40                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.30s           2025-07-17T22:05:40                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.29s           2025-07-17T22:05:42                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.26s           2025-07-17T22:05:45                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.26s           2025-07-17T22:05:43                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.26s           2025-07-17T22:05:45                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.26s           2025-07-17T22:05:43                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.26s           2025-07-17T22:05:45                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.26s           2025-07-17T22:05:40                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.25s           2025-07-17T22:05:43                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.25s           2025-07-17T22:05:41                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.25s           2025-07-17T22:05:40                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.25s           2025-07-17T22:05:45                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.24s           2025-07-17T22:05:45                      32
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    1           0  0.24s           2025-07-17T22:05:41                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-17T22:05:40                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.23s           2025-07-17T22:05:41                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.23s           2025-07-17T22:05:40                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.22s           2025-07-17T22:05:41                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.20s           2025-07-17T22:05:41                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-17T22:05:46                       5
/opt/airflow/dags/enable_cost_tracking.py                                              0           0  0.05s           2025-07-17T22:05:40                       3
================================================================================
[2025-07-17T22:06:37.637+0000] {manager.py:465} INFO - Exiting gracefully upon receiving signal 15
