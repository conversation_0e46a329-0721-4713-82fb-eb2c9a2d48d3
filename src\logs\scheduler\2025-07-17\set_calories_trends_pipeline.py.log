[2025-07-17T21:34:33.193+0000] {processor.py:186} INFO - Started process (PID=311) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:34:33.194+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:34:33.196+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.196+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:34:33.270+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.266+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:34:33.271+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:34:33.290+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.103 seconds
[2025-07-17T21:35:05.047+0000] {processor.py:186} INFO - Started process (PID=447) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:05.048+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:35:05.050+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.050+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:05.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.239+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:35:05.243+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:05.260+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.219 seconds
[2025-07-17T21:35:35.889+0000] {processor.py:186} INFO - Started process (PID=583) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:35.889+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:35:35.892+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:35.957+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.951+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:35:35.958+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:35.975+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.093 seconds
[2025-07-17T21:36:06.260+0000] {processor.py:186} INFO - Started process (PID=719) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:06.262+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:36:06.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.264+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:06.322+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.317+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:36:06.323+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:06.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.090 seconds
[2025-07-17T21:36:36.520+0000] {processor.py:186} INFO - Started process (PID=850) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:36.521+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:36:36.523+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.523+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:36.587+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.583+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:36:36.588+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:36.606+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.093 seconds
[2025-07-17T21:37:07.445+0000] {processor.py:186} INFO - Started process (PID=993) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:37:07.446+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:37:07.450+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.450+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:37:07.532+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.527+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:37:07.533+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:37:07.562+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.124 seconds
[2025-07-17T21:37:37.966+0000] {processor.py:186} INFO - Started process (PID=1129) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:37:37.967+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:37:37.970+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.969+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:37:38.045+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:38.041+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:37:38.046+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:37:38.065+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.105 seconds
[2025-07-17T21:38:08.485+0000] {processor.py:186} INFO - Started process (PID=1265) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:38:08.486+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:38:08.488+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.488+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:38:08.548+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.542+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:38:08.549+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:38:08.568+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.089 seconds
[2025-07-17T21:38:39.354+0000] {processor.py:186} INFO - Started process (PID=1401) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:38:39.354+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:38:39.356+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.356+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:38:39.422+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.416+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:38:39.423+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:38:39.442+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.094 seconds
[2025-07-17T21:39:09.806+0000] {processor.py:186} INFO - Started process (PID=1537) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:39:09.807+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:39:09.810+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.810+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:39:09.875+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.868+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:39:09.876+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:39:09.896+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.095 seconds
[2025-07-17T21:39:40.459+0000] {processor.py:186} INFO - Started process (PID=1673) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:39:40.460+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:39:40.462+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.461+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:39:40.536+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.528+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:39:40.537+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:39:40.559+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.106 seconds
[2025-07-17T21:40:11.200+0000] {processor.py:186} INFO - Started process (PID=1807) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:40:11.201+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:40:11.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.203+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:40:11.286+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.279+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:40:11.286+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:40:11.312+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.120 seconds
[2025-07-17T21:40:42.471+0000] {processor.py:186} INFO - Started process (PID=1945) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:40:42.472+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:40:42.475+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.475+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:40:42.570+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.564+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:40:42.571+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:40:42.591+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.129 seconds
[2025-07-17T21:43:00.020+0000] {processor.py:186} INFO - Started process (PID=317) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:43:00.022+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:43:00.024+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:00.024+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:43:00.087+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:00.081+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:43:00.088+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:43:00.110+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.096 seconds
[2025-07-17T21:43:30.840+0000] {processor.py:186} INFO - Started process (PID=448) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:43:30.841+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:43:30.844+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.843+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:43:31.055+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.050+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:43:31.056+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:43:31.071+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.239 seconds
[2025-07-17T21:44:01.559+0000] {processor.py:186} INFO - Started process (PID=586) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:44:01.560+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:44:01.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.563+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:44:01.633+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.628+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:44:01.633+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:44:01.654+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.103 seconds
[2025-07-17T21:44:32.034+0000] {processor.py:186} INFO - Started process (PID=722) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:44:32.036+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:44:32.039+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:32.039+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:44:32.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:32.110+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:44:32.118+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:44:32.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.111 seconds
[2025-07-17T21:45:02.990+0000] {processor.py:186} INFO - Started process (PID=861) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:45:02.994+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:45:02.998+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.997+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:45:03.067+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:03.061+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:45:03.068+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:45:03.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.107 seconds
[2025-07-17T21:55:27.742+0000] {processor.py:186} INFO - Started process (PID=318) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:55:27.743+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:55:27.745+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.745+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:55:27.812+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.808+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:55:27.813+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:55:27.831+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.094 seconds
[2025-07-17T21:55:58.522+0000] {processor.py:186} INFO - Started process (PID=454) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:55:58.523+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:55:58.525+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.525+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:55:58.721+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.716+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:55:58.722+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:55:58.735+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.219 seconds
[2025-07-17T21:56:29.203+0000] {processor.py:186} INFO - Started process (PID=593) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:56:29.204+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:56:29.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:29.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:56:29.263+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:29.258+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:56:29.264+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:56:29.283+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.085 seconds
[2025-07-17T21:56:59.592+0000] {processor.py:186} INFO - Started process (PID=729) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:56:59.593+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:56:59.595+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:59.595+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:56:59.653+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:59.649+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:56:59.654+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:56:59.673+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.087 seconds
[2025-07-17T21:57:30.034+0000] {processor.py:186} INFO - Started process (PID=865) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:57:30.035+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:57:30.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:30.037+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:57:30.097+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:30.092+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:57:30.098+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:57:30.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.088 seconds
[2025-07-17T21:58:00.504+0000] {processor.py:186} INFO - Started process (PID=1001) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:58:00.505+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:58:00.508+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.507+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:58:00.570+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.566+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:58:00.571+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:58:00.590+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.092 seconds
[2025-07-17T21:58:30.813+0000] {processor.py:186} INFO - Started process (PID=1137) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:58:30.814+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:58:30.816+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.816+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:58:30.874+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.870+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:58:30.875+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:58:30.893+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.085 seconds
[2025-07-17T22:00:39.216+0000] {processor.py:186} INFO - Started process (PID=311) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:00:39.217+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:00:39.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:39.219+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:00:39.282+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:39.278+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:00:39.283+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:00:39.303+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.093 seconds
[2025-07-17T22:01:10.978+0000] {processor.py:186} INFO - Started process (PID=449) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:01:10.979+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:01:10.982+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.981+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:01:11.180+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:11.173+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:01:11.181+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:01:11.197+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.225 seconds
[2025-07-17T22:01:41.595+0000] {processor.py:186} INFO - Started process (PID=585) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:01:41.596+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:01:41.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.598+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:01:41.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.667+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:01:41.674+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:01:41.702+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.113 seconds
[2025-07-17T22:02:12.230+0000] {processor.py:186} INFO - Started process (PID=721) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:02:12.231+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:02:12.233+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:12.233+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:02:12.294+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:12.287+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:02:12.295+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:02:12.312+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.088 seconds
[2025-07-17T22:02:42.600+0000] {processor.py:186} INFO - Started process (PID=857) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:02:42.601+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:02:42.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.603+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:02:42.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.652+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:02:42.659+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:02:42.676+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.081 seconds
[2025-07-17T22:03:12.786+0000] {processor.py:186} INFO - Started process (PID=991) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:03:12.787+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:03:12.789+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.789+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:03:12.844+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.839+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:03:12.844+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:03:12.863+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.083 seconds
[2025-07-17T22:03:44.025+0000] {processor.py:186} INFO - Started process (PID=1127) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:03:44.027+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:03:44.029+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:44.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:03:44.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:44.099+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:03:44.104+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:03:44.124+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.105 seconds
[2025-07-17T22:04:14.512+0000] {processor.py:186} INFO - Started process (PID=1265) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:04:14.513+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:04:14.516+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:14.515+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:04:14.569+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:14.564+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:04:14.569+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:04:14.585+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.079 seconds
[2025-07-17T22:04:44.957+0000] {processor.py:186} INFO - Started process (PID=1401) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:04:44.958+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:04:44.960+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:04:45.033+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:45.028+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:04:45.034+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:04:45.054+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.107 seconds
[2025-07-17T22:05:15.215+0000] {processor.py:186} INFO - Started process (PID=1535) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:05:15.216+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:05:15.218+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:15.218+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:05:15.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:15.272+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:05:15.279+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:05:15.297+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.087 seconds
[2025-07-17T22:05:46.012+0000] {processor.py:186} INFO - Started process (PID=1673) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:05:46.013+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:05:46.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:46.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:05:46.072+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:46.067+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:05:46.073+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:05:46.090+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.085 seconds
[2025-07-17T22:06:16.401+0000] {processor.py:186} INFO - Started process (PID=1809) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:06:16.402+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:06:16.405+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:16.404+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:06:16.462+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:16.458+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:06:16.463+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:06:16.481+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.085 seconds
[2025-07-17T22:07:30.704+0000] {processor.py:186} INFO - Started process (PID=311) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:07:30.705+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:07:30.707+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.707+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:07:30.764+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.757+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:07:30.765+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:07:30.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.086 seconds
[2025-07-17T22:08:02.379+0000] {processor.py:186} INFO - Started process (PID=447) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:08:02.380+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:08:02.382+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:02.382+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:08:02.556+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:02.551+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:08:02.557+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:08:02.571+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.198 seconds
[2025-07-17T22:08:33.316+0000] {processor.py:186} INFO - Started process (PID=583) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:08:33.317+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:08:33.321+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:33.320+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:08:33.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:33.389+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:08:33.395+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:08:33.419+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.111 seconds
[2025-07-17T22:09:04.187+0000] {processor.py:186} INFO - Started process (PID=719) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:09:04.188+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:09:04.190+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:04.190+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:09:04.257+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:04.251+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:09:04.257+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:09:04.277+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.096 seconds
[2025-07-17T22:09:34.865+0000] {processor.py:186} INFO - Started process (PID=857) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:09:34.867+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:09:34.869+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:34.869+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:09:34.931+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:34.926+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:09:34.932+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:09:34.952+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.092 seconds
[2025-07-17T22:10:05.273+0000] {processor.py:186} INFO - Started process (PID=993) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:10:05.274+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:10:05.277+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:05.277+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:10:05.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:05.332+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:10:05.339+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:10:05.359+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.091 seconds
[2025-07-17T22:10:36.154+0000] {processor.py:186} INFO - Started process (PID=1129) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:10:36.155+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:10:36.158+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:36.157+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:10:36.218+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:36.214+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:10:36.219+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:10:36.239+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.091 seconds
[2025-07-17T22:11:07.159+0000] {processor.py:186} INFO - Started process (PID=1265) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:11:07.160+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:11:07.162+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:07.162+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:11:07.224+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:07.219+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:11:07.224+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:11:07.242+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.089 seconds
[2025-07-17T22:11:38.178+0000] {processor.py:186} INFO - Started process (PID=1401) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:11:38.179+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:11:38.182+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:38.181+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:11:38.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:38.244+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:11:38.252+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:11:38.276+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.105 seconds
[2025-07-17T22:12:09.007+0000] {processor.py:186} INFO - Started process (PID=1530) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:12:09.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:12:09.009+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:09.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:12:09.075+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:09.070+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:12:09.076+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:12:09.092+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.091 seconds
[2025-07-17T22:12:39.223+0000] {processor.py:186} INFO - Started process (PID=1656) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:12:39.223+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:12:39.225+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:39.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:12:39.279+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:39.273+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:12:39.280+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:12:39.297+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.080 seconds
