[2025-07-17T22:21:50.672+0000] {processor.py:186} INFO - Started process (PID=4014) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:21:50.673+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:21:50.674+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:50.674+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:21:50.741+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:50.736+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:21:50.742+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:21:50.760+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.095 seconds
