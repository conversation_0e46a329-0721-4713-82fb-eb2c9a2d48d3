[2025-07-17T21:34:33.193+0000] {processor.py:186} INFO - Started process (PID=311) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:34:33.194+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:34:33.196+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.196+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:34:33.270+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.266+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:34:33.271+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:34:33.290+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.103 seconds
[2025-07-17T21:35:05.047+0000] {processor.py:186} INFO - Started process (PID=447) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:05.048+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:35:05.050+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.050+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:05.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:05.239+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:35:05.243+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:05.260+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.219 seconds
[2025-07-17T21:35:35.889+0000] {processor.py:186} INFO - Started process (PID=583) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:35.889+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:35:35.892+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:35.957+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.951+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:35:35.958+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:35:35.975+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.093 seconds
[2025-07-17T21:36:06.260+0000] {processor.py:186} INFO - Started process (PID=719) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:06.262+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:36:06.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.264+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:06.322+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.317+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:36:06.323+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:06.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.090 seconds
[2025-07-17T21:36:36.520+0000] {processor.py:186} INFO - Started process (PID=850) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:36.521+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T21:36:36.523+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.523+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:36.587+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.583+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T21:36:36.588+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T21:36:36.606+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.093 seconds
