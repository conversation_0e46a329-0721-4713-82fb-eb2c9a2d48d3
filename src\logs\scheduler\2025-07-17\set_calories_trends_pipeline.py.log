[2025-07-17T22:41:32.417+0000] {processor.py:186} INFO - Started process (PID=306) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:41:32.418+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:41:32.421+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.421+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:41:32.498+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.492+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:41:32.500+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:41:32.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.111 seconds
[2025-07-17T22:42:03.181+0000] {processor.py:186} INFO - Started process (PID=439) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:42:03.182+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:42:03.184+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:42:03.372+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.366+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:42:03.373+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:42:03.386+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.210 seconds
[2025-07-17T22:42:34.185+0000] {processor.py:186} INFO - Started process (PID=568) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:42:34.186+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:42:34.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.187+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:42:34.254+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.250+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:42:34.255+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:42:34.269+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.090 seconds
[2025-07-17T22:43:04.530+0000] {processor.py:186} INFO - Started process (PID=699) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:43:04.531+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:43:04.532+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.532+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:43:04.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.600+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:43:04.607+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:43:04.624+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.099 seconds
[2025-07-17T22:43:35.784+0000] {processor.py:186} INFO - Started process (PID=830) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:43:35.786+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:43:35.787+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.787+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:43:35.857+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.850+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:43:35.858+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:43:35.877+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.098 seconds
[2025-07-17T22:44:06.729+0000] {processor.py:186} INFO - Started process (PID=961) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:44:06.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:44:06.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.731+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:44:06.802+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.798+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:44:06.803+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:44:06.822+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.099 seconds
[2025-07-17T22:44:37.671+0000] {processor.py:186} INFO - Started process (PID=1092) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:44:37.672+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:44:37.674+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:44:37.743+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.734+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:44:37.744+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:44:37.760+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.094 seconds
[2025-07-17T22:45:08.377+0000] {processor.py:186} INFO - Started process (PID=1225) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:45:08.377+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:45:08.379+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.378+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:45:08.439+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.434+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:45:08.440+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:45:08.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.086 seconds
[2025-07-17T22:45:39.345+0000] {processor.py:186} INFO - Started process (PID=1354) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:45:39.347+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:45:39.350+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.349+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:45:39.447+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.442+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:45:39.448+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:45:39.468+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.131 seconds
[2025-07-17T22:46:10.361+0000] {processor.py:186} INFO - Started process (PID=1485) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:46:10.362+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:46:10.364+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.363+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:46:10.426+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.422+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:46:10.427+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:46:10.443+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.088 seconds
[2025-07-17T22:46:40.746+0000] {processor.py:186} INFO - Started process (PID=1624) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:46:40.747+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:46:40.748+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.748+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:46:40.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.800+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:46:40.806+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:46:40.824+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.083 seconds
[2025-07-17T22:47:11.617+0000] {processor.py:186} INFO - Started process (PID=1753) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:47:11.619+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:47:11.620+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.620+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:47:11.693+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.688+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:47:11.695+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:47:11.714+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.103 seconds
[2025-07-17T22:47:42.630+0000] {processor.py:186} INFO - Started process (PID=1890) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:47:42.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:47:42.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.632+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:47:42.694+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.690+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:47:42.695+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:47:42.711+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.087 seconds
[2025-07-17T22:48:13.621+0000] {processor.py:186} INFO - Started process (PID=2021) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:48:13.622+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:48:13.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.623+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:48:13.683+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.679+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:48:13.683+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:48:13.698+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.084 seconds
[2025-07-17T22:48:44.996+0000] {processor.py:186} INFO - Started process (PID=2152) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:48:44.997+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:48:44.998+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.998+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:48:45.075+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:45.070+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:48:45.075+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:48:45.094+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.105 seconds
[2025-07-17T22:49:15.670+0000] {processor.py:186} INFO - Started process (PID=2283) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:49:15.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:49:15.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.672+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:49:15.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.756+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:49:15.764+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:49:15.785+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.123 seconds
[2025-07-17T22:49:46.267+0000] {processor.py:186} INFO - Started process (PID=2416) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:49:46.268+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:49:46.270+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:46.269+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:49:46.325+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:46.319+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:49:46.326+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:49:46.341+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.080 seconds
[2025-07-17T22:50:16.852+0000] {processor.py:186} INFO - Started process (PID=2547) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:50:16.853+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:50:16.855+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.854+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:50:16.917+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.913+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:50:16.918+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:50:16.937+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.092 seconds
[2025-07-17T22:50:47.540+0000] {processor.py:186} INFO - Started process (PID=2676) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:50:47.541+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:50:47.543+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.542+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:50:47.608+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.604+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:50:47.609+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:50:47.625+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.095 seconds
[2025-07-17T22:51:18.077+0000] {processor.py:186} INFO - Started process (PID=2809) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:51:18.078+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:51:18.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:18.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:51:18.135+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:18.129+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:51:18.135+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:51:18.154+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.082 seconds
[2025-07-17T22:51:48.904+0000] {processor.py:186} INFO - Started process (PID=2938) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:51:48.905+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:51:48.907+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.906+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:51:48.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.961+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:51:48.966+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:51:48.981+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.083 seconds
[2025-07-17T22:52:19.596+0000] {processor.py:186} INFO - Started process (PID=3069) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:52:19.596+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-17T22:52:19.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.598+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:52:19.659+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.656+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-17T22:52:19.660+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-17T22:52:19.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.085 seconds
