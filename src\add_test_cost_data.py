#!/usr/bin/env python3
"""
Скрипт для добавления тестовых данных о затратах в Redis
Имитирует выполнение различных DAG с разными затратами
"""

import redis
import json
from datetime import datetime, timedelta
import random
import uuid

def connect_to_redis():
    """Подключение к Redis"""
    try:
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        r.ping()
        print("✅ Подключение к Redis успешно")
        return r
    except Exception as e:
        print(f"❌ Ошибка подключения к Redis: {e}")
        return None

def generate_test_data():
    """Генерация тестовых данных о затратах"""
    
    # Различные DAG с разными типичными затратами
    dag_configs = {
        'chatgpt_message_pipeline': {
            'min_cost': 0.002,
            'max_cost': 0.015,
            'operations': 15
        },
        'chatgpt_analyze_food_pipeline': {
            'min_cost': 0.005,
            'max_cost': 0.025,
            'operations': 8
        },
        'chatgpt_image_pipeline': {
            'min_cost': 0.010,
            'max_cost': 0.040,
            'operations': 5
        },
        'chatgpt_generate_challenges_pipeline': {
            'min_cost': 0.008,
            'max_cost': 0.030,
            'operations': 6
        },
        'chatgpt_message_recommendation_pipeline': {
            'min_cost': 0.003,
            'max_cost': 0.018,
            'operations': 12
        }
    }
    
    test_data = []
    
    # Генерируем данные за последние 7 дней
    for dag_id, config in dag_configs.items():
        for i in range(config['operations']):
            # Случайное время в последние 7 дней
            days_ago = random.randint(0, 7)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            
            timestamp = datetime.now() - timedelta(
                days=days_ago, 
                hours=hours_ago, 
                minutes=minutes_ago
            )
            
            # Случайная стоимость в диапазоне для данного DAG
            cost = round(random.uniform(config['min_cost'], config['max_cost']), 6)
            
            # Генерируем токены на основе стоимости
            # Примерные цены: input $0.0015/1K tokens, output $0.002/1K tokens
            total_tokens = int(cost / 0.0018 * 1000)  # Средняя цена
            input_tokens = int(total_tokens * random.uniform(0.3, 0.7))
            output_tokens = total_tokens - input_tokens
            
            cost_data = {
                'dag_id': dag_id,
                'task_id': f'analyze_message_task_{i+1}',
                'run_id': f'manual__{timestamp.strftime("%Y-%m-%dT%H:%M:%S")}+00:00',
                'timestamp': timestamp.isoformat(),
                'tokens_input': input_tokens,
                'tokens_output': output_tokens,
                'cost_input': round(input_tokens * 0.0015 / 1000, 6),
                'cost_output': round(output_tokens * 0.002 / 1000, 6),
                'total_cost': cost,
                'model': 'gpt-4o-mini'
            }
            
            test_data.append(cost_data)
    
    return test_data

def save_to_redis(r, test_data):
    """Сохранение тестовых данных в Redis"""
    
    saved_count = 0
    
    for data in test_data:
        try:
            # Создаем уникальный ключ для каждой операции
            key = f"cost_tracking:{data['dag_id']}:{data['run_id']}:{uuid.uuid4().hex[:8]}"
            
            # Сохраняем данные в Redis с TTL 30 дней
            r.setex(key, 30 * 24 * 60 * 60, json.dumps(data))
            saved_count += 1
            
        except Exception as e:
            print(f"❌ Ошибка сохранения данных: {e}")
    
    return saved_count

def main():
    """Основная функция"""
    print("🚀 ДОБАВЛЕНИЕ ТЕСТОВЫХ ДАННЫХ О ЗАТРАТАХ")
    print("=" * 50)
    
    # Подключаемся к Redis
    r = connect_to_redis()
    if not r:
        return
    
    # Генерируем тестовые данные
    print("📊 Генерация тестовых данных...")
    test_data = generate_test_data()
    print(f"✅ Сгенерировано {len(test_data)} записей")
    
    # Сохраняем в Redis
    print("💾 Сохранение в Redis...")
    saved_count = save_to_redis(r, test_data)
    print(f"✅ Сохранено {saved_count} записей")
    
    # Показываем статистику
    print("\n📈 СТАТИСТИКА ПО DAG:")
    dag_stats = {}
    total_cost = 0
    
    for data in test_data:
        dag_id = data['dag_id']
        if dag_id not in dag_stats:
            dag_stats[dag_id] = {'count': 0, 'cost': 0}
        
        dag_stats[dag_id]['count'] += 1
        dag_stats[dag_id]['cost'] += data['total_cost']
        total_cost += data['total_cost']
    
    for dag_id, stats in dag_stats.items():
        print(f"  {dag_id}: {stats['count']} операций, ${stats['cost']:.6f}")
    
    print(f"\n💰 ОБЩАЯ СТОИМОСТЬ: ${total_cost:.6f}")
    print(f"🔢 ВСЕГО ОПЕРАЦИЙ: {len(test_data)}")
    
    print("\n🎉 ГОТОВО!")
    print("Теперь проверьте API: GET http://localhost:9000/cost-stats")

if __name__ == "__main__":
    main()
