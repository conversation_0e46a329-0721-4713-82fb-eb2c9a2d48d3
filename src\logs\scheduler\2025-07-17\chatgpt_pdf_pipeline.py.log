[2025-07-17T22:21:49.724+0000] {processor.py:186} INFO - Started process (PID=3981) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:21:49.725+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:21:49.726+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:49.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:21:50.009+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:21:50.109+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:50.108+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:21:50.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:50.117+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T22:21:50.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.417 seconds
