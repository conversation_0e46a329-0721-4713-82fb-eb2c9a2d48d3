[2025-07-17T22:41:32.057+0000] {processor.py:186} INFO - Started process (PID=296) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:41:32.058+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:41:32.060+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.060+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:41:32.072+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.071+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:41:32.074+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:41:32.096+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-17T22:42:02.710+0000] {processor.py:186} INFO - Started process (PID=424) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:42:02.711+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:42:02.714+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.713+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:42:02.727+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.726+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:42:02.727+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:42:02.744+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.039 seconds
[2025-07-17T22:42:33.815+0000] {processor.py:186} INFO - Started process (PID=553) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:42:33.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:42:33.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:42:33.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.833+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:42:33.835+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:42:33.856+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-17T22:43:04.061+0000] {processor.py:186} INFO - Started process (PID=679) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:43:04.062+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:43:04.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.063+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:43:04.078+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.077+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:43:04.078+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:43:04.096+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.042 seconds
[2025-07-17T22:43:35.572+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:43:35.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:43:35.574+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.574+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:43:35.591+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.590+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:43:35.592+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:43:35.611+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-17T22:44:06.365+0000] {processor.py:186} INFO - Started process (PID=946) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:44:06.366+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:44:06.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:44:06.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.382+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:44:06.384+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:44:06.407+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.049 seconds
[2025-07-17T22:44:37.321+0000] {processor.py:186} INFO - Started process (PID=1077) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:44:37.322+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:44:37.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:44:37.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.335+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:44:37.337+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:44:37.357+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-17T22:45:07.762+0000] {processor.py:186} INFO - Started process (PID=1203) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:45:07.763+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:45:07.765+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:45:07.780+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.778+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:45:07.781+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:45:07.800+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-17T22:45:38.840+0000] {processor.py:186} INFO - Started process (PID=1334) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:45:38.841+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:45:38.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:38.842+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:45:38.858+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:38.857+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:45:38.859+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:45:38.877+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-17T22:46:10.121+0000] {processor.py:186} INFO - Started process (PID=1475) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:46:10.122+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:46:10.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.123+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:46:10.135+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.134+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:46:10.136+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:46:10.155+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-17T22:46:40.433+0000] {processor.py:186} INFO - Started process (PID=1609) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:46:40.434+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:46:40.435+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.435+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:46:40.445+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.444+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-17T22:46:40.446+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:46:40.462+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.035 seconds
