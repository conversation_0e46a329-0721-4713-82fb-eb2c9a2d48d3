[2025-07-17T21:34:30.412+0000] {processor.py:186} INFO - Started process (PID=251) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:34:30.413+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:34:30.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.415+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:34:30.862+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:34:30.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:30.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.977+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:34:30.996+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.589 seconds
[2025-07-17T21:35:02.075+0000] {processor.py:186} INFO - Started process (PID=387) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:35:02.076+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:35:02.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:35:02.421+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:35:02.534+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.534+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:02.548+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.548+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:35:02.570+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.501 seconds
[2025-07-17T21:35:32.635+0000] {processor.py:186} INFO - Started process (PID=525) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:35:32.636+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:35:32.639+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.638+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:35:32.834+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:35:32.979+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.979+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.993+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.993+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:35:33.009+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.380 seconds
[2025-07-17T21:36:03.089+0000] {processor.py:186} INFO - Started process (PID=661) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:36:03.090+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:36:03.093+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.092+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:36:03.257+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:36:03.386+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.386+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.398+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:36:03.422+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.339 seconds
[2025-07-17T21:36:34.132+0000] {processor.py:186} INFO - Started process (PID=797) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:36:34.133+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:36:34.135+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.135+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:36:34.289+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:36:34.404+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.404+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.415+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:36:34.433+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.306 seconds
[2025-07-17T21:37:04.625+0000] {processor.py:186} INFO - Started process (PID=933) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:37:04.626+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:37:04.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.628+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:37:04.796+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:37:04.922+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.922+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:04.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.936+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:37:04.962+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.344 seconds
[2025-07-17T21:37:35.208+0000] {processor.py:186} INFO - Started process (PID=1072) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:37:35.209+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:37:35.211+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.211+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:37:35.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:37:35.507+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.507+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:35.518+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.517+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:37:35.537+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.334 seconds
[2025-07-17T21:38:05.744+0000] {processor.py:186} INFO - Started process (PID=1210) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:38:05.745+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:38:05.747+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.747+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:38:05.955+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:38:06.087+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.101+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:38:06.136+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.398 seconds
[2025-07-17T21:38:36.300+0000] {processor.py:186} INFO - Started process (PID=1346) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:38:36.301+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:38:36.304+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.304+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:38:36.475+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:38:36.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.583+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:36.596+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.596+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:38:36.621+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.328 seconds
[2025-07-17T21:39:06.837+0000] {processor.py:186} INFO - Started process (PID=1477) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:39:06.839+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:39:06.841+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:06.841+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:39:07.023+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:39:07.147+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.146+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.158+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.158+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:39:07.182+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.351 seconds
[2025-07-17T21:39:37.308+0000] {processor.py:186} INFO - Started process (PID=1613) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:39:37.310+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:39:37.313+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.313+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:39:37.493+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:39:37.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.680+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:37.711+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.711+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:39:37.757+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.455 seconds
[2025-07-17T21:40:08.389+0000] {processor.py:186} INFO - Started process (PID=1749) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:40:08.390+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:40:08.392+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.392+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:40:08.590+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:40:08.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.739+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:08.758+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.757+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:40:08.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.402 seconds
[2025-07-17T21:40:39.439+0000] {processor.py:186} INFO - Started process (PID=1885) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:40:39.440+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:40:39.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.442+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:40:39.649+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:40:39.815+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.815+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:39.833+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.833+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:40:39.861+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.428 seconds
[2025-07-17T21:42:57.267+0000] {processor.py:186} INFO - Started process (PID=251) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:42:57.269+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:42:57.272+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.271+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:42:57.624+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:42:57.742+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.741+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:57.762+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.762+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:42:57.787+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.526 seconds
[2025-07-17T21:43:28.422+0000] {processor.py:186} INFO - Started process (PID=387) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:43:28.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:43:28.426+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:43:28.753+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:43:28.881+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.880+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:28.891+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.891+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:43:28.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.494 seconds
[2025-07-17T21:43:59.154+0000] {processor.py:186} INFO - Started process (PID=529) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:43:59.155+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:43:59.157+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.157+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:43:59.356+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:43:59.482+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.482+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.494+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:43:59.517+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.370 seconds
[2025-07-17T21:44:29.976+0000] {processor.py:186} INFO - Started process (PID=665) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:44:29.977+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:44:29.980+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.979+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:44:30.185+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:44:30.309+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.308+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:30.320+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.320+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:44:30.342+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.373 seconds
[2025-07-17T21:45:01.145+0000] {processor.py:186} INFO - Started process (PID=801) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:45:01.146+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:45:01.148+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.148+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:45:01.354+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:45:01.476+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.475+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:01.487+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.487+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:45:01.509+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.371 seconds
[2025-07-17T21:55:25.139+0000] {processor.py:186} INFO - Started process (PID=252) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:55:25.140+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:55:25.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.142+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:55:25.505+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:55:25.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.624+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:25.638+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.637+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:55:25.660+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.526 seconds
[2025-07-17T21:55:56.135+0000] {processor.py:186} INFO - Started process (PID=388) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:55:56.136+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:55:56.138+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.138+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:55:56.420+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:55:56.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.522+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:56.532+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.532+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:55:56.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.427 seconds
[2025-07-17T21:56:26.612+0000] {processor.py:186} INFO - Started process (PID=526) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:56:26.613+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:56:26.615+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.615+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:56:26.778+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:56:26.894+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.894+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:26.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.906+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:56:26.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.321 seconds
[2025-07-17T21:56:57.085+0000] {processor.py:186} INFO - Started process (PID=669) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:56:57.086+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:56:57.088+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.088+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:56:57.226+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:56:57.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.336+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:57.349+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.349+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:56:57.371+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.292 seconds
[2025-07-17T21:57:28.205+0000] {processor.py:186} INFO - Started process (PID=805) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:57:28.206+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:57:28.208+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.208+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:57:28.349+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:57:28.457+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.457+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:28.468+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.468+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:57:28.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.286 seconds
[2025-07-17T21:57:58.793+0000] {processor.py:186} INFO - Started process (PID=941) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:57:58.794+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:57:58.796+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:58.796+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:57:58.951+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:57:59.066+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.066+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:59.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.079+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:57:59.098+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.311 seconds
[2025-07-17T21:58:29.192+0000] {processor.py:186} INFO - Started process (PID=1077) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:58:29.193+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T21:58:29.195+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.195+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:58:29.350+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T21:58:29.460+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.459+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:29.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.471+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T21:58:29.490+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.304 seconds
[2025-07-17T22:00:36.688+0000] {processor.py:186} INFO - Started process (PID=251) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:00:36.689+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:00:36.691+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.691+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:00:37.000+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:00:37.112+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.112+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:37.126+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T22:00:37.156+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.473 seconds
[2025-07-17T22:01:07.673+0000] {processor.py:186} INFO - Started process (PID=387) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:01:07.674+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:01:07.678+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:01:08.058+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:01:08.201+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.200+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:08.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T22:01:08.234+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.568 seconds
[2025-07-17T22:01:38.927+0000] {processor.py:186} INFO - Started process (PID=525) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:01:38.928+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:01:38.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:38.930+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:01:39.090+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:01:39.208+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.208+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:39.219+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.219+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T22:01:39.238+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.317 seconds
[2025-07-17T22:02:09.428+0000] {processor.py:186} INFO - Started process (PID=661) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:02:09.429+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:02:09.431+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.431+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:02:09.585+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:02:09.686+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.686+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:09.696+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.696+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T22:02:09.714+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.292 seconds
[2025-07-17T22:02:39.815+0000] {processor.py:186} INFO - Started process (PID=797) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:02:39.815+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:02:39.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:39.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:02:39.977+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:02:40.078+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.078+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:40.088+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.088+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T22:02:40.104+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.294 seconds
[2025-07-17T22:03:10.401+0000] {processor.py:186} INFO - Started process (PID=938) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:03:10.402+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:03:10.405+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.405+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:03:10.562+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:03:10.670+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.670+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:10.682+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.682+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T22:03:10.696+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.302 seconds
[2025-07-17T22:03:40.980+0000] {processor.py:186} INFO - Started process (PID=1079) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:03:40.981+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:03:40.984+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:40.984+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:03:41.144+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:03:41.262+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:41.262+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:41.272+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:41.272+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T22:03:41.291+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.317 seconds
[2025-07-17T22:04:11.464+0000] {processor.py:186} INFO - Started process (PID=1205) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:04:11.465+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:04:11.467+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.467+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:04:11.617+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:04:11.727+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.727+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:11.738+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.737+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T22:04:11.758+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.299 seconds
[2025-07-17T22:04:42.055+0000] {processor.py:186} INFO - Started process (PID=1341) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:04:42.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-17T22:04:42.058+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.058+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:04:42.219+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_pdf_pipeline' retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-17T22:04:42.324+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.324+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:42.336+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.336+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_pdf_pipeline to None, run_after=None
[2025-07-17T22:04:42.355+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.306 seconds
