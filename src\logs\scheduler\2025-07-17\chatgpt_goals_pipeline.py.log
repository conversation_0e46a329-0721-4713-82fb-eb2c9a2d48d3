[2025-07-17T22:41:29.610+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:41:29.611+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:41:29.614+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.614+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:41:29.695+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.695+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:29.703+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:41:29.964+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.964+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.972+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.972+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:41:29.991+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.387 seconds
[2025-07-17T22:42:00.642+0000] {processor.py:186} INFO - Started process (PID=374) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:00.643+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:42:00.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.645+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:00.714+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.713+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:00.722+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:00.945+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.945+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:00.954+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.954+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:42:00.975+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.341 seconds
[2025-07-17T22:42:31.365+0000] {processor.py:186} INFO - Started process (PID=505) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:31.366+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:42:31.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:31.551+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.550+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:31.559+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:31.648+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.648+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:31.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.656+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:42:31.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.315 seconds
[2025-07-17T22:43:01.930+0000] {processor.py:186} INFO - Started process (PID=636) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:43:01.931+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:43:01.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:43:02.003+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.003+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:02.011+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:43:02.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:02.123+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:02.122+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:43:02.145+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.220 seconds
[2025-07-17T22:43:32.359+0000] {processor.py:186} INFO - Started process (PID=767) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:43:32.360+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:43:32.362+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.361+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:43:32.441+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.441+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:32.453+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:43:32.547+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.547+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:32.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:32.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:43:32.575+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.222 seconds
[2025-07-17T22:44:03.186+0000] {processor.py:186} INFO - Started process (PID=898) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:44:03.187+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:44:03.188+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.188+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:44:03.252+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.252+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:03.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:44:03.361+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.361+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:03.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:03.374+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:44:03.397+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.217 seconds
[2025-07-17T22:44:34.174+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:44:34.175+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:44:34.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.176+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:44:34.247+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.247+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:34.255+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:44:34.357+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.357+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:34.371+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:34.371+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:44:34.388+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.220 seconds
[2025-07-17T22:45:05.073+0000] {processor.py:186} INFO - Started process (PID=1158) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:45:05.074+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:45:05.075+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.075+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:45:05.147+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.147+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:05.157+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:45:05.249+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.249+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:05.262+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:05.262+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:45:05.283+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.216 seconds
[2025-07-17T22:45:35.887+0000] {processor.py:186} INFO - Started process (PID=1291) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:45:35.888+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:45:35.890+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.889+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:45:35.958+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.958+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:35.967+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:45:36.062+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:36.075+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:36.074+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:45:36.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.210 seconds
[2025-07-17T22:46:06.713+0000] {processor.py:186} INFO - Started process (PID=1422) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:46:06.714+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:46:06.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.715+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:46:06.789+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.789+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:06.798+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:46:06.896+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.896+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:06.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.906+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:46:06.924+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.217 seconds
[2025-07-17T22:46:36.992+0000] {processor.py:186} INFO - Started process (PID=1551) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:46:36.993+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:46:36.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.994+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:46:37.061+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.061+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:37.072+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:46:37.187+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.187+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:37.198+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:37.197+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:46:37.216+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.230 seconds
[2025-07-17T22:47:07.991+0000] {processor.py:186} INFO - Started process (PID=1690) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:47:07.992+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:47:07.993+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.993+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:47:08.067+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.066+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:08.074+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:47:08.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.173+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:08.184+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:08.184+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:47:08.202+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.217 seconds
[2025-07-17T22:47:39.054+0000] {processor.py:186} INFO - Started process (PID=1825) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:47:39.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:47:39.058+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.057+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:47:39.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.141+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:39.154+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:47:39.272+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.272+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:39.284+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:39.284+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:47:39.307+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.261 seconds
[2025-07-17T22:48:10.008+0000] {processor.py:186} INFO - Started process (PID=1958) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:48:10.009+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:48:10.010+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.010+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:48:10.084+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.084+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:10.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:48:10.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:10.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:10.204+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:48:10.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.221 seconds
[2025-07-17T22:48:40.853+0000] {processor.py:186} INFO - Started process (PID=2087) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:48:40.854+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:48:40.856+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.856+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:48:40.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.938+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:40.948+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:48:41.054+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.054+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:41.068+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:41.068+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:48:41.087+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.240 seconds
[2025-07-17T22:49:11.767+0000] {processor.py:186} INFO - Started process (PID=2220) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:49:11.769+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:49:11.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.770+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:49:11.866+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.866+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:11.875+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:49:11.991+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.990+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:12.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:12.006+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:49:12.028+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.268 seconds
[2025-07-17T22:49:42.626+0000] {processor.py:186} INFO - Started process (PID=2351) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:49:42.627+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:49:42.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.628+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:49:42.698+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.698+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:42.707+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:49:42.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.792+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:42.802+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.802+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:49:42.819+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.199 seconds
[2025-07-17T22:50:13.373+0000] {processor.py:186} INFO - Started process (PID=2482) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:50:13.374+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:50:13.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:13.375+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:50:13.447+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:13.447+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:13.457+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:50:13.560+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:13.560+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:13.570+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:13.570+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:50:13.592+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.225 seconds
[2025-07-17T22:50:44.062+0000] {processor.py:186} INFO - Started process (PID=2613) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:50:44.063+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:50:44.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:44.064+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:50:44.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:44.142+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:44.151+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:50:44.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:44.251+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:44.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:44.264+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:50:44.284+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.231 seconds
[2025-07-17T22:51:14.651+0000] {processor.py:186} INFO - Started process (PID=2742) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:51:14.652+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:51:14.654+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.654+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:51:14.736+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.736+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:14.744+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:51:14.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.845+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:14.856+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.856+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:51:14.874+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.228 seconds
[2025-07-17T22:51:45.282+0000] {processor.py:186} INFO - Started process (PID=2875) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:51:45.283+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:51:45.284+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:45.284+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:51:45.355+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:45.355+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:45.363+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:51:45.453+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:45.453+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:45.464+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:45.464+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:51:45.482+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.206 seconds
[2025-07-17T22:52:15.978+0000] {processor.py:186} INFO - Started process (PID=3004) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:52:15.979+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:52:15.980+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.980+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:52:16.048+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:16.048+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:16.057+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:52:16.162+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:16.162+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:16.175+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:16.175+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:52:16.195+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.223 seconds
[2025-07-17T22:52:46.678+0000] {processor.py:186} INFO - Started process (PID=3137) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:52:46.679+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:52:46.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:46.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:52:46.748+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:46.747+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:46.756+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:52:46.851+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:46.851+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:46.861+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:46.861+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:52:46.879+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.207 seconds
[2025-07-17T22:53:17.304+0000] {processor.py:186} INFO - Started process (PID=3274) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:53:17.305+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:53:17.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:17.306+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:53:17.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:17.374+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:17.381+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:53:17.479+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:17.479+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:17.489+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:17.489+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:53:17.508+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.211 seconds
[2025-07-17T22:53:48.208+0000] {processor.py:186} INFO - Started process (PID=3405) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:53:48.209+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:53:48.210+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:48.210+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:53:48.283+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:48.283+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:48.293+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:53:48.394+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:48.394+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:48.405+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:48.405+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:53:48.426+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.224 seconds
[2025-07-17T22:54:19.136+0000] {processor.py:186} INFO - Started process (PID=3542) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:54:19.137+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:54:19.138+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:19.138+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:54:19.208+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:19.208+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:54:19.217+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:54:19.314+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:19.313+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:54:19.325+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:19.325+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:54:19.344+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.213 seconds
