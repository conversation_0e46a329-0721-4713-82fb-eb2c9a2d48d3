[2025-07-17T21:34:31.330+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:34:31.332+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:34:31.334+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.334+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:34:31.416+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.416+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:31.423+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:34:31.667+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.667+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.677+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.677+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:34:31.701+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.377 seconds
[2025-07-17T21:35:02.632+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:02.633+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:35:02.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:02.881+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.881+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:02.888+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:02.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.994+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.007+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:35:03.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.402 seconds
[2025-07-17T21:35:33.374+0000] {processor.py:186} INFO - Started process (PID=540) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:33.375+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:35:33.377+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.376+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:33.472+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.472+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:33.480+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:33.594+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:33.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.603+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:35:33.624+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.258 seconds
[2025-07-17T21:36:03.711+0000] {processor.py:186} INFO - Started process (PID=676) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:03.713+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:36:03.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.717+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:03.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.818+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:03.827+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:03.925+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.925+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.937+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:36:03.960+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.257 seconds
[2025-07-17T21:36:34.470+0000] {processor.py:186} INFO - Started process (PID=810) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:34.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:36:34.473+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.473+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:34.542+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.542+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.550+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:34.647+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.646+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:36:34.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.210 seconds
[2025-07-17T21:37:05.009+0000] {processor.py:186} INFO - Started process (PID=946) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:05.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:37:05.013+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:05.094+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.093+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:05.103+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:05.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.209+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:05.219+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.219+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:37:05.238+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.236 seconds
[2025-07-17T21:37:35.537+0000] {processor.py:186} INFO - Started process (PID=1082) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:35.538+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:37:35.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.541+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:35.616+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.616+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.623+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:35.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.717+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:35.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.728+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:37:35.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.216 seconds
[2025-07-17T21:38:06.099+0000] {processor.py:186} INFO - Started process (PID=1218) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:06.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:38:06.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:06.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.204+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:06.215+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:06.333+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.333+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.344+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.343+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:38:06.361+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.270 seconds
[2025-07-17T21:38:36.988+0000] {processor.py:186} INFO - Started process (PID=1356) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:36.989+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:38:36.992+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.992+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:37.069+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.069+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:37.079+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:37.192+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.191+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:37.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.204+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:38:37.227+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.246 seconds
[2025-07-17T21:39:07.422+0000] {processor.py:186} INFO - Started process (PID=1492) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:07.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:39:07.425+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.425+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:07.504+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.503+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:07.513+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:07.616+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.616+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.626+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:39:07.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.231 seconds
[2025-07-17T21:39:37.842+0000] {processor.py:186} INFO - Started process (PID=1626) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:37.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:39:37.848+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:37.979+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.979+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:37.989+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:38.105+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.105+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:38.118+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.118+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:39:38.138+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.307 seconds
[2025-07-17T21:40:08.836+0000] {processor.py:186} INFO - Started process (PID=1762) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:40:08.838+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:40:08.842+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.841+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:40:08.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.922+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:08.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:40:09.047+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.046+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:09.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.057+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:40:09.077+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.249 seconds
[2025-07-17T21:40:39.909+0000] {processor.py:186} INFO - Started process (PID=1898) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:40:39.910+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:40:39.913+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.913+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:40:40.000+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.000+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:40.008+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:40:40.120+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.120+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:40.133+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:40.133+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:40:40.155+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.254 seconds
[2025-07-17T21:42:58.156+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:42:58.157+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:42:58.161+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.161+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:42:58.253+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.252+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:58.261+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:42:58.508+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.508+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:58.518+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.517+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:42:58.542+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.392 seconds
[2025-07-17T21:43:29.077+0000] {processor.py:186} INFO - Started process (PID=408) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:43:29.078+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:43:29.080+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.080+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:43:29.314+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.314+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:29.321+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:43:29.425+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.425+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:29.436+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.436+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:43:29.457+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.387 seconds
[2025-07-17T21:43:59.709+0000] {processor.py:186} INFO - Started process (PID=544) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:43:59.710+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:43:59.713+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.712+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:43:59.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.792+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:59.801+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:43:59.921+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.921+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.933+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:43:59.955+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.252 seconds
[2025-07-17T21:44:30.589+0000] {processor.py:186} INFO - Started process (PID=680) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:44:30.590+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:44:30.592+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.592+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:44:30.678+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.678+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.688+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:44:30.798+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.798+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:30.808+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.808+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:44:30.829+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.249 seconds
[2025-07-17T21:45:01.723+0000] {processor.py:186} INFO - Started process (PID=816) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:45:01.724+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:45:01.727+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:45:01.811+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.811+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:01.819+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:45:01.924+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.924+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:01.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.937+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:45:01.957+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.240 seconds
[2025-07-17T21:55:26.005+0000] {processor.py:186} INFO - Started process (PID=267) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:55:26.006+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:55:26.009+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.008+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:55:26.076+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.076+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:26.084+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:55:26.297+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.297+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:26.308+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:26.308+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:55:26.328+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.331 seconds
[2025-07-17T21:55:56.679+0000] {processor.py:186} INFO - Started process (PID=409) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:55:56.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:55:56.683+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.683+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:55:56.892+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.892+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:56.899+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:55:56.986+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.986+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:56.997+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.997+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:55:57.013+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.339 seconds
[2025-07-17T21:56:27.267+0000] {processor.py:186} INFO - Started process (PID=546) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:56:27.268+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:56:27.272+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.271+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:56:27.361+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.360+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:27.370+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:56:27.477+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.477+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:27.488+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.488+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:56:27.510+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.250 seconds
[2025-07-17T21:56:57.678+0000] {processor.py:186} INFO - Started process (PID=682) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:56:57.679+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:56:57.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.681+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:56:57.746+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.745+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:57.755+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:56:57.865+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.865+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:57.874+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.874+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:56:57.893+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.221 seconds
