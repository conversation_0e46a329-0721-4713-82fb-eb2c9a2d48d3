[2025-07-17T21:34:31.330+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:34:31.332+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:34:31.334+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.334+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:34:31.416+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.416+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:31.423+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:34:31.667+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.667+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.677+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.677+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:34:31.701+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.377 seconds
[2025-07-17T21:35:02.632+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:02.633+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:35:02.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:02.881+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.881+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:02.888+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:02.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.994+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.007+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:35:03.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.402 seconds
[2025-07-17T21:35:33.374+0000] {processor.py:186} INFO - Started process (PID=540) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:33.375+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:35:33.377+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.376+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:33.472+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.472+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:33.480+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:33.594+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:33.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.603+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:35:33.624+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.258 seconds
[2025-07-17T21:36:03.711+0000] {processor.py:186} INFO - Started process (PID=676) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:03.713+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:36:03.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.717+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:03.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.818+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:03.827+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:03.925+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.925+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.937+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:36:03.960+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.257 seconds
[2025-07-17T21:36:34.470+0000] {processor.py:186} INFO - Started process (PID=810) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:34.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:36:34.473+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.473+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:34.542+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.542+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.550+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:34.647+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.646+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:36:34.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.210 seconds
[2025-07-17T21:37:05.009+0000] {processor.py:186} INFO - Started process (PID=946) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:05.011+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:37:05.013+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.013+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:05.094+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.093+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:05.103+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:05.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.209+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:05.219+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:05.219+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:37:05.238+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.236 seconds
[2025-07-17T21:37:35.537+0000] {processor.py:186} INFO - Started process (PID=1082) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:35.538+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:37:35.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.541+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:35.616+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.616+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.623+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:37:35.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.717+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:35.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.728+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:37:35.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.216 seconds
[2025-07-17T21:38:06.099+0000] {processor.py:186} INFO - Started process (PID=1218) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:06.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:38:06.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:06.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.204+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:06.215+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:06.333+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.333+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:06.344+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:06.343+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:38:06.361+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.270 seconds
[2025-07-17T21:38:36.988+0000] {processor.py:186} INFO - Started process (PID=1356) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:36.989+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:38:36.992+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.992+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:37.069+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.069+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:37.079+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:38:37.192+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.191+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:37.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:37.204+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:38:37.227+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.246 seconds
[2025-07-17T21:39:07.422+0000] {processor.py:186} INFO - Started process (PID=1492) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:07.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:39:07.425+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.425+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:07.504+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.503+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:07.513+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:07.616+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.616+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.626+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.626+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:39:07.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.231 seconds
[2025-07-17T21:39:37.842+0000] {processor.py:186} INFO - Started process (PID=1626) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:37.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:39:37.848+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:37.979+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.979+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:37.989+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:39:38.105+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.105+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:38.118+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:38.118+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:39:38.138+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.307 seconds
[2025-07-17T21:40:08.836+0000] {processor.py:186} INFO - Started process (PID=1762) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:40:08.838+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:40:08.842+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.841+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:40:08.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.922+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:08.930+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:40:09.047+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.046+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:09.057+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:09.057+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:40:09.077+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.249 seconds
