[2025-07-17T22:41:29.610+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:41:29.611+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:41:29.614+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.614+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:41:29.695+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.695+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:29.703+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:41:29.964+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.964+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.972+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.972+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:41:29.991+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.387 seconds
[2025-07-17T22:42:00.642+0000] {processor.py:186} INFO - Started process (PID=374) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:00.643+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:42:00.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.645+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:00.714+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.713+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:00.722+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:00.945+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.945+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:00.954+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.954+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:42:00.975+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.341 seconds
[2025-07-17T22:42:31.365+0000] {processor.py:186} INFO - Started process (PID=505) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:31.366+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T22:42:31.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:31.551+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.550+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:31.559+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T22:42:31.648+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.648+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:31.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:31.656+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T22:42:31.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.315 seconds
