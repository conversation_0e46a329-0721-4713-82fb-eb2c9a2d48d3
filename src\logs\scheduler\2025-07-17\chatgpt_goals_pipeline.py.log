[2025-07-17T21:34:31.330+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:34:31.332+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:34:31.334+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.334+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:34:31.416+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.416+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:31.423+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:34:31.667+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.667+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.677+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.677+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:34:31.701+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.377 seconds
[2025-07-17T21:35:02.632+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:02.633+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:35:02.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:02.881+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.881+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:02.888+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:02.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.994+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:03.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:03.007+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:35:03.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.402 seconds
[2025-07-17T21:35:33.374+0000] {processor.py:186} INFO - Started process (PID=540) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:33.375+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:35:33.377+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.376+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:33.472+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.472+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:33.480+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:35:33.594+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:33.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:33.603+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:35:33.624+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.258 seconds
[2025-07-17T21:36:03.711+0000] {processor.py:186} INFO - Started process (PID=676) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:03.713+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:36:03.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.717+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:03.818+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.818+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:03.827+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:03.925+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.925+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.937+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:36:03.960+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.257 seconds
[2025-07-17T21:36:34.470+0000] {processor.py:186} INFO - Started process (PID=810) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:34.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-17T21:36:34.473+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.473+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:34.542+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.542+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.550+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-17T21:36:34.647+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.646+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-17T21:36:34.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.210 seconds
