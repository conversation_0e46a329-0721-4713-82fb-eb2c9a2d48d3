[2025-07-17T22:41:28.677+0000] {processor.py:186} INFO - Started process (PID=218) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:28.678+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:41:28.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.680+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:28.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.763+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:28.773+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:28.876+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.876+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.037+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:41:29.064+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.393 seconds
[2025-07-17T22:41:59.420+0000] {processor.py:186} INFO - Started process (PID=347) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:59.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:41:59.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:59.495+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.495+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.502+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:59.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.740+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:59.749+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.749+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:41:59.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.353 seconds
[2025-07-17T22:42:30.257+0000] {processor.py:186} INFO - Started process (PID=480) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:42:30.258+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:42:30.260+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.259+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:42:30.458+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.458+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.465+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:42:30.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.549+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:42:30.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.325 seconds
[2025-07-17T22:43:00.874+0000] {processor.py:186} INFO - Started process (PID=611) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:00.875+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:43:00.877+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.877+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:00.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.952+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.960+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:01.051+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.051+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:01.063+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.062+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:43:01.082+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.214 seconds
[2025-07-17T22:43:31.289+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:31.290+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:43:31.291+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.291+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:31.365+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.364+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:31.372+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:31.479+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.479+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:31.491+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.490+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:43:31.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.228 seconds
[2025-07-17T22:44:02.413+0000] {processor.py:186} INFO - Started process (PID=873) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:02.414+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:44:02.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.415+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:02.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.486+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:02.495+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:02.594+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.603+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:44:02.623+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.216 seconds
[2025-07-17T22:44:33.372+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:33.373+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:44:33.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:33.445+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.445+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:33.452+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:33.554+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.554+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.565+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.565+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:44:33.586+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.220 seconds
[2025-07-17T22:45:04.293+0000] {processor.py:186} INFO - Started process (PID=1135) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:04.294+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:45:04.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:04.373+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.372+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:04.381+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:04.481+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.481+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:04.492+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.491+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:45:04.515+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.228 seconds
[2025-07-17T22:45:35.049+0000] {processor.py:186} INFO - Started process (PID=1266) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:35.050+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:45:35.052+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.051+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:35.171+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.171+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:35.177+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:35.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.278+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:35.290+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.290+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:45:35.309+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.266 seconds
[2025-07-17T22:46:05.903+0000] {processor.py:186} INFO - Started process (PID=1397) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:05.904+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:46:05.905+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.905+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:05.982+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.982+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:05.990+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:06.103+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.103+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:06.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.115+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:46:06.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.238 seconds
[2025-07-17T22:46:36.221+0000] {processor.py:186} INFO - Started process (PID=1528) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:36.222+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:46:36.224+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.223+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:36.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.295+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:36.302+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:36.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.398+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:36.411+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.410+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:46:36.429+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.214 seconds
