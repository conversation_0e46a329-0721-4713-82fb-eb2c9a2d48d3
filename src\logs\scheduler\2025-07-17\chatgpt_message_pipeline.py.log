[2025-07-17T21:34:29.341+0000] {processor.py:186} INFO - Started process (PID=216) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:34:29.342+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:34:29.346+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.345+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:34:29.434+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.434+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:29.442+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:34:29.543+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.543+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:29.689+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.689+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:34:29.708+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.375 seconds
[2025-07-17T21:35:00.683+0000] {processor.py:186} INFO - Started process (PID=359) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:35:00.685+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:35:00.688+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.688+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:35:00.775+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.775+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:00.781+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:35:01.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.037+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:01.049+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.049+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:35:01.075+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.398 seconds
[2025-07-17T21:35:31.465+0000] {processor.py:186} INFO - Started process (PID=495) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:35:31.466+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:35:31.469+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.468+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:35:31.552+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.551+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:31.561+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:35:31.674+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.674+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:31.687+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.686+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:35:31.707+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.247 seconds
[2025-07-17T21:36:01.802+0000] {processor.py:186} INFO - Started process (PID=631) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:36:01.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:36:01.807+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.806+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:36:01.886+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.885+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.892+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:36:01.989+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.989+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:01.999+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.999+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:36:02.019+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.224 seconds
[2025-07-17T21:36:32.078+0000] {processor.py:186} INFO - Started process (PID=767) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:36:32.080+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:36:32.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.082+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:36:32.151+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.151+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:32.160+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:36:32.250+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.250+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:32.260+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.259+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:36:32.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.206 seconds
[2025-07-17T21:37:02.503+0000] {processor.py:186} INFO - Started process (PID=903) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:37:02.504+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:37:02.507+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.506+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:37:02.575+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.575+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:02.583+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:37:02.679+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.678+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:02.688+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.688+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:37:02.709+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.211 seconds
[2025-07-17T21:37:33.148+0000] {processor.py:186} INFO - Started process (PID=1039) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:37:33.149+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:37:33.152+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.152+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:37:33.225+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.225+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:33.234+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:37:33.333+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.333+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:33.343+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.343+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:37:33.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.222 seconds
[2025-07-17T21:38:03.650+0000] {processor.py:186} INFO - Started process (PID=1175) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:38:03.651+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:38:03.653+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.653+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:38:03.739+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.739+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:03.749+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:38:03.875+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.874+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:03.885+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.885+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:38:03.906+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.264 seconds
[2025-07-17T21:38:34.182+0000] {processor.py:186} INFO - Started process (PID=1311) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:38:34.183+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:38:34.186+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.185+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:38:34.270+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.270+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:34.279+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:38:34.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:34.401+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.401+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:38:34.423+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.247 seconds
[2025-07-17T21:39:04.968+0000] {processor.py:186} INFO - Started process (PID=1442) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:39:04.969+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:39:04.971+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.971+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:39:05.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.045+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:05.053+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:39:05.161+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.160+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:05.172+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.172+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:39:05.195+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.233 seconds
[2025-07-17T21:39:35.541+0000] {processor.py:186} INFO - Started process (PID=1578) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:39:35.542+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:39:35.544+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.543+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:39:35.613+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.612+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.619+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:39:35.710+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.710+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:35.720+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:39:35.738+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.203 seconds
[2025-07-17T21:40:06.453+0000] {processor.py:186} INFO - Started process (PID=1719) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:40:06.454+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:40:06.458+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.458+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:40:06.538+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.538+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:06.547+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:40:06.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:06.673+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.673+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:40:06.696+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.250 seconds
[2025-07-17T21:40:37.237+0000] {processor.py:186} INFO - Started process (PID=1855) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:40:37.238+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:40:37.242+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.241+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:40:37.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.335+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:37.343+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:40:37.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.454+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:37.465+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.465+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:40:37.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.255 seconds
[2025-07-17T21:42:56.142+0000] {processor.py:186} INFO - Started process (PID=216) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:42:56.143+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:42:56.147+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.146+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:42:56.226+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.226+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:56.235+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:42:56.352+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.352+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:56.534+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.534+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:42:56.559+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.423 seconds
[2025-07-17T21:43:27.129+0000] {processor.py:186} INFO - Started process (PID=352) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:43:27.131+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:43:27.133+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:43:27.211+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.211+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:27.217+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:43:27.485+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.484+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:27.495+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.495+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:43:27.515+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.394 seconds
[2025-07-17T21:43:58.215+0000] {processor.py:186} INFO - Started process (PID=490) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:43:58.216+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:43:58.218+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.218+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:43:58.299+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.299+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:58.306+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:43:58.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.416+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:58.427+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.427+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:43:58.452+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.244 seconds
[2025-07-17T21:44:28.738+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:44:28.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:44:28.742+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:44:28.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.819+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:28.828+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:44:28.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.937+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:28.948+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.947+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:44:28.968+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.235 seconds
[2025-07-17T21:44:59.424+0000] {processor.py:186} INFO - Started process (PID=768) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:44:59.425+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:44:59.427+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:44:59.519+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.519+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:59.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:44:59.661+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.661+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:59.672+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.672+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:44:59.696+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.278 seconds
[2025-07-17T21:55:24.057+0000] {processor.py:186} INFO - Started process (PID=217) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:55:24.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:55:24.062+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:55:24.161+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.161+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:24.169+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:55:24.281+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.281+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:24.419+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.418+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:55:24.439+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.388 seconds
[2025-07-17T21:55:54.983+0000] {processor.py:186} INFO - Started process (PID=355) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:55:54.984+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:55:54.986+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.986+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:55:55.055+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.055+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:55.064+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:55:55.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.303+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:55.314+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.314+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:55:55.335+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.358 seconds
[2025-07-17T21:56:25.516+0000] {processor.py:186} INFO - Started process (PID=496) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:56:25.517+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:56:25.520+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.520+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:56:25.599+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.599+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:25.607+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:56:25.714+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.714+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:25.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.728+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:56:25.755+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.247 seconds
[2025-07-17T21:56:55.817+0000] {processor.py:186} INFO - Started process (PID=633) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:56:55.818+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:56:55.821+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.821+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:56:55.903+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.902+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:55.914+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:56:56.022+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.022+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:56.033+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.033+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:56:56.053+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.243 seconds
[2025-07-17T21:57:26.241+0000] {processor.py:186} INFO - Started process (PID=769) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:57:26.242+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:57:26.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.245+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:57:26.324+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.324+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:26.331+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:57:26.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:26.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.423+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:57:26.442+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.207 seconds
[2025-07-17T21:57:56.669+0000] {processor.py:186} INFO - Started process (PID=905) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:57:56.670+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:57:56.672+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.672+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:57:56.742+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.742+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:56.749+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:57:56.847+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.846+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:56.858+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.858+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:57:56.875+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.212 seconds
[2025-07-17T21:58:27.145+0000] {processor.py:186} INFO - Started process (PID=1047) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:58:27.146+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T21:58:27.148+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.147+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:58:27.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.220+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:27.228+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T21:58:27.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.318+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:27.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T21:58:27.347+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.208 seconds
[2025-07-17T22:00:35.604+0000] {processor.py:186} INFO - Started process (PID=216) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:00:35.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:00:35.608+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.608+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:00:35.705+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.705+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:35.713+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:00:35.878+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.878+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:36.008+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.008+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:00:36.025+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.428 seconds
[2025-07-17T22:01:06.309+0000] {processor.py:186} INFO - Started process (PID=357) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:01:06.310+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:01:06.313+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.313+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:01:06.414+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.414+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:06.424+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:01:06.718+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.717+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:06.732+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.731+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:01:06.755+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.453 seconds
[2025-07-17T22:01:36.926+0000] {processor.py:186} INFO - Started process (PID=495) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:01:36.927+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:01:36.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:36.929+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:01:36.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:36.994+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:37.001+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:01:37.088+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.088+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:37.098+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.098+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:01:37.115+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.194 seconds
[2025-07-17T22:02:07.451+0000] {processor.py:186} INFO - Started process (PID=631) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:02:07.452+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:02:07.455+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.455+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:02:07.519+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.519+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:07.527+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:02:07.619+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.619+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:07.630+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.629+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:02:07.646+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.200 seconds
[2025-07-17T22:02:37.866+0000] {processor.py:186} INFO - Started process (PID=767) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:02:37.867+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:02:37.869+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.869+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:02:37.936+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.936+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:37.945+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:02:38.034+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.034+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:38.043+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.043+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:02:38.062+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.201 seconds
[2025-07-17T22:03:08.193+0000] {processor.py:186} INFO - Started process (PID=903) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:03:08.194+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:03:08.196+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.196+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:03:08.261+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.261+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:08.267+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:03:08.363+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.362+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:08.373+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.373+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:03:08.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.204 seconds
[2025-07-17T22:03:38.686+0000] {processor.py:186} INFO - Started process (PID=1039) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:03:38.687+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:03:38.690+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.689+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:03:38.774+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.773+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:38.784+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:03:38.891+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.891+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:38.902+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.902+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:03:38.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.240 seconds
[2025-07-17T22:04:09.682+0000] {processor.py:186} INFO - Started process (PID=1170) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:04:09.684+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:04:09.689+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.688+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:04:09.777+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.777+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:09.785+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:04:09.881+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.881+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:09.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.893+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:04:09.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.242 seconds
[2025-07-17T22:04:40.097+0000] {processor.py:186} INFO - Started process (PID=1311) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:04:40.098+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:04:40.100+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.099+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:04:40.164+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.164+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:40.172+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:04:40.270+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.270+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:40.280+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.280+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:04:40.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.207 seconds
