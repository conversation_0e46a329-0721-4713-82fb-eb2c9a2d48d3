[2025-07-17T22:41:28.677+0000] {processor.py:186} INFO - Started process (PID=218) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:28.678+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:41:28.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.680+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:28.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.763+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:28.773+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:28.876+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.876+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.037+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:41:29.064+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.393 seconds
[2025-07-17T22:41:59.420+0000] {processor.py:186} INFO - Started process (PID=347) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:59.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:41:59.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:59.495+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.495+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.502+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:59.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.740+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:59.749+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.749+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:41:59.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.353 seconds
[2025-07-17T22:42:30.257+0000] {processor.py:186} INFO - Started process (PID=480) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:42:30.258+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:42:30.260+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.259+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:42:30.458+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.458+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.465+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:42:30.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.549+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:42:30.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.325 seconds
