[2025-07-17T22:41:28.677+0000] {processor.py:186} INFO - Started process (PID=218) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:28.678+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:41:28.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.680+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:28.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.763+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:28.773+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:28.876+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:28.876+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.037+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:41:29.064+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.393 seconds
[2025-07-17T22:41:59.420+0000] {processor.py:186} INFO - Started process (PID=347) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:59.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:41:59.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:59.495+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.495+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.502+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:41:59.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.740+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:59.749+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.749+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:41:59.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.353 seconds
[2025-07-17T22:42:30.257+0000] {processor.py:186} INFO - Started process (PID=480) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:42:30.258+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:42:30.260+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.259+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:42:30.458+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.458+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.465+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:42:30.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.549+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:42:30.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.325 seconds
[2025-07-17T22:43:00.874+0000] {processor.py:186} INFO - Started process (PID=611) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:00.875+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:43:00.877+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.877+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:00.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:00.952+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:00.960+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:01.051+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.051+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:01.063+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.062+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:43:01.082+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.214 seconds
[2025-07-17T22:43:31.289+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:31.290+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:43:31.291+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.291+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:31.365+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.364+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:31.372+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:43:31.479+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.479+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:31.491+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.490+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:43:31.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.228 seconds
[2025-07-17T22:44:02.413+0000] {processor.py:186} INFO - Started process (PID=873) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:02.414+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:44:02.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.415+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:02.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.486+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:02.495+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:02.594+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.603+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:44:02.623+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.216 seconds
[2025-07-17T22:44:33.372+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:33.373+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:44:33.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.374+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:33.445+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.445+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:33.452+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:44:33.554+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.554+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.565+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.565+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:44:33.586+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.220 seconds
[2025-07-17T22:45:04.293+0000] {processor.py:186} INFO - Started process (PID=1135) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:04.294+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:45:04.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:04.373+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.372+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:04.381+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:04.481+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.481+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:04.492+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.491+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:45:04.515+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.228 seconds
[2025-07-17T22:45:35.049+0000] {processor.py:186} INFO - Started process (PID=1266) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:35.050+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:45:35.052+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.051+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:35.171+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.171+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:35.177+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:45:35.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.278+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:35.290+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.290+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:45:35.309+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.266 seconds
[2025-07-17T22:46:05.903+0000] {processor.py:186} INFO - Started process (PID=1397) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:05.904+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:46:05.905+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.905+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:05.982+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:05.982+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:05.990+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:06.103+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.103+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:06.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.115+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:46:06.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.238 seconds
[2025-07-17T22:46:36.221+0000] {processor.py:186} INFO - Started process (PID=1528) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:36.222+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:46:36.224+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.223+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:36.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.295+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:36.302+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:46:36.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.398+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:36.411+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.410+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:46:36.429+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.214 seconds
[2025-07-17T22:47:07.288+0000] {processor.py:186} INFO - Started process (PID=1665) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:47:07.289+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:47:07.290+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.290+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:47:07.355+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.355+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:07.364+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:47:07.459+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.458+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:07.469+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.469+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:47:07.487+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.204 seconds
[2025-07-17T22:47:38.471+0000] {processor.py:186} INFO - Started process (PID=1802) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:47:38.472+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:47:38.473+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.473+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:47:38.548+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.547+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:38.557+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:47:38.664+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.663+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:38.675+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.675+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:47:38.697+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.232 seconds
[2025-07-17T22:48:09.279+0000] {processor.py:186} INFO - Started process (PID=1933) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:48:09.280+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:48:09.281+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.281+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:48:09.350+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.350+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:09.358+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:48:09.451+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.451+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:09.462+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.462+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:48:09.483+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.209 seconds
[2025-07-17T22:48:40.303+0000] {processor.py:186} INFO - Started process (PID=2064) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:48:40.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:48:40.307+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.306+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:48:40.381+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.381+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:40.388+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:48:40.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.486+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:40.497+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.497+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:48:40.517+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.221 seconds
[2025-07-17T22:49:10.828+0000] {processor.py:186} INFO - Started process (PID=2195) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:49:10.829+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:49:10.831+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.830+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:49:10.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:10.906+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:10.914+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:49:11.035+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.034+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:11.049+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.048+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:49:11.080+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.258 seconds
[2025-07-17T22:49:41.919+0000] {processor.py:186} INFO - Started process (PID=2326) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:49:41.920+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:49:41.921+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:41.921+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:49:41.983+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:41.983+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:41.991+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:49:42.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.082+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:42.092+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.092+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:49:42.110+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.196 seconds
[2025-07-17T22:50:12.531+0000] {processor.py:186} INFO - Started process (PID=2457) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:50:12.532+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:50:12.534+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:12.533+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:50:12.629+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:12.629+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:12.637+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:50:12.738+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:12.738+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:12.750+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:12.750+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:50:12.770+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.248 seconds
[2025-07-17T22:50:43.249+0000] {processor.py:186} INFO - Started process (PID=2588) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:50:43.249+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:50:43.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.250+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:50:43.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.326+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:43.334+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:50:43.438+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.438+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:43.450+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.450+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:50:43.469+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.227 seconds
[2025-07-17T22:51:14.004+0000] {processor.py:186} INFO - Started process (PID=2719) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:51:14.005+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:51:14.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.006+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:51:14.099+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.099+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:14.107+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:51:14.229+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.229+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:14.252+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.251+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:51:14.281+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.284 seconds
[2025-07-17T22:51:44.561+0000] {processor.py:186} INFO - Started process (PID=2850) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:51:44.561+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:51:44.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:51:44.628+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.628+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:44.637+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:51:44.727+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.727+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:44.739+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.739+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:51:44.760+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.205 seconds
[2025-07-17T22:52:15.244+0000] {processor.py:186} INFO - Started process (PID=2981) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:52:15.245+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-17T22:52:15.246+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.246+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:52:15.312+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.312+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:15.321+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-17T22:52:15.410+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:15.421+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.421+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-17T22:52:15.440+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.201 seconds
