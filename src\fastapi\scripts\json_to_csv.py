#!/usr/bin/env python3
import sys
from pathlib import Path
import pandas as pd
import argparse
try:
    import chardet  # optional; auto‑detects text encoding
except ImportError:
    chardet = None

def detect_encoding(file_path: Path, n_bytes: int = 10000) -> str:
    """
    Try to detect the encoding of a text file using chardet.
    Falls back to UTF‑8 if chardet isn't available or detection fails.
    """
    if chardet is None:
        return "utf-8"
    with file_path.open("rb") as f:
        raw = f.read(n_bytes)
    result = chardet.detect(raw)
    return result.get("encoding") or "utf-8"


def main(json_path: str, src_encoding: str | None = None):
    json_file = Path(json_path)
    if not json_file.is_file():
        sys.exit(f"Файл {json_file} не найден.")

    # Determine source encoding if not provided
    if src_encoding is None:
        src_encoding = detect_encoding(json_file)

    # Read JSON using the detected/explicit encoding
    df = pd.read_json(json_file, orient="records", encoding=src_encoding)

    # Build output file name
    csv_file = json_file.with_suffix(".csv")

    # Write CSV in UTF‑8 with BOM so Excel recognises it correctly
    df.to_csv(csv_file, index=False, encoding="utf-8-sig")
    print(f"CSV сохранён в {csv_file} (UTF‑8 with BOM)")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Конвертация JSON → CSV с учётом кодировок.")
    parser.add_argument("json_path", help="Путь к JSON‑файлу")
    parser.add_argument(
        "-e", "--encoding",
        help="Кодировка входного файла (если не указана — определяется автоматически)",
        default=None
    )
    args = parser.parse_args()
    main(args.json_path, args.encoding)