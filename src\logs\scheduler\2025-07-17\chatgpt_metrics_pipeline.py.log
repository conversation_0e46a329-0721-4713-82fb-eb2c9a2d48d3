[2025-07-17T21:34:30.869+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:34:30.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:34:30.873+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.873+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:34:30.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.952+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:30.963+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:34:31.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.254+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:34:31.285+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.423 seconds
[2025-07-17T21:35:02.163+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:02.164+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:35:02.166+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.166+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:02.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.417+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:02.423+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:02.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.522+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:02.533+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.533+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:35:02.553+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.396 seconds
[2025-07-17T21:35:32.646+0000] {processor.py:186} INFO - Started process (PID=528) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:32.647+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:35:32.650+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.650+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:32.737+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.737+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:32.749+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:32.882+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.896+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.896+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:35:32.921+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.283 seconds
[2025-07-17T21:36:03.099+0000] {processor.py:186} INFO - Started process (PID=664) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:03.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:36:03.103+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:03.181+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.181+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:03.192+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:03.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.306+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.317+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:36:03.341+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.248 seconds
[2025-07-17T21:36:34.140+0000] {processor.py:186} INFO - Started process (PID=800) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:34.141+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:36:34.144+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.143+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:34.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.212+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.221+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:34.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.326+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.340+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:36:34.362+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.227 seconds
