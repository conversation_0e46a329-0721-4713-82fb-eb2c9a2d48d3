[2025-07-17T21:34:30.869+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:34:30.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:34:30.873+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.873+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:34:30.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.952+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:30.963+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:34:31.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.254+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:34:31.285+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.423 seconds
[2025-07-17T21:35:02.163+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:02.164+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:35:02.166+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.166+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:02.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.417+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:02.423+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:02.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.522+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:02.533+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.533+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:35:02.553+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.396 seconds
[2025-07-17T21:35:32.646+0000] {processor.py:186} INFO - Started process (PID=528) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:32.647+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:35:32.650+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.650+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:32.737+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.737+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:32.749+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:32.882+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.896+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.896+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:35:32.921+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.283 seconds
[2025-07-17T21:36:03.099+0000] {processor.py:186} INFO - Started process (PID=664) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:03.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:36:03.103+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:03.181+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.181+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:03.192+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:03.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.306+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.317+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:36:03.341+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.248 seconds
[2025-07-17T21:36:34.140+0000] {processor.py:186} INFO - Started process (PID=800) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:34.141+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:36:34.144+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.143+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:34.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.212+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.221+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:34.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.326+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.340+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:36:34.362+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.227 seconds
[2025-07-17T21:37:04.633+0000] {processor.py:186} INFO - Started process (PID=936) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:04.635+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:37:04.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.637+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:04.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.716+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:04.725+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:04.840+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.840+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:04.854+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.853+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:37:04.873+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.246 seconds
[2025-07-17T21:37:34.986+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:34.987+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:37:34.989+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:34.989+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:35.059+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.059+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.069+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:35.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.165+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:35.175+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.175+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:37:35.193+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.212 seconds
[2025-07-17T21:38:05.462+0000] {processor.py:186} INFO - Started process (PID=1205) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:05.463+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:38:05.466+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.465+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:05.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.545+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:05.554+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:05.654+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.653+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:05.665+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.665+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:38:05.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.230 seconds
[2025-07-17T21:38:36.042+0000] {processor.py:186} INFO - Started process (PID=1341) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:36.043+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:38:36.045+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:36.122+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.122+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:36.132+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:36.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.237+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:36.249+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:38:36.270+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.234 seconds
[2025-07-17T21:39:06.846+0000] {processor.py:186} INFO - Started process (PID=1480) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:06.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:39:06.849+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:06.849+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:06.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:06.938+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:06.950+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:07.059+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.058+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.071+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.071+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:39:07.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.251 seconds
[2025-07-17T21:39:37.320+0000] {processor.py:186} INFO - Started process (PID=1616) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:37.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:39:37.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:37.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.416+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:37.428+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:37.565+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.565+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:37.585+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.584+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:39:37.616+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.302 seconds
[2025-07-17T21:40:08.400+0000] {processor.py:186} INFO - Started process (PID=1752) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:08.401+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:40:08.403+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.403+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:08.495+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.494+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:08.504+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:08.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.631+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:08.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.648+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:40:08.674+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.281 seconds
[2025-07-17T21:40:39.451+0000] {processor.py:186} INFO - Started process (PID=1888) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:39.452+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:40:39.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.454+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:39.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.558+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:39.571+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:39.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:39.745+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.745+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:40:39.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.325 seconds
[2025-07-17T21:42:57.705+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:42:57.707+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:42:57.709+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.709+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:42:57.793+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.793+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:57.802+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:42:58.071+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.071+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:58.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.082+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:42:58.108+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.410 seconds
[2025-07-17T21:43:28.652+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:28.653+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:43:28.655+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.655+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:28.885+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.885+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:28.896+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:28.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.994+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:29.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.004+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:43:29.025+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.380 seconds
[2025-07-17T21:43:59.419+0000] {processor.py:186} INFO - Started process (PID=534) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:59.420+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:43:59.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:59.507+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.507+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:59.517+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:59.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.632+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.644+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:43:59.664+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.252 seconds
[2025-07-17T21:44:30.284+0000] {processor.py:186} INFO - Started process (PID=670) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:44:30.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:44:30.288+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.287+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:44:30.376+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.376+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.384+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:44:30.492+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.492+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:30.504+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.504+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:44:30.525+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.248 seconds
[2025-07-17T21:45:01.441+0000] {processor.py:186} INFO - Started process (PID=806) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:45:01.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:45:01.446+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.446+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:45:01.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.524+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:01.534+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:45:01.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.644+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:01.655+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.655+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:45:01.677+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.241 seconds
[2025-07-17T21:55:25.575+0000] {processor.py:186} INFO - Started process (PID=257) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:55:25.576+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:55:25.579+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.579+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:55:25.664+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.664+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:25.671+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:55:25.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.936+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:25.947+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:25.947+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:55:25.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.398 seconds
[2025-07-17T21:55:56.305+0000] {processor.py:186} INFO - Started process (PID=393) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:55:56.306+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:55:56.309+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.309+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:55:56.503+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.502+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:56.511+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:55:56.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.597+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:56.607+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:56.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:55:56.622+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.323 seconds
[2025-07-17T21:56:26.980+0000] {processor.py:186} INFO - Started process (PID=538) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:56:26.981+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:56:26.983+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.983+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:56:27.054+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.053+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:27.064+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:56:27.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:27.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:27.178+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:56:27.198+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.224 seconds
[2025-07-17T21:56:57.428+0000] {processor.py:186} INFO - Started process (PID=674) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:56:57.429+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:56:57.432+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.431+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:56:57.497+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.497+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:57.506+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:56:57.598+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.597+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:57.607+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:57.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:56:57.625+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.204 seconds
[2025-07-17T21:57:28.212+0000] {processor.py:186} INFO - Started process (PID=808) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:57:28.213+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:57:28.215+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.215+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:57:28.282+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.282+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:28.291+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:57:28.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.387+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:28.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:28.398+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:57:28.418+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.211 seconds
[2025-07-17T21:57:58.802+0000] {processor.py:186} INFO - Started process (PID=944) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:57:58.803+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:57:58.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:58.805+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:57:58.880+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:58.880+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:58.888+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:57:58.993+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:58.993+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:59.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:59.005+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:57:59.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.231 seconds
[2025-07-17T21:58:29.200+0000] {processor.py:186} INFO - Started process (PID=1080) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:58:29.201+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:58:29.204+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.203+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:58:29.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.278+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:29.288+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:58:29.391+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.390+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:29.402+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:29.402+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:58:29.423+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.228 seconds
[2025-07-17T22:00:37.064+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:00:37.065+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:00:37.068+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.067+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:00:37.159+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.159+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:37.170+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:00:37.435+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.435+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:37.445+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:37.445+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:00:37.470+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.411 seconds
[2025-07-17T22:01:07.894+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:01:07.895+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:01:07.897+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.897+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:01:08.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.174+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:08.182+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:01:08.299+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:08.310+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:08.310+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:01:08.333+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.445 seconds
[2025-07-17T22:01:38.935+0000] {processor.py:186} INFO - Started process (PID=528) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:01:38.936+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:01:38.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:38.938+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:01:39.010+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.010+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:39.020+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:01:39.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.131+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:39.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:39.142+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:01:39.163+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.233 seconds
[2025-07-17T22:02:09.435+0000] {processor.py:186} INFO - Started process (PID=664) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:02:09.436+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:02:09.438+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.438+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:02:09.509+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.509+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:09.519+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:02:09.617+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.617+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:09.627+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:09.627+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:02:09.644+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.214 seconds
[2025-07-17T22:02:39.823+0000] {processor.py:186} INFO - Started process (PID=800) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:02:39.823+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:02:39.825+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:39.825+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:02:39.898+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:39.897+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:39.907+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:02:40.009+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.009+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:40.020+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:40.020+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:02:40.039+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.222 seconds
[2025-07-17T22:03:10.150+0000] {processor.py:186} INFO - Started process (PID=933) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:03:10.151+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:03:10.153+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.153+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:03:10.219+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.219+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:10.227+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:03:10.315+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.315+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:10.327+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:10.327+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:03:10.346+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.201 seconds
[2025-07-17T22:03:40.494+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:03:40.495+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:03:40.497+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:40.497+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:03:40.566+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:40.566+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:40.575+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:03:40.669+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:40.668+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:40.679+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:40.678+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:03:40.697+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.209 seconds
[2025-07-17T22:04:11.473+0000] {processor.py:186} INFO - Started process (PID=1208) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:04:11.473+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:04:11.476+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.476+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:04:11.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.545+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:11.554+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:04:11.648+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.648+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:11.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:11.658+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:04:11.678+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.212 seconds
[2025-07-17T22:04:42.064+0000] {processor.py:186} INFO - Started process (PID=1344) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:04:42.065+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:04:42.067+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.067+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:04:42.146+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.146+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:42.154+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:04:42.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.251+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:42.262+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:42.262+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:04:42.281+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.223 seconds
[2025-07-17T22:05:12.334+0000] {processor.py:186} INFO - Started process (PID=1477) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:05:12.335+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:05:12.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:12.337+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:05:12.404+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:12.404+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:12.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:05:12.507+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:12.507+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:12.517+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:12.517+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:05:12.536+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.208 seconds
[2025-07-17T22:05:42.689+0000] {processor.py:186} INFO - Started process (PID=1613) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:05:42.690+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:05:42.693+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:42.692+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:05:42.797+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:42.796+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:42.806+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:05:42.898+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:42.898+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:42.909+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:42.909+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:05:42.928+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.246 seconds
[2025-07-17T22:06:13.268+0000] {processor.py:186} INFO - Started process (PID=1749) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:06:13.269+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:06:13.271+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:13.271+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:06:13.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:13.341+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:13.350+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:06:13.447+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:13.447+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:13.456+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:13.456+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:06:13.470+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.207 seconds
[2025-07-17T22:07:28.645+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:07:28.646+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:07:28.648+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.648+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:07:28.713+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.712+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:28.721+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:07:28.956+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.956+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:28.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:28.965+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:07:28.983+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.343 seconds
[2025-07-17T22:08:00.129+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:08:00.130+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:08:00.132+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.132+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:08:00.361+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.361+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:00.368+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:08:00.457+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.457+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:00.467+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:00.467+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:08:00.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.361 seconds
[2025-07-17T22:08:30.723+0000] {processor.py:186} INFO - Started process (PID=528) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:08:30.724+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:08:30.727+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.726+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:08:30.800+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.800+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:30.809+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:08:30.944+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.943+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:30.958+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:30.957+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:08:30.985+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.268 seconds
[2025-07-17T22:09:01.055+0000] {processor.py:186} INFO - Started process (PID=659) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:09:01.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:09:01.058+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.058+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:09:01.126+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.126+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:01.132+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:09:01.229+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.229+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:01.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:01.239+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:09:01.256+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.207 seconds
[2025-07-17T22:09:31.611+0000] {processor.py:186} INFO - Started process (PID=795) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:09:31.612+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:09:31.614+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.614+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:09:31.688+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.688+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:31.696+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:09:31.815+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.815+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:31.827+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:31.827+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:09:31.850+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.244 seconds
[2025-07-17T22:10:02.362+0000] {processor.py:186} INFO - Started process (PID=936) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:10:02.363+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:10:02.365+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:02.365+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:10:02.441+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:02.441+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:02.449+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:10:02.566+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:02.566+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:02.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:02.582+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:10:02.605+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.249 seconds
[2025-07-17T22:10:33.543+0000] {processor.py:186} INFO - Started process (PID=1072) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:10:33.544+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:10:33.547+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:33.546+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:10:33.622+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:33.622+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:33.629+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:10:33.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:33.715+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:33.725+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:33.725+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:10:33.741+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.204 seconds
[2025-07-17T22:11:04.599+0000] {processor.py:186} INFO - Started process (PID=1208) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:11:04.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:11:04.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:04.603+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:11:04.680+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:04.680+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:04.689+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:11:04.783+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:04.783+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:04.794+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:04.794+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:11:04.815+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.221 seconds
[2025-07-17T22:11:35.523+0000] {processor.py:186} INFO - Started process (PID=1344) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:11:35.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:11:35.527+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:35.527+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:11:35.605+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:35.605+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:35.613+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:11:35.716+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:35.715+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:35.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:35.728+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:11:35.748+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.232 seconds
[2025-07-17T22:12:06.398+0000] {processor.py:186} INFO - Started process (PID=1475) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:12:06.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:12:06.401+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:06.400+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:12:06.470+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:06.470+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:06.479+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:12:06.570+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:06.569+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:06.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:06.582+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:12:06.601+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.209 seconds
[2025-07-17T22:12:36.854+0000] {processor.py:186} INFO - Started process (PID=1606) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:12:36.855+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:12:36.856+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:36.856+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:12:36.926+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:36.926+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:36.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:12:37.029+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:37.029+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:37.043+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:37.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:12:37.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.215 seconds
