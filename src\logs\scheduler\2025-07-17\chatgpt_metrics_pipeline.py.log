[2025-07-17T22:41:29.126+0000] {processor.py:186} INFO - Started process (PID=227) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:41:29.128+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:41:29.132+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.131+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:41:29.217+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.217+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:29.226+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:41:29.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.523+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.523+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:41:29.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.429 seconds
[2025-07-17T22:41:59.819+0000] {processor.py:186} INFO - Started process (PID=357) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:41:59.820+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:41:59.823+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.822+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:41:59.895+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.895+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.903+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:42:00.146+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.146+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:00.158+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.158+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:42:00.177+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.364 seconds
[2025-07-17T22:42:30.627+0000] {processor.py:186} INFO - Started process (PID=490) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:42:30.630+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:42:30.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.631+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:42:30.856+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.855+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.864+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:42:30.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.953+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:42:30.980+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.358 seconds
