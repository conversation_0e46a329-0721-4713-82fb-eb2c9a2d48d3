[2025-07-17T22:21:48.705+0000] {processor.py:186} INFO - Started process (PID=3961) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:21:48.706+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:21:48.707+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.707+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:21:48.772+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.772+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:21:48.781+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:21:48.872+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.872+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:21:48.883+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:21:48.883+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:21:48.900+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.201 seconds
