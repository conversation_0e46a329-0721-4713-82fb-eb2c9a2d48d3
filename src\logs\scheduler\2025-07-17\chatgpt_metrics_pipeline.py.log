[2025-07-17T22:41:29.126+0000] {processor.py:186} INFO - Started process (PID=227) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:41:29.128+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:41:29.132+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.131+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:41:29.217+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.217+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:29.226+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:41:29.337+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:29.523+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:29.523+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:41:29.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.429 seconds
[2025-07-17T22:41:59.819+0000] {processor.py:186} INFO - Started process (PID=357) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:41:59.820+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:41:59.823+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.822+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:41:59.895+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:59.895+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:59.903+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:42:00.146+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.146+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:00.158+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:00.158+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:42:00.177+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.364 seconds
[2025-07-17T22:42:30.627+0000] {processor.py:186} INFO - Started process (PID=490) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:42:30.630+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:42:30.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.631+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:42:30.856+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.855+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:30.864+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:42:30.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.953+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:30.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:30.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:42:30.980+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.358 seconds
[2025-07-17T22:43:01.135+0000] {processor.py:186} INFO - Started process (PID=621) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:43:01.136+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:43:01.137+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.137+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:43:01.208+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.208+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:01.217+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:43:01.317+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.317+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:01.328+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:01.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:43:01.349+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.219 seconds
[2025-07-17T22:43:31.565+0000] {processor.py:186} INFO - Started process (PID=752) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:43:31.566+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:43:31.567+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.567+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:43:31.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.637+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:31.646+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:43:31.740+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.740+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:31.750+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:31.750+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:43:31.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.210 seconds
[2025-07-17T22:44:02.672+0000] {processor.py:186} INFO - Started process (PID=883) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:44:02.674+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:44:02.675+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.675+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:44:02.762+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.762+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:02.771+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:44:02.871+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.870+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:02.882+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:02.882+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:44:02.901+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.234 seconds
[2025-07-17T22:44:33.632+0000] {processor.py:186} INFO - Started process (PID=1014) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:44:33.633+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:44:33.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.634+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:44:33.704+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.704+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:33.713+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:44:33.825+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.825+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:33.839+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:33.839+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:44:33.862+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.236 seconds
[2025-07-17T22:45:04.555+0000] {processor.py:186} INFO - Started process (PID=1145) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:45:04.556+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:45:04.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.557+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:45:04.633+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.633+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:04.642+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:45:04.739+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.739+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:04.751+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:04.751+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:45:04.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.219 seconds
[2025-07-17T22:45:35.352+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:45:35.352+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:45:35.354+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.354+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:45:35.424+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.424+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:35.433+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:45:35.528+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.528+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:35.540+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:35.540+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:45:35.560+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.214 seconds
[2025-07-17T22:46:06.177+0000] {processor.py:186} INFO - Started process (PID=1407) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:46:06.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:46:06.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.179+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:46:06.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.251+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:06.261+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:46:06.360+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:06.373+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:06.373+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:46:06.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.224 seconds
[2025-07-17T22:46:36.482+0000] {processor.py:186} INFO - Started process (PID=1538) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:46:36.482+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:46:36.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:46:36.552+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.552+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:36.560+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:46:36.650+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.650+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:36.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:36.660+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:46:36.679+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.204 seconds
[2025-07-17T22:47:07.525+0000] {processor.py:186} INFO - Started process (PID=1675) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:47:07.526+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:47:07.527+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.526+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:47:07.594+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.594+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:07.603+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:47:07.691+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.691+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:07.701+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:07.701+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:47:07.717+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.198 seconds
[2025-07-17T22:47:38.745+0000] {processor.py:186} INFO - Started process (PID=1811) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:47:38.746+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:47:38.747+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.747+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:47:38.816+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.816+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:38.825+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:47:38.927+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.927+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:38.940+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:38.939+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:47:38.961+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.222 seconds
[2025-07-17T22:48:09.520+0000] {processor.py:186} INFO - Started process (PID=1943) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:48:09.521+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:48:09.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.522+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:48:09.590+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.590+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:09.598+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:48:09.684+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.684+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:09.694+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:09.694+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:48:09.713+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.199 seconds
[2025-07-17T22:48:40.558+0000] {processor.py:186} INFO - Started process (PID=2074) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:48:40.559+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:48:40.560+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.560+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:48:40.633+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.633+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:40.643+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:48:40.746+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.746+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:40.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:40.757+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:48:40.777+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.225 seconds
[2025-07-17T22:49:11.172+0000] {processor.py:186} INFO - Started process (PID=2205) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:49:11.173+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:49:11.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.174+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:49:11.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.251+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:11.260+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:49:11.381+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.381+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:11.393+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:11.393+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:49:11.415+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.250 seconds
[2025-07-17T22:49:42.148+0000] {processor.py:186} INFO - Started process (PID=2336) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:49:42.149+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:49:42.151+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.150+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:49:42.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.220+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:42.229+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:49:42.310+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:42.319+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:42.319+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:49:42.337+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.194 seconds
[2025-07-17T22:50:12.819+0000] {processor.py:186} INFO - Started process (PID=2467) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:50:12.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:50:12.821+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:12.821+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:50:12.895+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:12.895+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:12.904+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:50:13.035+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:13.034+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:13.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:13.046+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:50:13.066+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.254 seconds
[2025-07-17T22:50:43.514+0000] {processor.py:186} INFO - Started process (PID=2598) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:50:43.515+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:50:43.516+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.516+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:50:43.592+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.591+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:43.601+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:50:43.695+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.695+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:43.706+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:43.705+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:50:43.725+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.218 seconds
[2025-07-17T22:51:14.335+0000] {processor.py:186} INFO - Started process (PID=2727) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:51:14.336+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:51:14.338+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.338+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:51:14.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.415+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:14.422+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:51:14.531+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.530+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:14.543+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:14.543+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:51:14.564+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.238 seconds
[2025-07-17T22:51:44.808+0000] {processor.py:186} INFO - Started process (PID=2860) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:51:44.808+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:51:44.810+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.809+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:51:44.876+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.876+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:44.886+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:51:44.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.977+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:44.990+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:44.990+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:51:45.008+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.206 seconds
[2025-07-17T22:52:15.476+0000] {processor.py:186} INFO - Started process (PID=2991) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:52:15.477+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T22:52:15.478+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:52:15.547+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.547+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:15.556+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T22:52:15.642+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.641+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:15.651+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:15.651+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T22:52:15.669+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.198 seconds
