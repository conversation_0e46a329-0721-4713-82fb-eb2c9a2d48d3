[2025-07-17T21:34:30.869+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:34:30.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:34:30.873+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.873+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:34:30.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.952+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:30.963+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:34:31.243+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:31.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:31.254+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:34:31.285+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.423 seconds
[2025-07-17T21:35:02.163+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:02.164+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:35:02.166+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.166+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:02.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.417+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:02.423+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:02.522+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.522+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:02.533+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:02.533+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:35:02.553+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.396 seconds
[2025-07-17T21:35:32.646+0000] {processor.py:186} INFO - Started process (PID=528) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:32.647+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:35:32.650+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.650+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:32.737+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.737+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:32.749+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:35:32.882+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:32.896+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:32.896+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:35:32.921+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.283 seconds
[2025-07-17T21:36:03.099+0000] {processor.py:186} INFO - Started process (PID=664) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:03.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:36:03.103+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:03.181+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.181+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:03.192+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:03.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.306+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:03.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:03.317+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:36:03.341+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.248 seconds
[2025-07-17T21:36:34.140+0000] {processor.py:186} INFO - Started process (PID=800) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:34.141+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:36:34.144+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.143+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:34.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.212+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:34.221+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:36:34.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.326+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:34.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:34.340+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:36:34.362+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.227 seconds
[2025-07-17T21:37:04.633+0000] {processor.py:186} INFO - Started process (PID=936) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:04.635+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:37:04.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.637+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:04.717+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.716+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:04.725+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:04.840+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.840+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:04.854+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:04.853+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:37:04.873+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.246 seconds
[2025-07-17T21:37:34.986+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:34.987+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:37:34.989+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:34.989+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:35.059+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.059+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:35.069+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:37:35.165+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.165+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:35.175+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:35.175+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:37:35.193+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.212 seconds
[2025-07-17T21:38:05.462+0000] {processor.py:186} INFO - Started process (PID=1205) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:05.463+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:38:05.466+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.465+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:05.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.545+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:05.554+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:05.654+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.653+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:05.665+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:05.665+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:38:05.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.230 seconds
[2025-07-17T21:38:36.042+0000] {processor.py:186} INFO - Started process (PID=1341) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:36.043+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:38:36.045+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:36.122+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.122+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:36.132+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:38:36.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.237+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:36.249+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:36.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:38:36.270+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.234 seconds
[2025-07-17T21:39:06.846+0000] {processor.py:186} INFO - Started process (PID=1480) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:06.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:39:06.849+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:06.849+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:06.939+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:06.938+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:06.950+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:07.059+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.058+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:07.071+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:07.071+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:39:07.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.251 seconds
[2025-07-17T21:39:37.320+0000] {processor.py:186} INFO - Started process (PID=1616) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:37.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:39:37.323+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:37.417+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.416+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:37.428+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:39:37.565+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.565+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:37.585+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:37.584+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:39:37.616+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.302 seconds
[2025-07-17T21:40:08.400+0000] {processor.py:186} INFO - Started process (PID=1752) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:08.401+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:40:08.403+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.403+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:08.495+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.494+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:08.504+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:08.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.631+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:08.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:08.648+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:40:08.674+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.281 seconds
[2025-07-17T21:40:39.451+0000] {processor.py:186} INFO - Started process (PID=1888) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:39.452+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:40:39.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.454+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:39.558+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.558+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:39.571+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:40:39.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:39.745+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:39.745+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:40:39.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.325 seconds
[2025-07-17T21:42:57.705+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:42:57.707+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:42:57.709+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.709+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:42:57.793+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:57.793+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:57.802+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:42:58.071+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.071+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:58.082+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:58.082+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:42:58.108+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.410 seconds
[2025-07-17T21:43:28.652+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:28.653+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:43:28.655+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.655+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:28.885+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.885+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:28.896+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:28.994+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:28.994+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:29.004+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:29.004+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:43:29.025+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.380 seconds
[2025-07-17T21:43:59.419+0000] {processor.py:186} INFO - Started process (PID=534) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:59.420+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:43:59.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:59.507+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.507+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:59.517+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:43:59.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.632+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:59.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:59.644+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:43:59.664+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.252 seconds
[2025-07-17T21:44:30.284+0000] {processor.py:186} INFO - Started process (PID=670) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:44:30.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:44:30.288+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.287+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:44:30.376+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.376+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:30.384+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:44:30.492+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.492+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:30.504+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:30.504+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:44:30.525+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.248 seconds
[2025-07-17T21:45:01.441+0000] {processor.py:186} INFO - Started process (PID=806) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:45:01.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-17T21:45:01.446+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.446+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:45:01.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.524+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:01.534+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-17T21:45:01.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.644+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:01.655+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:01.655+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-17T21:45:01.677+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.241 seconds
