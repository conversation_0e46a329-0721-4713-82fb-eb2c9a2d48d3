[loggers]
keys = root,uvicorn,uvicorn.error,uvicorn.access

[handlers]
keys = console

[formatters]
keys = default

[logger_root]
level = INFO
handlers = console

[logger_uvicorn]
level = INFO
handlers = console
propagate = 0
qualname = uvicorn

[logger_uvicorn.error]
level = ERROR
handlers = console
propagate = 0
qualname = uvicorn.error

[logger_uvicorn.access]
level = INFO
handlers = console
propagate = 0
qualname = uvicorn.access

[handler_console]
class = StreamHandler
formatter = default
args = (sys.stdout,)

[formatter_default]
format = %(levelname)s | %(asctime)s | %(name)s | %(message)s
