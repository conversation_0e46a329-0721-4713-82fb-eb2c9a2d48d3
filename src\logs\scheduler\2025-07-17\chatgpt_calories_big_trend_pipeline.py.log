[2025-07-17T22:41:31.602+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:41:31.604+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:41:31.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.605+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:41:31.679+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.679+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:31.687+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:41:31.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.929+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.938+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:41:31.957+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.360 seconds
[2025-07-17T22:42:02.303+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:02.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:42:02.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.306+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:02.537+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.537+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:02.544+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:02.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.649+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:02.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.658+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:42:02.673+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.376 seconds
[2025-07-17T22:42:33.732+0000] {processor.py:186} INFO - Started process (PID=545) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:33.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:42:33.734+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:33.811+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.810+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:33.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:33.932+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:33.946+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.945+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:42:33.968+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.242 seconds
