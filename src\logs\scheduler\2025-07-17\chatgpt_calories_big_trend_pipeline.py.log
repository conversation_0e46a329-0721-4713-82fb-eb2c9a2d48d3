[2025-07-17T22:41:31.602+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:41:31.604+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:41:31.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.605+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:41:31.679+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.679+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:31.687+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:41:31.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.929+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.938+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:41:31.957+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.360 seconds
[2025-07-17T22:42:02.303+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:02.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:42:02.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.306+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:02.537+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.537+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:02.544+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:02.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.649+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:02.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.658+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:42:02.673+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.376 seconds
[2025-07-17T22:42:33.732+0000] {processor.py:186} INFO - Started process (PID=545) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:33.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:42:33.734+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:33.811+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.810+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:33.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:33.932+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:33.946+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.945+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:42:33.968+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.242 seconds
[2025-07-17T22:43:04.151+0000] {processor.py:186} INFO - Started process (PID=684) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:04.152+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:43:04.154+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.153+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:04.230+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.230+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:04.236+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:04.349+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:04.363+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.363+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:43:04.382+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.237 seconds
[2025-07-17T22:43:35.243+0000] {processor.py:186} INFO - Started process (PID=805) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:35.244+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:43:35.245+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.245+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:35.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.317+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:35.328+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:35.446+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.446+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:35.460+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.459+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:43:35.479+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.241 seconds
[2025-07-17T22:44:06.271+0000] {processor.py:186} INFO - Started process (PID=938) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:06.273+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:44:06.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.274+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:06.365+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.365+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:06.374+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:06.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.486+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:06.498+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.497+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:44:06.515+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.250 seconds
[2025-07-17T22:44:37.240+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:37.241+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:44:37.242+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.242+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:37.311+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.311+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:37.318+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:37.413+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.413+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:37.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.422+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:44:37.441+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.206 seconds
[2025-07-17T22:45:07.852+0000] {processor.py:186} INFO - Started process (PID=1210) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:45:07.854+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:45:07.856+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.855+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:45:07.934+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.934+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:07.941+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:45:08.033+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.032+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:08.043+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.043+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:45:08.062+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.216 seconds
[2025-07-17T22:45:38.930+0000] {processor.py:186} INFO - Started process (PID=1339) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:45:38.931+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:45:38.932+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:38.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:45:39.012+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.012+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:39.020+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:45:39.128+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.128+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:39.140+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.140+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:45:39.163+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.239 seconds
[2025-07-17T22:46:09.773+0000] {processor.py:186} INFO - Started process (PID=1460) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:46:09.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:46:09.776+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:09.775+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:46:09.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:09.852+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:09.859+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:46:09.967+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:09.967+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:09.980+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:09.979+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:46:10.001+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.235 seconds
[2025-07-17T22:46:40.108+0000] {processor.py:186} INFO - Started process (PID=1597) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:46:40.109+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:46:40.110+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.110+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:46:40.179+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.179+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:40.189+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:46:40.313+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.312+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:40.324+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.323+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:46:40.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.239 seconds
[2025-07-17T22:47:11.032+0000] {processor.py:186} INFO - Started process (PID=1728) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:47:11.034+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:47:11.035+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:47:11.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.119+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:11.129+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:47:11.229+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.229+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:11.242+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.241+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:47:11.262+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.236 seconds
[2025-07-17T22:47:42.022+0000] {processor.py:186} INFO - Started process (PID=1865) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:47:42.023+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:47:42.024+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.024+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:47:42.097+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.097+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:42.107+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:47:42.217+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.217+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:42.232+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.231+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:47:42.255+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.239 seconds
[2025-07-17T22:48:13.079+0000] {processor.py:186} INFO - Started process (PID=1996) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:48:13.080+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:48:13.081+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.081+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:48:13.151+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.151+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:13.161+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:48:13.261+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.261+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:13.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.273+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:48:13.292+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.217 seconds
[2025-07-17T22:48:44.079+0000] {processor.py:186} INFO - Started process (PID=2127) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:48:44.085+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:48:44.091+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.090+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:48:44.321+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.321+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:44.335+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:48:44.505+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.504+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:44.521+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.521+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:48:44.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.496 seconds
[2025-07-17T22:49:15.041+0000] {processor.py:186} INFO - Started process (PID=2258) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:49:15.042+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:49:15.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:49:15.136+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.136+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:15.144+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:49:15.271+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.271+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:15.288+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.288+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:49:15.315+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.282 seconds
[2025-07-17T22:49:45.629+0000] {processor.py:186} INFO - Started process (PID=2389) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:49:45.630+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:49:45.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:45.631+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:49:45.716+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:45.716+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:45.724+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:49:45.830+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:45.830+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:45.843+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:45.843+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:49:45.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.235 seconds
[2025-07-17T22:50:16.211+0000] {processor.py:186} INFO - Started process (PID=2520) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:50:16.211+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:50:16.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.212+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:50:16.280+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.280+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:16.288+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:50:16.376+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.376+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:16.388+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.387+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:50:16.409+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.204 seconds
[2025-07-17T22:50:46.941+0000] {processor.py:186} INFO - Started process (PID=2651) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:50:46.942+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:50:46.943+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:46.943+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:50:47.010+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.010+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:47.018+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:50:47.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.118+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:47.131+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.130+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:50:47.149+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.214 seconds
[2025-07-17T22:51:17.482+0000] {processor.py:186} INFO - Started process (PID=2782) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:51:17.482+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:51:17.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:51:17.553+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.553+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:17.561+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:51:17.653+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.653+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:17.663+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.663+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:51:17.681+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.205 seconds
[2025-07-17T22:51:48.364+0000] {processor.py:186} INFO - Started process (PID=2913) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:51:48.365+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:51:48.366+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.366+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:51:48.436+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.436+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:48.443+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:51:48.540+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.539+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:48.549+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.549+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:51:48.566+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.207 seconds
[2025-07-17T22:52:19.018+0000] {processor.py:186} INFO - Started process (PID=3044) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:52:19.019+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:52:19.021+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.021+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:52:19.103+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.103+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:19.113+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:52:19.221+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:19.233+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:52:19.251+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.239 seconds
[2025-07-17T22:52:49.492+0000] {processor.py:186} INFO - Started process (PID=3175) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:52:49.492+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:52:49.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:49.493+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:52:49.561+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:49.561+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:49.570+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:52:49.661+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:49.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:49.672+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:49.671+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:52:49.691+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.206 seconds
[2025-07-17T22:53:20.376+0000] {processor.py:186} INFO - Started process (PID=3312) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:53:20.377+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:53:20.378+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:20.378+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:53:20.452+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:20.451+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:20.459+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:53:20.555+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:20.555+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:20.566+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:20.565+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:53:20.586+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.216 seconds
[2025-07-17T22:53:51.320+0000] {processor.py:186} INFO - Started process (PID=3443) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:53:51.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:53:51.322+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:51.322+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:53:51.393+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:51.393+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:51.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:53:51.493+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:51.493+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:51.503+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:51.503+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:53:51.523+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.209 seconds
[2025-07-17T22:54:21.978+0000] {processor.py:186} INFO - Started process (PID=3580) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:54:21.979+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:54:21.981+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:21.980+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:54:22.058+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:22.058+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:54:22.068+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:54:22.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:22.203+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:54:22.220+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:22.219+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:54:22.242+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.271 seconds
