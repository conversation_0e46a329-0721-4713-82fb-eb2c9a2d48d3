[2025-07-17T21:34:32.789+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:34:32.790+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:34:32.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.791+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:34:32.878+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.878+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.885+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:34:33.108+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.108+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:33.118+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.118+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:34:33.142+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.359 seconds
[2025-07-17T21:35:04.659+0000] {processor.py:186} INFO - Started process (PID=439) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:04.661+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:35:04.665+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.664+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:04.864+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.864+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:04.872+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:04.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:04.974+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.974+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:35:04.993+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.340 seconds
[2025-07-17T21:35:35.588+0000] {processor.py:186} INFO - Started process (PID=575) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:35.589+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:35:35.591+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.591+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:35.675+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.675+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:35.684+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:35.787+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.786+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:35.797+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.796+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:35:35.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.238 seconds
[2025-07-17T21:36:05.949+0000] {processor.py:186} INFO - Started process (PID=711) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:05.950+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:36:05.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.953+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:06.045+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.044+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:06.058+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:06.155+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.155+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:06.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.167+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:36:06.187+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.244 seconds
[2025-07-17T21:36:36.283+0000] {processor.py:186} INFO - Started process (PID=845) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:36.284+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:36:36.286+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.285+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:36.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.368+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.376+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:36.482+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.481+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.494+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:36:36.513+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.237 seconds
