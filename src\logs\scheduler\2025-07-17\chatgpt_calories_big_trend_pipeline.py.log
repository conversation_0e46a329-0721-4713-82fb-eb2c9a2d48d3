[2025-07-17T21:34:32.789+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:34:32.790+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:34:32.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.791+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:34:32.878+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:32.878+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:32.885+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:34:33.108+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.108+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:33.118+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:33.118+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:34:33.142+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.359 seconds
[2025-07-17T21:35:04.659+0000] {processor.py:186} INFO - Started process (PID=439) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:04.661+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:35:04.665+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.664+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:04.864+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.864+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:04.872+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:04.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:04.974+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:04.974+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:35:04.993+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.340 seconds
[2025-07-17T21:35:35.588+0000] {processor.py:186} INFO - Started process (PID=575) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:35.589+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:35:35.591+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.591+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:35.675+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.675+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:35.684+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:35:35.787+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.786+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:35.797+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:35.796+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:35:35.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.238 seconds
[2025-07-17T21:36:05.949+0000] {processor.py:186} INFO - Started process (PID=711) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:05.950+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:36:05.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:05.953+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:06.045+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.044+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:06.058+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:06.155+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.155+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:06.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:06.167+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:36:06.187+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.244 seconds
[2025-07-17T21:36:36.283+0000] {processor.py:186} INFO - Started process (PID=845) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:36.284+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:36:36.286+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.285+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:36.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.368+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:36.376+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:36:36.482+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.481+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:36.494+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:36.494+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:36:36.513+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.237 seconds
[2025-07-17T21:37:06.903+0000] {processor.py:186} INFO - Started process (PID=981) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:37:06.904+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:37:06.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.905+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:37:06.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:06.988+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:06.995+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:37:07.091+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.091+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:07.102+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:07.102+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:37:07.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.222 seconds
[2025-07-17T21:37:37.673+0000] {processor.py:186} INFO - Started process (PID=1119) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:37:37.674+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:37:37.676+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.676+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:37:37.763+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.762+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:37.771+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:37:37.878+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.877+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:37.890+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:37.890+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:37:37.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.247 seconds
[2025-07-17T21:38:08.224+0000] {processor.py:186} INFO - Started process (PID=1255) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:38:08.225+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:38:08.227+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.227+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:38:08.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.295+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:08.304+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:38:08.411+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.411+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:08.422+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:08.421+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:38:08.441+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.223 seconds
[2025-07-17T21:38:39.076+0000] {processor.py:186} INFO - Started process (PID=1391) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:38:39.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:38:39.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:38:39.154+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.153+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:39.162+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:38:39.259+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.259+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:39.269+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:39.269+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:38:39.290+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.219 seconds
[2025-07-17T21:39:09.532+0000] {processor.py:186} INFO - Started process (PID=1527) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:39:09.533+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:39:09.535+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.535+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:39:09.613+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.613+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:09.621+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:39:09.723+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.722+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:09.735+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:09.735+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:39:09.756+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.229 seconds
[2025-07-17T21:39:40.164+0000] {processor.py:186} INFO - Started process (PID=1663) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:39:40.165+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:39:40.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.167+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:39:40.254+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.253+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:40.263+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:39:40.375+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.374+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:40.386+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:40.385+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:39:40.406+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.248 seconds
[2025-07-17T21:40:10.841+0000] {processor.py:186} INFO - Started process (PID=1797) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:40:10.842+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:40:10.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:40:10.948+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:10.948+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:10.958+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:40:11.106+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.105+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:11.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:11.117+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:40:11.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.309 seconds
[2025-07-17T21:40:42.136+0000] {processor.py:186} INFO - Started process (PID=1935) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:40:42.138+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:40:42.140+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.140+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:40:42.228+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.228+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:42.238+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:40:42.348+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:42.369+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:42.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:40:42.404+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.274 seconds
[2025-07-17T21:42:59.597+0000] {processor.py:186} INFO - Started process (PID=307) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:42:59.598+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:42:59.600+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.600+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:42:59.678+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.678+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:59.687+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:42:59.941+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.941+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:59.953+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:59.953+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:42:59.972+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.382 seconds
[2025-07-17T21:43:30.744+0000] {processor.py:186} INFO - Started process (PID=443) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:43:30.745+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:43:30.748+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.748+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:43:30.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:30.988+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:30.996+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:43:31.098+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.098+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:31.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:31.107+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:43:31.127+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.390 seconds
[2025-07-17T21:44:01.280+0000] {processor.py:186} INFO - Started process (PID=581) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:44:01.281+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:44:01.283+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.283+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:44:01.367+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.367+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:01.375+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:44:01.475+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.475+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:01.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:01.486+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:44:01.505+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.232 seconds
[2025-07-17T21:44:31.748+0000] {processor.py:186} INFO - Started process (PID=717) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:44:31.749+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:44:31.755+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.754+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:44:31.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.836+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:31.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:44:31.950+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.950+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:31.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:31.962+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:44:31.982+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.240 seconds
[2025-07-17T21:45:02.703+0000] {processor.py:186} INFO - Started process (PID=851) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:45:02.704+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:45:02.706+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.706+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:45:02.779+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.779+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:45:02.787+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:45:02.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.900+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:45:02.911+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:45:02.911+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:45:02.936+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.240 seconds
[2025-07-17T21:55:27.380+0000] {processor.py:186} INFO - Started process (PID=308) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:55:27.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:55:27.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.383+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:55:27.446+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.446+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:27.452+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:55:27.668+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.668+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:27.679+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:27.679+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:55:27.699+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.324 seconds
[2025-07-17T21:55:58.130+0000] {processor.py:186} INFO - Started process (PID=444) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:55:58.131+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:55:58.133+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:55:58.346+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.346+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:58.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:55:58.449+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.448+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:58.457+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:58.457+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:55:58.477+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.352 seconds
[2025-07-17T21:56:28.921+0000] {processor.py:186} INFO - Started process (PID=583) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:56:28.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:56:28.924+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:56:28.998+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:28.998+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:29.005+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:56:29.128+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:29.128+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:29.140+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:29.140+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:56:29.161+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.245 seconds
[2025-07-17T21:56:59.290+0000] {processor.py:186} INFO - Started process (PID=719) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:56:59.291+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:56:59.294+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:59.293+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:56:59.374+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:59.373+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:59.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:56:59.498+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:59.498+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:59.509+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:59.509+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:56:59.529+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.248 seconds
[2025-07-17T21:57:29.772+0000] {processor.py:186} INFO - Started process (PID=855) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:57:29.773+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:57:29.775+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.774+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:57:29.840+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.840+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:29.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:57:29.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:29.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:29.977+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:57:29.994+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.228 seconds
[2025-07-17T21:58:00.243+0000] {processor.py:186} INFO - Started process (PID=989) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:58:00.245+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:58:00.247+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.247+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:58:00.321+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.321+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:00.328+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:58:00.430+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.430+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:00.441+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:00.440+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:58:00.458+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.221 seconds
[2025-07-17T21:58:30.547+0000] {processor.py:186} INFO - Started process (PID=1125) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:58:30.548+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T21:58:30.550+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.550+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:58:30.622+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.621+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:30.630+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T21:58:30.730+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.730+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:30.741+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:30.741+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T21:58:30.761+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.219 seconds
[2025-07-17T22:00:38.747+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:00:38.755+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:00:38.758+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.757+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:00:38.828+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:38.828+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:38.836+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:00:39.132+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:39.132+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:39.149+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:39.148+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:00:39.168+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.427 seconds
[2025-07-17T22:01:10.147+0000] {processor.py:186} INFO - Started process (PID=437) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:01:10.148+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:01:10.151+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.151+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:01:10.395+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.395+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:10.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:01:10.488+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.488+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:10.500+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:10.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:01:10.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.379 seconds
[2025-07-17T22:01:41.294+0000] {processor.py:186} INFO - Started process (PID=573) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:01:41.295+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:01:41.298+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.298+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:01:41.381+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.381+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:41.387+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:01:41.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.502+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:41.513+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:41.513+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:01:41.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.246 seconds
[2025-07-17T22:02:11.721+0000] {processor.py:186} INFO - Started process (PID=711) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:02:11.722+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:02:11.725+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:11.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:02:11.796+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:11.796+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:11.804+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:02:11.896+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:11.896+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:11.907+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:11.907+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:02:11.926+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.210 seconds
[2025-07-17T22:02:42.114+0000] {processor.py:186} INFO - Started process (PID=847) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:02:42.115+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:02:42.117+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.116+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:02:42.185+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.184+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:42.193+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:02:42.284+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.284+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:42.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:42.293+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:02:42.310+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.201 seconds
[2025-07-17T22:03:12.529+0000] {processor.py:186} INFO - Started process (PID=983) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:03:12.530+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:03:12.533+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.533+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:03:12.601+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.601+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:12.609+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:03:12.700+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.700+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:12.710+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:12.710+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:03:12.726+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.203 seconds
[2025-07-17T22:03:43.670+0000] {processor.py:186} INFO - Started process (PID=1117) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:03:43.672+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:03:43.677+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.676+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:03:43.797+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.797+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:43.806+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:03:43.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.938+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:43.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:43.952+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:03:43.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.316 seconds
[2025-07-17T22:04:14.265+0000] {processor.py:186} INFO - Started process (PID=1255) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:04:14.266+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:04:14.268+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:14.267+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:04:14.335+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:14.335+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:14.342+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:04:14.442+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:14.442+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:14.453+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:14.453+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:04:14.471+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.212 seconds
[2025-07-17T22:04:44.655+0000] {processor.py:186} INFO - Started process (PID=1391) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:04:44.656+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:04:44.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.658+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:04:44.731+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.731+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:44.740+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:04:44.859+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.859+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:44.870+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:44.870+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:04:44.893+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.244 seconds
[2025-07-17T22:05:14.956+0000] {processor.py:186} INFO - Started process (PID=1527) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:05:14.957+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:05:14.960+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:14.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:05:15.034+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:15.034+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:15.041+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:05:15.127+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:15.126+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:15.137+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:15.137+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:05:15.153+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.203 seconds
[2025-07-17T22:05:45.497+0000] {processor.py:186} INFO - Started process (PID=1661) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:05:45.499+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:05:45.501+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.500+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:05:45.577+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.577+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:45.586+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:05:45.698+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.697+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:45.709+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:45.708+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:05:45.728+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.237 seconds
[2025-07-17T22:06:15.882+0000] {processor.py:186} INFO - Started process (PID=1797) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:06:15.882+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:06:15.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.884+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:06:15.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:15.951+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:15.958+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:06:16.050+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:16.050+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:16.060+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:16.060+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:06:16.077+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.201 seconds
[2025-07-17T22:07:30.318+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:07:30.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:07:30.321+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.320+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:07:30.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.387+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:30.394+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:07:30.631+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.631+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:30.643+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:30.643+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:07:30.660+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.347 seconds
[2025-07-17T22:08:02.005+0000] {processor.py:186} INFO - Started process (PID=437) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:08:02.006+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:08:02.008+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:02.008+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:08:02.218+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:02.218+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:02.224+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:08:02.310+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:02.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:02.320+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:02.319+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:08:02.336+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.336 seconds
[2025-07-17T22:08:32.999+0000] {processor.py:186} INFO - Started process (PID=573) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:08:33.000+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:08:33.003+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:33.003+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:08:33.080+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:33.080+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:33.089+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:08:33.226+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:33.226+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:33.239+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:33.239+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:08:33.257+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.265 seconds
[2025-07-17T22:09:03.905+0000] {processor.py:186} INFO - Started process (PID=711) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:09:03.906+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:09:03.909+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:03.908+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:09:03.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:03.975+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:03.983+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:09:04.085+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:04.085+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:04.097+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:04.096+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:09:04.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.216 seconds
