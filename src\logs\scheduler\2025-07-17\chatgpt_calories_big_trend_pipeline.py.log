[2025-07-17T22:41:31.602+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:41:31.604+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:41:31.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.605+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:41:31.679+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.679+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:31.687+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:41:31.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.929+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.938+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:41:31.957+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.360 seconds
[2025-07-17T22:42:02.303+0000] {processor.py:186} INFO - Started process (PID=412) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:02.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:42:02.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.306+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:02.537+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.537+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:02.544+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:02.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.649+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:02.658+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.658+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:42:02.673+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.376 seconds
[2025-07-17T22:42:33.732+0000] {processor.py:186} INFO - Started process (PID=545) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:33.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:42:33.734+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:33.811+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.810+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:33.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:42:33.932+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:33.946+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.945+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:42:33.968+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.242 seconds
[2025-07-17T22:43:04.151+0000] {processor.py:186} INFO - Started process (PID=684) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:04.152+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:43:04.154+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.153+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:04.230+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.230+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:04.236+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:04.349+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:04.363+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.363+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:43:04.382+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.237 seconds
[2025-07-17T22:43:35.243+0000] {processor.py:186} INFO - Started process (PID=805) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:35.244+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:43:35.245+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.245+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:35.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.317+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:35.328+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:43:35.446+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.446+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:35.460+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.459+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:43:35.479+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.241 seconds
[2025-07-17T22:44:06.271+0000] {processor.py:186} INFO - Started process (PID=938) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:06.273+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:44:06.274+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.274+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:06.365+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.365+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:06.374+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:06.486+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.486+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:06.498+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.497+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:44:06.515+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.250 seconds
[2025-07-17T22:44:37.240+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:37.241+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-17T22:44:37.242+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.242+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:37.311+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.311+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:37.318+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-17T22:44:37.413+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.413+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:37.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.422+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-17T22:44:37.441+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.206 seconds
