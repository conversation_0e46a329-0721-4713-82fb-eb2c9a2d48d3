"""
Комплексное тестирование системы мониторинга затрат Biome AI
БЕЗОПАСНОЕ ТЕСТИРОВАНИЕ - ИСПОЛЬЗУЕТ ТЕСТОВУЮ БАЗУ ДАННЫХ
"""

import sys
import os
import asyncio
import httpx
import json
from datetime import datetime

# Добавляем пути для импорта модулей
sys.path.append(os.path.join(os.path.dirname(__file__), 'dags'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'fastapi'))

async def test_api_endpoints():
    """
    Тестирует API эндпоинты мониторинга затрат
    """
    print("🌐 ТЕСТИРОВАНИЕ API ЭНДПОИНТОВ")
    print("=" * 50)
    
    base_url = "http://localhost:8000"  # Предполагаемый адрес FastAPI
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Тест проверки здоровья системы
        print("1. Тестирование health check...")
        try:
            response = await client.get(f"{base_url}/cost-monitoring/health")
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Health check: {health_data['status']}")
                print(f"   Redis подключен: {health_data['redis_connected']}")
                print(f"   Мониторинг доступен: {health_data['cost_tracking_available']}")
            else:
                print(f"❌ Health check failed: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Health check недоступен (сервер не запущен?): {e}")
        
        # Тест получения типов операций
        print("\n2. Тестирование получения типов операций...")
        try:
            response = await client.get(f"{base_url}/cost-monitoring/operation-types")
            if response.status_code == 200:
                types_data = response.json()
                print(f"✅ Получено типов операций: {types_data['total_count']}")
                for op_type in types_data['operation_types'][:3]:  # Показываем первые 3
                    print(f"   - {op_type['name']}: {op_type['value']}")
            else:
                print(f"❌ Ошибка получения типов операций: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Типы операций недоступны: {e}")
        
        # Тест получения сценариев
        print("\n3. Тестирование получения сценариев...")
        try:
            response = await client.get(f"{base_url}/cost-monitoring/scenarios")
            if response.status_code == 200:
                scenarios_data = response.json()
                print(f"✅ Получено сценариев: {len(scenarios_data['scenarios'])}")
                print(f"   Оценочная месячная стоимость: ${scenarios_data['estimated_monthly_cost']:.2f}")
                print(f"   Самый дорогой сценарий: {scenarios_data['most_expensive_scenario']}")
            else:
                print(f"❌ Ошибка получения сценариев: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Сценарии недоступны: {e}")
        
        # Тест получения статистики
        print("\n4. Тестирование получения статистики...")
        try:
            response = await client.get(f"{base_url}/cost-monitoring/stats?days=7")
            if response.status_code == 200:
                stats_data = response.json()
                print(f"✅ Статистика за {stats_data['period_days']} дней")
                print(f"   Общая стоимость: ${stats_data['total_cost']:.6f}")
                print(f"   Общее количество операций: {stats_data['total_operations']}")
            else:
                print(f"❌ Ошибка получения статистики: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Статистика недоступна: {e}")
        
        # Тест получения сводки
        print("\n5. Тестирование получения сводки...")
        try:
            response = await client.get(f"{base_url}/cost-monitoring/summary?days=7")
            if response.status_code == 200:
                summary_data = response.json()
                print(f"✅ Сводка за {summary_data['period_days']} дней получена")
            else:
                print(f"❌ Ошибка получения сводки: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Сводка недоступна: {e}")

def test_cost_tracking_module():
    """
    Тестирует модуль мониторинга затрат
    """
    print("\n📊 ТЕСТИРОВАНИЕ МОДУЛЯ МОНИТОРИНГА ЗАТРАТ")
    print("=" * 50)
    
    try:
        from cost_tracking import CostTracker, OperationType, track_openai_request, UsageScenarios
        
        # Тест инициализации трекера
        print("1. Тестирование инициализации трекера...")
        tracker = CostTracker(redis_db=1)  # Используем тестовую базу данных
        print("✅ Трекер успешно инициализирован")
        
        # Тест анализа токенов
        print("\n2. Тестирование анализа токенов...")
        test_prompt = "Проанализируй питательную ценность банана"
        test_response = "Банан содержит калий, витамин B6, витамин C и клетчатку. Средний банан содержит около 105 калорий."
        
        token_analysis = tracker.analyze_token_usage(test_prompt, test_response)
        if "error" not in token_analysis:
            print(f"✅ Анализ токенов: {token_analysis['total_tokens']} токенов, ${token_analysis['total_cost']:.6f}")
        else:
            print(f"❌ Ошибка анализа токенов: {token_analysis['error']}")
        
        # Тест отслеживания операции
        print("\n3. Тестирование отслеживания операции...")
        success = tracker.track_operation_cost(
            operation_type=OperationType.FOOD_ANALYSIS,
            user_id=99999,  # Тестовый пользователь
            session_id="test_session_complete",
            token_usage=token_analysis,
            additional_metadata={"test": "complete_system_test"}
        )
        
        if success:
            print("✅ Операция успешно отслежена")
        else:
            print("❌ Ошибка отслеживания операции")
        
        # Тест удобной функции
        print("\n4. Тестирование удобной функции...")
        result = track_openai_request(
            operation_type=OperationType.GENERAL_CHAT,
            input_prompt="Привет, как дела?",
            output_response="Привет! У меня все хорошо, спасибо за вопрос!",
            user_id=99999,
            session_id="test_session_complete_2"
        )
        
        if "error" not in result:
            print(f"✅ Удобная функция: ${result['total_cost']:.6f}")
        else:
            print(f"❌ Ошибка удобной функции: {result['error']}")
        
        # Тест получения статистики
        print("\n5. Тестирование получения статистики...")
        stats = tracker.get_operation_stats(days=1)
        if "error" not in stats:
            print(f"✅ Статистика получена: {len(stats['statistics'])} типов операций")
        else:
            print(f"❌ Ошибка получения статистики: {stats['error']}")
        
        # Тест сценариев
        print("\n6. Тестирование сценариев использования...")
        scenarios = UsageScenarios.get_scenario_definitions()
        print(f"✅ Определено сценариев: {len(scenarios)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Ошибка импорта модуля: {e}")
        return False
    except Exception as e:
        print(f"❌ Ошибка тестирования модуля: {e}")
        return False

def test_dag_integration():
    """
    Тестирует интеграцию с DAG файлами
    """
    print("\n🔄 ТЕСТИРОВАНИЕ ИНТЕГРАЦИИ С DAG")
    print("=" * 50)
    
    # Проверяем, что файлы обновлены
    dags_path = os.path.join(os.path.dirname(__file__), 'dags')
    
    if not os.path.exists(dags_path):
        print("❌ Директория dags не найдена")
        return False
    
    # Проверяем наличие основных файлов
    required_files = [
        'cost_tracking.py',
        'test_cost_tracking.py',
        'update_all_dags.py'
    ]
    
    for filename in required_files:
        file_path = os.path.join(dags_path, filename)
        if os.path.exists(file_path):
            print(f"✅ {filename} найден")
        else:
            print(f"❌ {filename} не найден")
    
    # Проверяем, что хотя бы один DAG файл обновлен
    sample_dag = os.path.join(dags_path, 'chatgpt_challenge_ask_pipeline.py')
    if os.path.exists(sample_dag):
        with open(sample_dag, 'r', encoding='utf-8') as f:
            content = f.read()
            if "from cost_tracking import" in content:
                print("✅ DAG файлы обновлены для использования системы мониторинга")
            else:
                print("⚠️ DAG файлы еще не обновлены (запустите update_all_dags.py)")
    
    return True

def cleanup_test_data():
    """
    Очищает тестовые данные
    """
    print("\n🧹 ОЧИСТКА ТЕСТОВЫХ ДАННЫХ")
    print("=" * 30)
    
    try:
        from cost_tracking import CostTracker
        tracker = CostTracker(redis_db=1)  # Тестовая база данных
        
        if tracker.redis_client:
            # Удаляем все ключи из тестовой базы данных
            keys_deleted = 0
            for key in tracker.redis_client.scan_iter("*"):
                tracker.redis_client.delete(key)
                keys_deleted += 1
            
            print(f"✅ Удалено {keys_deleted} тестовых ключей из Redis")
        else:
            print("⚠️ Redis недоступен для очистки")
            
    except Exception as e:
        print(f"❌ Ошибка очистки: {e}")

async def main():
    """
    Основная функция тестирования
    """
    print("🚀 КОМПЛЕКСНОЕ ТЕСТИРОВАНИЕ СИСТЕМЫ МОНИТОРИНГА ЗАТРАТ BIOME AI")
    print("=" * 80)
    print(f"Время начала: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # Тестируем модуль мониторинга затрат
        module_success = test_cost_tracking_module()
        
        # Тестируем интеграцию с DAG
        dag_success = test_dag_integration()
        
        # Тестируем API эндпоинты
        await test_api_endpoints()
        
        # Очищаем тестовые данные
        cleanup_test_data()
        
        print("\n" + "=" * 80)
        print("📋 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
        print(f"   Модуль мониторинга: {'✅ Успешно' if module_success else '❌ Ошибки'}")
        print(f"   Интеграция с DAG: {'✅ Успешно' if dag_success else '❌ Ошибки'}")
        print("   API эндпоинты: Проверьте логи выше")
        
        if module_success and dag_success:
            print("\n🎉 СИСТЕМА ГОТОВА К ИСПОЛЬЗОВАНИЮ В ПРОДАКШЕНЕ!")
            print("\nСледующие шаги:")
            print("1. Запустите update_all_dags.py для обновления всех DAG файлов")
            print("2. Перезапустите Airflow для применения изменений")
            print("3. Запустите FastAPI сервер для доступа к API мониторинга")
            print("4. Проверьте работу системы на реальных данных")
        else:
            print("\n⚠️ ОБНАРУЖЕНЫ ПРОБЛЕМЫ!")
            print("Исправьте ошибки перед использованием в продакшене.")
        
    except KeyboardInterrupt:
        print("\n⚠️ Тестирование прервано пользователем")
        cleanup_test_data()
    except Exception as e:
        print(f"\n💥 Критическая ошибка: {e}")
        cleanup_test_data()

if __name__ == "__main__":
    asyncio.run(main())
