"""
Тестовый скрипт для проверки системы мониторинга затрат
БЕЗОПАСНОЕ ТЕСТИРОВАНИЕ - НЕ ВЛИЯЕТ НА ПРОДАКШН ДАННЫЕ
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cost_tracking import CostTracker, OperationType, track_openai_request, get_cost_summary, UsageScenarios
import json
from datetime import datetime

def test_cost_tracking_system():
    """
    Безопасное тестирование системы мониторинга затрат
    """
    print("🧪 НАЧАЛО ТЕСТИРОВАНИЯ СИСТЕМЫ МОНИТОРИНГА ЗАТРАТ")
    print("=" * 60)
    
    # Инициализация трекера с тестовой базой данных
    print("1. Инициализация трекера затрат...")
    try:
        # Используем отдельную тестовую базу данных (db=1)
        tracker = CostTracker(redis_db=1)
        print("✅ Трекер успешно инициализирован")
    except Exception as e:
        print(f"❌ Ошибка инициализации: {e}")
        return False
    
    # Тест анализа токенов
    print("\n2. Тестирование анализа токенов...")
    test_prompt = "Проанализируй следующую еду и рассчитай калории: яблоко среднего размера"
    test_response = "Яблоко среднего размера (около 150г) содержит примерно 80 калорий, 0.3г белков, 0.2г жиров и 21г углеводов."
    
    token_analysis = tracker.analyze_token_usage(
        input_prompt=test_prompt,
        output_response=test_response,
        model="gpt-4o"
    )
    
    if "error" not in token_analysis:
        print("✅ Анализ токенов работает корректно")
        print(f"   Входные токены: {token_analysis['tokens_input']}")
        print(f"   Выходные токены: {token_analysis['tokens_output']}")
        print(f"   Общая стоимость: ${token_analysis['total_cost']:.6f}")
    else:
        print(f"❌ Ошибка анализа токенов: {token_analysis['error']}")
        return False
    
    # Тест расчета стоимости хранения
    print("\n3. Тестирование расчета стоимости хранения...")
    storage_cost = tracker.calculate_storage_cost(
        data_size_mb=10.5,
        storage_days=30
    )
    
    if "error" not in storage_cost:
        print("✅ Расчет стоимости хранения работает корректно")
        print(f"   Размер данных: {storage_cost['data_size_mb']} MB")
        print(f"   Стоимость хранения: ${storage_cost['storage_cost']:.6f}")
    else:
        print(f"❌ Ошибка расчета стоимости хранения: {storage_cost['error']}")
    
    # Тест расчета стоимости интеграций
    print("\n4. Тестирование расчета стоимости интеграций...")
    integration_cost = tracker.calculate_integration_cost(
        api_calls=100,
        cost_per_call=0.001
    )
    
    if "error" not in integration_cost:
        print("✅ Расчет стоимости интеграций работает корректно")
        print(f"   API вызовы: {integration_cost['api_calls']}")
        print(f"   Стоимость интеграций: ${integration_cost['integration_cost']:.6f}")
    else:
        print(f"❌ Ошибка расчета стоимости интеграций: {integration_cost['error']}")
    
    # Тест сохранения метрик
    print("\n5. Тестирование сохранения метрик в Redis...")
    success = tracker.track_operation_cost(
        operation_type=OperationType.FOOD_ANALYSIS,
        user_id=12345,  # Тестовый пользователь
        session_id="test_session_001",
        token_usage=token_analysis,
        storage_cost=storage_cost,
        integration_cost=integration_cost,
        additional_metadata={"test_mode": True}
    )
    
    if success:
        print("✅ Метрики успешно сохранены в Redis")
    else:
        print("❌ Ошибка сохранения метрик")
    
    # Тест удобной функции track_openai_request
    print("\n6. Тестирование удобной функции track_openai_request...")
    try:
        result = track_openai_request(
            operation_type=OperationType.RECOMMENDATION_GENERATION,
            input_prompt="Создай рекомендацию по питанию для пользователя",
            output_response="Рекомендую увеличить потребление овощей и фруктов до 5 порций в день.",
            user_id=12345,
            session_id="test_session_002",
            additional_metadata={"test_mode": True}
        )
        print("✅ Функция track_openai_request работает корректно")
        print(f"   Стоимость операции: ${result['total_cost']:.6f}")
    except Exception as e:
        print(f"❌ Ошибка в track_openai_request: {e}")
    
    # Тест получения статистики
    print("\n7. Тестирование получения статистики...")
    try:
        stats = get_cost_summary(days=1)
        if "error" not in stats:
            print("✅ Получение статистики работает корректно")
            print(f"   Период анализа: {stats['period_days']} дней")
            print(f"   Количество типов операций: {len(stats['statistics'])}")
        else:
            print(f"❌ Ошибка получения статистики: {stats['error']}")
    except Exception as e:
        print(f"❌ Ошибка получения статистики: {e}")
    
    # Тест сценариев использования
    print("\n8. Тестирование сценариев использования...")
    scenarios = UsageScenarios.get_scenario_definitions()
    print("✅ Определены следующие сценарии использования:")
    for scenario_id, scenario in scenarios.items():
        print(f"   - {scenario['name']}: ~${scenario['estimated_cost']:.2f}")
    
    print("\n" + "=" * 60)
    print("🎉 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО УСПЕШНО!")
    print("Система мониторинга затрат готова к использованию в продакшене.")
    return True

def cleanup_test_data():
    """
    Очистка тестовых данных из Redis
    """
    print("\n🧹 Очистка тестовых данных...")
    try:
        tracker = CostTracker(redis_db=1)
        if tracker.redis_client:
            # Удаляем все ключи из тестовой базы данных
            for key in tracker.redis_client.scan_iter("*"):
                tracker.redis_client.delete(key)
            print("✅ Тестовые данные очищены")
    except Exception as e:
        print(f"❌ Ошибка очистки: {e}")

if __name__ == "__main__":
    try:
        # Запускаем тестирование
        success = test_cost_tracking_system()
        
        # Очищаем тестовые данные
        cleanup_test_data()
        
        if success:
            print("\n✅ ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
            print("Система готова к интеграции с DAG файлами.")
        else:
            print("\n❌ НЕКОТОРЫЕ ТЕСТЫ НЕ ПРОШЛИ!")
            print("Проверьте логи и исправьте ошибки.")
            
    except KeyboardInterrupt:
        print("\n⚠️ Тестирование прервано пользователем")
        cleanup_test_data()
    except Exception as e:
        print(f"\n💥 Критическая ошибка: {e}")
        cleanup_test_data()
