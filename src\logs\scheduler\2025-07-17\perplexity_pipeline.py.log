[2025-07-17T22:41:32.141+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:41:32.142+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:41:32.145+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.145+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:41:32.236+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.236+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:32.246+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:41:32.542+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.542+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:32.551+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:32.551+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:41:32.573+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.440 seconds
[2025-07-17T22:42:02.792+0000] {processor.py:186} INFO - Started process (PID=432) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:42:02.793+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:42:02.796+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.795+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:42:03.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.015+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:03.022+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:42:03.132+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.132+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:03.142+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:03.141+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:42:03.158+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.371 seconds
[2025-07-17T22:42:34.007+0000] {processor.py:186} INFO - Started process (PID=563) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:42:34.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:42:34.009+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:42:34.085+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.085+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:34.093+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:42:34.196+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:34.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:34.209+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:42:34.230+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.229 seconds
[2025-07-17T22:43:04.440+0000] {processor.py:186} INFO - Started process (PID=694) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:43:04.441+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:43:04.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.442+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:43:04.533+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.533+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:04.540+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:43:04.640+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.640+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:04.651+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:04.651+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:43:04.670+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.238 seconds
[2025-07-17T22:43:35.646+0000] {processor.py:186} INFO - Started process (PID=825) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:43:35.647+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:43:35.649+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.648+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:43:35.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.727+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:35.738+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:43:35.840+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.840+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:35.852+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.852+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:43:35.871+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.230 seconds
[2025-07-17T22:44:06.557+0000] {processor.py:186} INFO - Started process (PID=956) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:44:06.558+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:44:06.560+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.559+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:44:06.638+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.638+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:06.646+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:44:06.753+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:06.765+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:06.765+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:44:06.786+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.234 seconds
[2025-07-17T22:44:37.484+0000] {processor.py:186} INFO - Started process (PID=1087) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:44:37.485+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:44:37.487+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.487+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:44:37.559+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.559+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:37.568+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:44:37.671+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.671+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:37.683+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:37.683+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:44:37.700+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.223 seconds
[2025-07-17T22:45:08.113+0000] {processor.py:186} INFO - Started process (PID=1218) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:45:08.114+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:45:08.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.115+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:45:08.188+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.187+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:08.197+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:45:08.302+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.302+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:08.315+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:08.314+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:45:08.336+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.229 seconds
[2025-07-17T22:45:39.211+0000] {processor.py:186} INFO - Started process (PID=1349) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:45:39.212+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:45:39.213+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.213+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:45:39.295+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.294+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:39.305+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:45:39.454+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.453+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:39.465+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:39.465+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:45:39.487+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.282 seconds
[2025-07-17T22:46:10.189+0000] {processor.py:186} INFO - Started process (PID=1480) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:46:10.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:46:10.191+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.191+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:46:10.266+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.266+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:10.276+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:46:10.377+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.377+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:10.387+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:10.387+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:46:10.405+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.221 seconds
[2025-07-17T22:46:40.516+0000] {processor.py:186} INFO - Started process (PID=1617) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:46:40.517+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:46:40.518+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.518+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:46:40.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.582+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:40.592+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:46:40.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.680+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:40.691+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:40.691+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:46:40.709+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.198 seconds
[2025-07-17T22:47:11.434+0000] {processor.py:186} INFO - Started process (PID=1748) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:47:11.435+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:47:11.436+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.436+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:47:11.506+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.506+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:11.516+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:47:11.617+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.616+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:11.629+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:11.629+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:47:11.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.219 seconds
[2025-07-17T22:47:42.461+0000] {processor.py:186} INFO - Started process (PID=1885) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:47:42.462+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:47:42.463+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.463+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:47:42.533+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.533+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:42.541+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:47:42.635+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:42.644+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:42.644+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:47:42.664+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.210 seconds
[2025-07-17T22:48:13.458+0000] {processor.py:186} INFO - Started process (PID=2016) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:48:13.459+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:48:13.461+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.460+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:48:13.525+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.525+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:13.533+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:48:13.627+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.627+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:13.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:13.637+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:48:13.653+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.200 seconds
[2025-07-17T22:48:44.800+0000] {processor.py:186} INFO - Started process (PID=2147) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:48:44.802+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:48:44.803+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.803+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:48:44.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:44.884+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:44.894+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:48:45.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:45.014+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:45.028+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:45.028+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:48:45.052+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.261 seconds
[2025-07-17T22:49:15.522+0000] {processor.py:186} INFO - Started process (PID=2278) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:49:15.524+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:49:15.526+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.525+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:49:15.615+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.615+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:15.625+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:49:15.755+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.755+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:15.770+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:15.770+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:49:15.792+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.275 seconds
[2025-07-17T22:49:46.036+0000] {processor.py:186} INFO - Started process (PID=2409) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:49:46.037+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:49:46.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:46.038+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:49:46.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:46.113+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:46.121+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:49:46.212+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:46.212+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:46.223+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:46.223+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:49:46.243+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.213 seconds
[2025-07-17T22:50:16.609+0000] {processor.py:186} INFO - Started process (PID=2540) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:50:16.610+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:50:16.611+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.611+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:50:16.681+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.680+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:16.688+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:50:16.790+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.790+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:16.801+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:16.801+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:50:16.819+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.216 seconds
[2025-07-17T22:50:47.348+0000] {processor.py:186} INFO - Started process (PID=2671) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:50:47.349+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:50:47.350+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.350+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:50:47.426+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.426+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:47.433+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:50:47.528+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.527+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:47.540+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:47.540+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:50:47.562+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.223 seconds
[2025-07-17T22:51:17.858+0000] {processor.py:186} INFO - Started process (PID=2802) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:51:17.859+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:51:17.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.859+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:51:17.924+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:17.923+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:17.934+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:51:18.022+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:18.021+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:18.031+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:18.031+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:51:18.048+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.196 seconds
[2025-07-17T22:51:48.740+0000] {processor.py:186} INFO - Started process (PID=2933) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:51:48.741+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:51:48.742+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:51:48.813+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.813+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:48.823+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:51:48.919+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.918+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:48.929+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:48.928+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:51:48.944+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.210 seconds
[2025-07-17T22:52:19.416+0000] {processor.py:186} INFO - Started process (PID=3064) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:52:19.417+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:52:19.419+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.418+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:52:19.490+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.490+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:19.499+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:52:19.594+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.594+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:19.604+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:19.604+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:52:19.621+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.211 seconds
[2025-07-17T22:52:49.903+0000] {processor.py:186} INFO - Started process (PID=3195) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:52:49.904+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:52:49.905+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:49.904+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:52:49.969+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:49.968+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:49.977+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:52:50.065+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:50.065+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:50.076+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:50.076+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:52:50.093+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.196 seconds
[2025-07-17T22:53:20.769+0000] {processor.py:186} INFO - Started process (PID=3332) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:53:20.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:53:20.771+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:20.771+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:53:20.841+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:20.841+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:20.849+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:53:20.949+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:20.949+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:20.959+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:20.959+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:53:20.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.213 seconds
[2025-07-17T22:53:51.726+0000] {processor.py:186} INFO - Started process (PID=3463) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:53:51.727+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:53:51.729+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:51.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:53:51.805+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:51.804+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:51.818+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:53:51.926+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:51.925+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:51.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:51.937+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:53:51.957+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.237 seconds
[2025-07-17T22:54:22.430+0000] {processor.py:186} INFO - Started process (PID=3600) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:54:22.431+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:54:22.432+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:22.432+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:54:22.499+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:22.499+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:54:22.508+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:54:22.602+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:22.602+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:54:22.613+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:22.613+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:54:22.629+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.205 seconds
