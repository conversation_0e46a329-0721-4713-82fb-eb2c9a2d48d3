[2025-07-17T21:34:29.748+0000] {processor.py:186} INFO - Started process (PID=231) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:34:29.749+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:34:29.751+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.751+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:34:29.823+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.822+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:29.832+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:34:30.078+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.078+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:30.087+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:30.087+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:34:30.108+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.366 seconds
[2025-07-17T21:35:01.109+0000] {processor.py:186} INFO - Started process (PID=369) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:35:01.110+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:35:01.113+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.112+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:35:01.230+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.230+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:01.464+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:35:01.604+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.603+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:01.619+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:01.618+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:35:01.641+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.539 seconds
[2025-07-17T21:35:31.754+0000] {processor.py:186} INFO - Started process (PID=505) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:35:31.756+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:35:31.758+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.757+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:35:31.830+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.830+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:31.839+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:35:31.946+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.946+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:31.958+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.958+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:35:31.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.228 seconds
[2025-07-17T21:36:02.074+0000] {processor.py:186} INFO - Started process (PID=641) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:36:02.075+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:36:02.077+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.077+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:36:02.147+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.147+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:02.156+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:36:02.254+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.253+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:02.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:02.264+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:36:02.283+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.217 seconds
[2025-07-17T21:36:32.581+0000] {processor.py:186} INFO - Started process (PID=777) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:36:32.582+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:36:32.584+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.584+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:36:32.659+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.659+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:32.668+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:36:32.774+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.773+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:32.783+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.783+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:36:32.801+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.225 seconds
[2025-07-17T21:37:03.041+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:37:03.042+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:37:03.044+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.043+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:37:03.118+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.118+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:03.127+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:37:03.224+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.224+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:03.236+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:03.235+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:37:03.255+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.220 seconds
[2025-07-17T21:37:33.418+0000] {processor.py:186} INFO - Started process (PID=1049) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:37:33.420+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:37:33.422+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:37:33.496+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.496+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:33.506+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:37:33.608+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.608+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:33.618+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.618+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:37:33.636+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.223 seconds
[2025-07-17T21:38:03.962+0000] {processor.py:186} INFO - Started process (PID=1185) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:38:03.963+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:38:03.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.965+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:38:04.038+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.038+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:04.046+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:38:04.141+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.141+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:04.151+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:04.151+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:38:04.168+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.211 seconds
[2025-07-17T21:38:34.480+0000] {processor.py:186} INFO - Started process (PID=1321) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:38:34.481+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:38:34.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.484+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:38:34.564+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.564+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:34.572+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:38:34.674+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.674+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:34.685+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.685+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:38:34.707+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.232 seconds
[2025-07-17T21:39:05.251+0000] {processor.py:186} INFO - Started process (PID=1455) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:39:05.252+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:39:05.255+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.255+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:39:05.341+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.341+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:05.348+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:39:05.452+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.451+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:05.463+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:05.462+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:39:05.483+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.238 seconds
[2025-07-17T21:39:35.778+0000] {processor.py:186} INFO - Started process (PID=1591) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:39:35.779+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:39:35.781+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.781+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:39:35.858+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.858+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.867+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:39:35.958+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.958+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:35.968+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.968+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:39:35.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.216 seconds
[2025-07-17T21:40:06.751+0000] {processor.py:186} INFO - Started process (PID=1728) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:40:06.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:40:06.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.755+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:40:06.853+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.852+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:06.863+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:40:06.988+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.988+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:07.001+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:07.001+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:40:07.020+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.276 seconds
[2025-07-17T21:40:37.542+0000] {processor.py:186} INFO - Started process (PID=1865) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:40:37.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:40:37.546+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.545+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:40:37.634+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.634+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:37.644+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:40:37.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.757+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:37.769+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.768+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:40:37.791+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.257 seconds
[2025-07-17T21:42:56.620+0000] {processor.py:186} INFO - Started process (PID=231) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:42:56.621+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:42:56.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.624+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:42:56.700+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.699+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:56.708+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:42:56.966+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:56.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.975+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:42:56.994+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.380 seconds
[2025-07-17T21:43:27.559+0000] {processor.py:186} INFO - Started process (PID=367) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:43:27.560+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:43:27.563+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:43:27.648+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.648+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:27.809+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:43:27.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.922+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:27.937+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.937+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:43:27.959+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.406 seconds
[2025-07-17T21:43:58.596+0000] {processor.py:186} INFO - Started process (PID=503) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:43:58.597+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:43:58.600+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.599+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:43:58.698+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.698+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:58.707+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:43:58.833+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.832+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:58.845+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.844+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:43:58.869+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.280 seconds
[2025-07-17T21:44:29.100+0000] {processor.py:186} INFO - Started process (PID=645) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:44:29.101+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:44:29.104+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.104+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:44:29.183+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.182+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:29.191+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:44:29.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.302+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:29.314+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.314+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:44:29.339+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.244 seconds
[2025-07-17T21:44:59.740+0000] {processor.py:186} INFO - Started process (PID=781) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:44:59.741+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:44:59.744+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.744+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:44:59.832+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.831+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:59.842+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:44:59.965+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:59.976+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.976+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:44:59.998+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.263 seconds
[2025-07-17T21:55:24.476+0000] {processor.py:186} INFO - Started process (PID=232) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:55:24.477+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:55:24.480+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.479+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:55:24.548+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.547+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:24.556+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:55:24.824+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.823+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:24.833+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.833+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:55:24.852+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.381 seconds
[2025-07-17T21:55:55.378+0000] {processor.py:186} INFO - Started process (PID=368) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:55:55.379+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:55:55.381+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.381+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:55:55.455+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.454+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:55.620+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:55:55.719+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.719+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:55.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:55.728+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:55:55.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.374 seconds
[2025-07-17T21:56:25.830+0000] {processor.py:186} INFO - Started process (PID=504) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:56:25.831+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:56:25.834+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:56:25.907+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.907+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:25.913+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:56:26.007+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.007+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:26.018+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:26.017+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:56:26.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.212 seconds
[2025-07-17T21:56:56.362+0000] {processor.py:186} INFO - Started process (PID=643) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:56:56.363+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:56:56.365+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.365+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:56:56.430+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.430+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:56.439+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:56:56.535+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.534+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:56.545+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:56.545+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:56:56.565+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.209 seconds
[2025-07-17T21:57:26.732+0000] {processor.py:186} INFO - Started process (PID=779) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:57:26.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:57:26.735+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.735+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:57:26.799+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.799+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:26.807+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:57:26.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.900+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:26.909+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.908+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:57:26.924+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.198 seconds
[2025-07-17T21:57:57.193+0000] {processor.py:186} INFO - Started process (PID=921) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:57:57.194+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:57:57.196+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.196+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:57:57.266+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.265+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:57.273+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:57:57.384+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:57.397+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:57.397+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:57:57.422+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.235 seconds
[2025-07-17T21:58:27.689+0000] {processor.py:186} INFO - Started process (PID=1057) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:58:27.691+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T21:58:27.694+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.694+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:58:27.781+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.781+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:27.788+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T21:58:27.884+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.883+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:27.895+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.895+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T21:58:27.915+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.237 seconds
[2025-07-17T22:00:36.063+0000] {processor.py:186} INFO - Started process (PID=231) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:00:36.064+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:00:36.067+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.066+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:00:36.144+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.144+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:36.153+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:00:36.390+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.390+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:36.401+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:36.401+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:00:36.420+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.362 seconds
[2025-07-17T22:01:06.818+0000] {processor.py:186} INFO - Started process (PID=367) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:01:06.819+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:01:06.821+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.821+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:01:07.051+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.051+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:07.057+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:01:07.172+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.171+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:07.183+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:07.182+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:01:07.205+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.395 seconds
[2025-07-17T22:01:37.412+0000] {processor.py:186} INFO - Started process (PID=505) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:01:37.413+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:01:37.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.415+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:01:37.484+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.484+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:37.493+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:01:37.593+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:37.616+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:37.615+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:01:37.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.243 seconds
[2025-07-17T22:02:07.930+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:02:07.931+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:02:07.934+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.933+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:02:07.997+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.996+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:08.005+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:02:08.101+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:08.101+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:08.111+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:08.110+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:02:08.125+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.201 seconds
[2025-07-17T22:02:38.353+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:02:38.354+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:02:38.356+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.356+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:02:38.422+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.422+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:38.432+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:02:38.530+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.529+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:38.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:38.540+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:02:38.556+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.208 seconds
[2025-07-17T22:03:08.684+0000] {processor.py:186} INFO - Started process (PID=918) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:03:08.685+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:03:08.687+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.686+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:03:08.752+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.752+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:08.760+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:03:08.857+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.857+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:08.868+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.868+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:03:08.884+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.205 seconds
[2025-07-17T22:03:38.974+0000] {processor.py:186} INFO - Started process (PID=1049) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:03:38.975+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:03:38.978+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.977+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:03:39.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.046+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:39.055+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:03:39.151+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.151+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:39.162+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:39.162+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:03:39.182+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.213 seconds
[2025-07-17T22:04:09.952+0000] {processor.py:186} INFO - Started process (PID=1183) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:04:09.953+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:04:09.957+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.956+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:04:10.059+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.059+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:10.065+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:04:10.158+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.158+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:10.167+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:10.166+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:04:10.183+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.237 seconds
[2025-07-17T22:04:40.347+0000] {processor.py:186} INFO - Started process (PID=1321) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:04:40.348+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-17T22:04:40.350+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.349+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:04:40.411+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.410+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:40.420+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-17T22:04:40.523+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.523+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:40.534+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.534+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-17T22:04:40.550+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.209 seconds
