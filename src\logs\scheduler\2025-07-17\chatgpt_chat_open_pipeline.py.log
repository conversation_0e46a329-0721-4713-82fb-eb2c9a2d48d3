[2025-07-17T22:41:31.198+0000] {processor.py:186} INFO - Started process (PID=273) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:41:31.200+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:41:31.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.203+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:41:31.275+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.275+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:31.284+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:41:31.518+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.518+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.530+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.530+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:41:31.553+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.361 seconds
[2025-07-17T22:42:01.892+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:01.893+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:42:01.895+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.895+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:01.970+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.969+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:01.979+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:02.242+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.242+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:02.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.250+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:42:02.266+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.379 seconds
[2025-07-17T22:42:32.955+0000] {processor.py:186} INFO - Started process (PID=533) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:32.956+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:42:32.957+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.957+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:33.167+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.167+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:33.174+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:33.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.277+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:33.288+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.288+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:42:33.305+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.355 seconds
[2025-07-17T22:43:03.732+0000] {processor.py:186} INFO - Started process (PID=666) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:43:03.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:43:03.735+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:43:03.804+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.803+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:03.812+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:43:03.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.906+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:03.916+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:03.916+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:43:03.935+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.208 seconds
[2025-07-17T22:43:34.963+0000] {processor.py:186} INFO - Started process (PID=797) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:43:34.964+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:43:34.967+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:34.966+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:43:35.046+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.046+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:43:35.054+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:43:35.156+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.156+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:43:35.168+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:43:35.168+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:43:35.190+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.234 seconds
[2025-07-17T22:44:05.754+0000] {processor.py:186} INFO - Started process (PID=926) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:44:05.755+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:44:05.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:05.756+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:44:05.833+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:05.832+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:05.842+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:44:05.948+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:05.947+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:05.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:05.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:44:05.981+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.234 seconds
[2025-07-17T22:44:36.741+0000] {processor.py:186} INFO - Started process (PID=1057) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:44:36.743+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:44:36.745+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:36.744+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:44:36.813+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:36.813+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:44:36.821+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:44:36.917+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:36.917+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:44:36.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:44:36.930+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:44:36.948+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.213 seconds
[2025-07-17T22:45:07.137+0000] {processor.py:186} INFO - Started process (PID=1188) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:45:07.139+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:45:07.140+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.140+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:45:07.236+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.236+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:07.245+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:45:07.376+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.375+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:07.392+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:07.391+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:45:07.415+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.284 seconds
[2025-07-17T22:45:37.726+0000] {processor.py:186} INFO - Started process (PID=1319) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:45:37.727+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:45:37.728+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:37.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:45:37.803+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:37.802+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:45:37.809+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:45:37.911+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:37.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:45:37.922+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:45:37.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:45:37.943+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.223 seconds
[2025-07-17T22:46:08.539+0000] {processor.py:186} INFO - Started process (PID=1450) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:46:08.540+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:46:08.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:08.541+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:46:08.633+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:08.633+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:08.642+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:46:08.748+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:08.748+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:08.759+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:08.758+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:46:08.779+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.247 seconds
[2025-07-17T22:46:39.076+0000] {processor.py:186} INFO - Started process (PID=1589) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:46:39.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:46:39.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:39.078+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:46:39.162+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:39.161+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:46:39.171+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:46:39.262+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:39.262+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:46:39.272+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:46:39.272+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:46:39.290+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.219 seconds
[2025-07-17T22:47:09.783+0000] {processor.py:186} INFO - Started process (PID=1718) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:47:09.784+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:47:09.786+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:09.785+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:47:09.860+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:09.860+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:09.869+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:47:09.972+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:09.972+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:09.983+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:09.983+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:47:10.002+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.224 seconds
[2025-07-17T22:47:40.960+0000] {processor.py:186} INFO - Started process (PID=1855) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:47:40.961+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:47:40.962+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:40.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:47:41.041+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:41.041+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:47:41.050+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:47:41.163+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:41.163+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:47:41.177+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:47:41.177+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:47:41.200+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.246 seconds
[2025-07-17T22:48:11.815+0000] {processor.py:186} INFO - Started process (PID=1986) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:48:11.815+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:48:11.817+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:11.816+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:48:11.890+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:11.890+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:11.899+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:48:11.997+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:11.996+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:12.008+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:12.008+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:48:12.027+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.219 seconds
[2025-07-17T22:48:42.835+0000] {processor.py:186} INFO - Started process (PID=2117) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:48:42.836+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:48:42.847+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:42.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:48:43.100+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:43.100+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:48:43.161+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:48:43.578+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:43.578+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:48:43.624+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:48:43.623+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:48:43.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.860 seconds
[2025-07-17T22:49:13.964+0000] {processor.py:186} INFO - Started process (PID=2248) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:49:13.965+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:49:13.967+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:13.967+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:49:14.040+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:14.040+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:14.047+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:49:14.144+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:14.144+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:14.155+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:14.155+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:49:14.176+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.220 seconds
[2025-07-17T22:49:44.359+0000] {processor.py:186} INFO - Started process (PID=2379) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:49:44.360+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:49:44.361+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:44.361+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:49:44.436+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:44.436+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:49:44.443+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:49:44.539+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:44.538+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:49:44.551+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:49:44.551+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:49:44.571+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.218 seconds
[2025-07-17T22:50:15.172+0000] {processor.py:186} INFO - Started process (PID=2510) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:50:15.172+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:50:15.174+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:15.173+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:50:15.240+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:15.240+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:15.249+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:50:15.348+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:15.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:15.361+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:15.361+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:50:15.382+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.218 seconds
[2025-07-17T22:50:45.904+0000] {processor.py:186} INFO - Started process (PID=2641) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:50:45.905+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:50:45.906+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:45.906+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:50:45.975+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:45.974+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:50:45.981+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:50:46.081+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:46.081+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:50:46.094+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:50:46.094+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:50:46.117+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.219 seconds
[2025-07-17T22:51:16.421+0000] {processor.py:186} INFO - Started process (PID=2772) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:51:16.422+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:51:16.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:16.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:51:16.489+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:16.489+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:16.497+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:51:16.583+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:16.582+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:16.594+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:16.594+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:51:16.612+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.196 seconds
[2025-07-17T22:51:47.074+0000] {processor.py:186} INFO - Started process (PID=2903) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:51:47.076+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:51:47.077+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:47.077+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:51:47.162+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:47.161+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:51:47.170+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:51:47.281+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:47.281+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:51:47.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:51:47.293+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:51:47.310+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.246 seconds
[2025-07-17T22:52:17.731+0000] {processor.py:186} INFO - Started process (PID=3034) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:52:17.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:52:17.735+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:17.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:52:17.811+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:17.810+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:17.820+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:52:17.938+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:17.938+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:17.952+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:17.952+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:52:17.970+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.244 seconds
[2025-07-17T22:52:48.436+0000] {processor.py:186} INFO - Started process (PID=3165) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:52:48.436+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:52:48.438+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:48.437+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:52:48.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:48.502+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:52:48.511+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:52:48.619+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:48.618+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:52:48.630+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:52:48.630+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:52:48.653+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.222 seconds
[2025-07-17T22:53:19.095+0000] {processor.py:186} INFO - Started process (PID=3302) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:53:19.095+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:53:19.097+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:19.097+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:53:19.171+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:19.171+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:19.179+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:53:19.281+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:19.281+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:19.293+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:19.293+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:53:19.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.227 seconds
[2025-07-17T22:53:50.027+0000] {processor.py:186} INFO - Started process (PID=3433) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:53:50.028+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:53:50.029+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:50.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:53:50.100+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:50.099+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:53:50.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:53:50.223+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:50.222+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:53:50.237+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:53:50.237+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:53:50.259+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.238 seconds
[2025-07-17T22:54:20.920+0000] {processor.py:186} INFO - Started process (PID=3570) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:54:20.921+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:54:20.922+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:20.922+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:54:20.989+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:20.989+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:54:20.996+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:54:21.096+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:21.096+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:54:21.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:54:21.107+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:54:21.129+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.214 seconds
