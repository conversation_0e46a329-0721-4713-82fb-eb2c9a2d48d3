[2025-07-17T21:34:29.060+0000] {processor.py:186} INFO - Started process (PID=211) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:34:29.061+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:34:29.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.064+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:34:29.160+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.160+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:29.167+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:34:29.282+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.281+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:29.441+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.441+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:34:29.462+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.410 seconds
[2025-07-17T21:35:00.184+0000] {processor.py:186} INFO - Started process (PID=349) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:00.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:35:00.188+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.187+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:00.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.264+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:00.273+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:00.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.502+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:00.511+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.511+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:35:00.528+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.350 seconds
[2025-07-17T21:35:31.099+0000] {processor.py:186} INFO - Started process (PID=485) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:31.100+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:35:31.103+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:31.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.193+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:31.201+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:31.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.306+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:31.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.318+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:35:31.341+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.248 seconds
[2025-07-17T21:36:01.459+0000] {processor.py:186} INFO - Started process (PID=621) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:01.460+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:36:01.463+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.462+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:01.532+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.532+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.540+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:01.645+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.645+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:01.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.655+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:36:01.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.222 seconds
[2025-07-17T21:36:31.754+0000] {processor.py:186} INFO - Started process (PID=757) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:31.755+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:36:31.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.757+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:31.822+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.822+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:31.832+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:31.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.923+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.933+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:36:31.951+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.203 seconds
[2025-07-17T21:37:02.183+0000] {processor.py:186} INFO - Started process (PID=893) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:02.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:37:02.188+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.187+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:02.262+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.261+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:02.269+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:02.366+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:02.377+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.376+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:37:02.396+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.219 seconds
[2025-07-17T21:37:32.825+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:32.826+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:37:32.828+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.827+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:32.902+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.902+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:32.909+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:33.003+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.003+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:33.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.014+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:37:33.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.216 seconds
[2025-07-17T21:38:03.322+0000] {processor.py:186} INFO - Started process (PID=1165) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:03.324+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:38:03.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.326+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:03.400+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.400+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:03.408+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:03.505+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.505+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:03.515+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.514+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:38:03.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.219 seconds
[2025-07-17T21:38:33.833+0000] {processor.py:186} INFO - Started process (PID=1301) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:33.834+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:38:33.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.836+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:33.917+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.917+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:33.926+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:34.029+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.029+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:34.040+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.040+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:38:34.061+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.234 seconds
[2025-07-17T21:39:04.717+0000] {processor.py:186} INFO - Started process (PID=1437) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:04.718+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:39:04.720+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.720+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:04.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.792+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:04.800+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:04.896+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.896+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:04.907+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.906+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:39:04.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.216 seconds
[2025-07-17T21:39:35.285+0000] {processor.py:186} INFO - Started process (PID=1573) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:35.286+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:39:35.288+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.288+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:35.355+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.355+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.364+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:35.460+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.460+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:35.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.471+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:39:35.491+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.212 seconds
[2025-07-17T21:40:06.029+0000] {processor.py:186} INFO - Started process (PID=1709) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:40:06.031+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:40:06.036+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:40:06.140+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.139+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:06.158+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:40:06.292+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.292+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:06.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.302+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:40:06.321+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.300 seconds
[2025-07-17T21:40:36.870+0000] {processor.py:186} INFO - Started process (PID=1845) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:40:36.870+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:40:36.873+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.873+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:40:36.956+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:36.956+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:36.964+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:40:37.068+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.068+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:37.078+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.078+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:40:37.099+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.236 seconds
[2025-07-17T21:41:08.236+0000] {processor.py:186} INFO - Started process (PID=2037) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:41:08.239+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:41:08.245+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:08.245+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:41:08.413+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:08.413+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:41:09.753+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:09.753+0000] {process_utils.py:266} INFO - Waiting up to 5 seconds for processes to exit...
[2025-07-17T21:41:09.980+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:41:09.980+0000] {process_utils.py:266} INFO - Waiting up to 5 seconds for processes to exit...
[2025-07-17T21:42:55.865+0000] {processor.py:186} INFO - Started process (PID=211) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:42:55.867+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:42:55.869+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.869+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:42:55.956+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:55.955+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:42:55.965+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:42:56.079+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.079+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:42:56.226+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.226+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:42:56.246+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.389 seconds
[2025-07-17T21:43:26.724+0000] {processor.py:186} INFO - Started process (PID=347) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:43:26.725+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:43:26.729+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:43:26.812+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:26.811+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:26.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:43:27.086+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:27.097+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.096+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:43:27.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.398 seconds
[2025-07-17T21:43:57.925+0000] {processor.py:186} INFO - Started process (PID=483) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:43:57.927+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:43:57.930+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:57.930+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:43:58.013+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.013+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:43:58.021+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:43:58.128+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.128+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:43:58.143+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.143+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:43:58.165+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.247 seconds
[2025-07-17T21:44:28.445+0000] {processor.py:186} INFO - Started process (PID=619) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:44:28.446+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:44:28.449+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.448+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:44:28.526+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.526+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:28.537+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:44:28.659+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.659+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:28.671+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:28.671+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:44:28.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.253 seconds
[2025-07-17T21:44:59.114+0000] {processor.py:186} INFO - Started process (PID=763) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:44:59.115+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:44:59.119+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:44:59.209+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.209+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:44:59.219+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:44:59.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.330+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:44:59.344+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.343+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:44:59.366+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.260 seconds
[2025-07-17T21:55:23.815+0000] {processor.py:186} INFO - Started process (PID=212) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:55:23.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:55:23.819+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.818+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:55:23.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:23.900+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:23.907+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:55:24.030+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.030+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:24.192+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.191+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:55:24.211+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.402 seconds
[2025-07-17T21:55:54.574+0000] {processor.py:186} INFO - Started process (PID=350) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:55:54.576+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:55:54.580+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.579+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:55:54.651+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.650+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:55:54.658+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:55:54.913+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.913+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:55:54.922+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:55:54.940+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.372 seconds
[2025-07-17T21:56:25.196+0000] {processor.py:186} INFO - Started process (PID=486) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:56:25.197+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:56:25.200+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.199+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:56:25.276+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.276+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:25.284+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:56:25.373+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.373+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:25.383+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.383+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:56:25.402+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.212 seconds
[2025-07-17T21:56:55.488+0000] {processor.py:186} INFO - Started process (PID=623) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:56:55.488+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:56:55.491+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.491+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:56:55.562+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.562+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:56:55.569+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:56:55.665+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.665+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:56:55.675+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.675+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:56:55.696+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.214 seconds
[2025-07-17T21:57:25.944+0000] {processor.py:186} INFO - Started process (PID=759) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:57:25.945+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:57:25.947+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:25.947+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:57:26.012+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.012+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:26.019+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:57:26.106+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.105+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:26.115+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.115+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:57:26.134+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.195 seconds
[2025-07-17T21:57:56.346+0000] {processor.py:186} INFO - Started process (PID=895) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:57:56.347+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:57:56.350+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.349+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:57:56.423+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.422+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:57:56.431+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:57:56.527+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.527+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:57:56.538+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.538+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:57:56.558+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.219 seconds
[2025-07-17T21:58:26.819+0000] {processor.py:186} INFO - Started process (PID=1031) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:58:26.820+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:58:26.822+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.822+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:58:26.892+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:26.892+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:58:26.901+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:58:27.006+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.005+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:58:27.016+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.015+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:58:27.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.221 seconds
[2025-07-17T22:00:35.343+0000] {processor.py:186} INFO - Started process (PID=211) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:00:35.344+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:00:35.346+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.346+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:00:35.420+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.420+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:00:35.428+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:00:35.540+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.540+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:00:35.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.715+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:00:35.739+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.402 seconds
[2025-07-17T22:01:05.973+0000] {processor.py:186} INFO - Started process (PID=347) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:01:05.974+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:01:05.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:05.976+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:01:06.089+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.089+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:06.097+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:01:06.390+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:06.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.408+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:01:06.433+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.468 seconds
[2025-07-17T22:01:36.666+0000] {processor.py:186} INFO - Started process (PID=490) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:01:36.667+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:01:36.670+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:36.669+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:01:36.747+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:36.747+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:01:36.755+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:01:36.855+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:36.855+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:01:36.866+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:36.866+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:01:36.882+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.222 seconds
[2025-07-17T22:02:07.151+0000] {processor.py:186} INFO - Started process (PID=619) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:02:07.151+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:02:07.153+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.153+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:02:07.221+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.221+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:07.230+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:02:07.324+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.324+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:07.334+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:02:07.352+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.207 seconds
[2025-07-17T22:02:37.568+0000] {processor.py:186} INFO - Started process (PID=755) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:02:37.569+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:02:37.571+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.571+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:02:37.637+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.636+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:02:37.646+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:02:37.746+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.745+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:02:37.756+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.755+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:02:37.773+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.210 seconds
[2025-07-17T22:03:07.890+0000] {processor.py:186} INFO - Started process (PID=891) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:03:07.891+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:03:07.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:07.893+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:03:07.959+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:07.959+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:07.965+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:03:08.060+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:08.070+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.069+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:03:08.091+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.206 seconds
[2025-07-17T22:03:38.329+0000] {processor.py:186} INFO - Started process (PID=1027) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:03:38.330+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:03:38.333+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.333+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:03:38.415+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.415+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:03:38.422+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:03:38.530+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.530+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:03:38.541+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.541+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:03:38.565+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.244 seconds
[2025-07-17T22:04:09.423+0000] {processor.py:186} INFO - Started process (PID=1165) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:04:09.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:04:09.426+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:04:09.493+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.493+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:09.502+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:04:09.593+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.593+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:09.603+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.603+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:04:09.623+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.205 seconds
[2025-07-17T22:04:39.799+0000] {processor.py:186} INFO - Started process (PID=1301) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:04:39.800+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:04:39.802+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.802+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:04:39.870+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.870+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:04:39.877+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:04:39.967+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.966+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:04:39.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:39.977+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:04:39.997+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.204 seconds
[2025-07-17T22:05:10.217+0000] {processor.py:186} INFO - Started process (PID=1437) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:05:10.218+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:05:10.221+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:10.220+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:05:10.287+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:10.287+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:10.296+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:05:10.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:10.388+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:10.398+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:10.398+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:05:10.416+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.205 seconds
[2025-07-17T22:05:40.778+0000] {processor.py:186} INFO - Started process (PID=1573) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:05:40.779+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:05:40.782+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.782+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:05:40.850+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.849+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:05:40.856+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:05:40.951+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.951+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:05:40.961+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:05:40.979+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.208 seconds
[2025-07-17T22:06:11.262+0000] {processor.py:186} INFO - Started process (PID=1714) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:06:11.262+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:06:11.265+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:11.264+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:06:11.332+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:11.332+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:06:11.340+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:06:11.427+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:11.426+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:06:11.439+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:11.439+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:06:11.459+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.203 seconds
[2025-07-17T22:07:26.897+0000] {processor.py:186} INFO - Started process (PID=205) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:07:26.898+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:07:26.900+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:26.900+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:07:26.977+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:26.976+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:26.983+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:07:27.086+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:27.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:27.249+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:27.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:07:27.266+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.375 seconds
[2025-07-17T22:07:58.516+0000] {processor.py:186} INFO - Started process (PID=349) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:07:58.517+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:07:58.519+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:58.519+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:07:58.593+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:58.593+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:07:58.600+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:07:58.811+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:58.811+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:07:58.820+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:58.820+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:07:58.837+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.327 seconds
[2025-07-17T22:08:29.256+0000] {processor.py:186} INFO - Started process (PID=485) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:08:29.257+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:08:29.260+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:29.259+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:08:29.330+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:29.329+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:29.337+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:08:29.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:29.443+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:29.455+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:29.454+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:08:29.476+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.227 seconds
[2025-07-17T22:08:59.697+0000] {processor.py:186} INFO - Started process (PID=621) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:08:59.701+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:08:59.704+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.704+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:08:59.783+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.783+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:08:59.792+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:08:59.903+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.903+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:08:59.913+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.913+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:08:59.931+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.242 seconds
[2025-07-17T22:09:30.643+0000] {processor.py:186} INFO - Started process (PID=757) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:09:30.644+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:09:30.646+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.646+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:09:30.715+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.714+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:09:30.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:09:30.816+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.816+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:09:30.827+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.826+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:09:30.844+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.206 seconds
[2025-07-17T22:10:01.007+0000] {processor.py:186} INFO - Started process (PID=891) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:10:01.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:10:01.010+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.010+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:10:01.080+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.079+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:01.088+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:10:01.190+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.190+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:01.206+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.206+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:10:01.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.225 seconds
[2025-07-17T22:10:31.521+0000] {processor.py:186} INFO - Started process (PID=1027) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:10:31.522+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:10:31.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.524+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:10:31.590+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.590+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:10:31.598+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:10:31.695+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.695+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:10:31.707+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.707+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:10:31.725+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.210 seconds
[2025-07-17T22:11:02.418+0000] {processor.py:186} INFO - Started process (PID=1165) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:11:02.419+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:11:02.422+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.421+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:11:02.497+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.497+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:02.504+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:11:02.611+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.611+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:02.623+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.623+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:11:02.645+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.232 seconds
[2025-07-17T22:11:33.456+0000] {processor.py:186} INFO - Started process (PID=1301) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:11:33.457+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:11:33.460+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.460+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:11:33.531+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.531+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:11:33.539+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:11:33.632+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.632+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:11:33.642+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.642+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:11:33.657+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.207 seconds
[2025-07-17T22:12:04.422+0000] {processor.py:186} INFO - Started process (PID=1437) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:12:04.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:12:04.424+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.424+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:12:04.501+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.501+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:04.510+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:12:04.597+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.596+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:04.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:04.606+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:12:04.623+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.206 seconds
[2025-07-17T22:12:35.560+0000] {processor.py:186} INFO - Started process (PID=1568) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:12:35.562+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:12:35.564+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:35.564+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:12:35.660+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:35.660+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:12:35.669+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:12:35.759+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:35.759+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:12:35.769+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:12:35.769+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:12:35.786+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.233 seconds
