[2025-07-17T22:41:31.198+0000] {processor.py:186} INFO - Started process (PID=273) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:41:31.200+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:41:31.203+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.203+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:41:31.275+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.275+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:41:31.284+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:41:31.518+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.518+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:41:31.530+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:41:31.530+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:41:31.553+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.361 seconds
[2025-07-17T22:42:01.892+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:01.893+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:42:01.895+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.895+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:01.970+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:01.969+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:01.979+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:02.242+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.242+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:02.251+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:02.250+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:42:02.266+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.379 seconds
[2025-07-17T22:42:32.955+0000] {processor.py:186} INFO - Started process (PID=533) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:32.956+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T22:42:32.957+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:32.957+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:33.167+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.167+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T22:42:33.174+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T22:42:33.278+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.277+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T22:42:33.288+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:42:33.288+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T22:42:33.305+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.355 seconds
