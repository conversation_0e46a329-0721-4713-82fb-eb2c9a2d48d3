[2025-07-17T21:34:29.060+0000] {processor.py:186} INFO - Started process (PID=211) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:34:29.061+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:34:29.064+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.064+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:34:29.160+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.160+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:34:29.167+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:34:29.282+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.281+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:34:29.441+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.441+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:34:29.462+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.410 seconds
[2025-07-17T21:35:00.184+0000] {processor.py:186} INFO - Started process (PID=349) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:00.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:35:00.188+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.187+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:00.264+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.264+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:00.273+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:00.502+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.502+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:00.511+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.511+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:35:00.528+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.350 seconds
[2025-07-17T21:35:31.099+0000] {processor.py:186} INFO - Started process (PID=485) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:31.100+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:35:31.103+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.103+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:31.193+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.193+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:35:31.201+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:35:31.306+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.306+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:35:31.318+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.318+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:35:31.341+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.248 seconds
[2025-07-17T21:36:01.459+0000] {processor.py:186} INFO - Started process (PID=621) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:01.460+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:36:01.463+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.462+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:01.532+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.532+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:01.540+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:01.645+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.645+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:01.656+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.655+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:36:01.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.222 seconds
[2025-07-17T21:36:31.754+0000] {processor.py:186} INFO - Started process (PID=757) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:31.755+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:36:31.757+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.757+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:31.822+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.822+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:36:31.832+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:36:31.923+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.923+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:36:31.933+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:31.933+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:36:31.951+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.203 seconds
[2025-07-17T21:37:02.183+0000] {processor.py:186} INFO - Started process (PID=893) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:02.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:37:02.188+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.187+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:02.262+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.261+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:02.269+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:02.366+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:02.377+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.376+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:37:02.396+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.219 seconds
[2025-07-17T21:37:32.825+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:32.826+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:37:32.828+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.827+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:32.902+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:32.902+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:37:32.909+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:37:33.003+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.003+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:37:33.015+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.014+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:37:33.035+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.216 seconds
[2025-07-17T21:38:03.322+0000] {processor.py:186} INFO - Started process (PID=1165) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:03.324+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:38:03.326+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.326+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:03.400+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.400+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:03.408+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:03.505+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.505+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:03.515+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.514+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:38:03.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.219 seconds
[2025-07-17T21:38:33.833+0000] {processor.py:186} INFO - Started process (PID=1301) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:33.834+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:38:33.836+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.836+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:33.917+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:33.917+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:38:33.926+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:38:34.029+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.029+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:38:34.040+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.040+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:38:34.061+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.234 seconds
[2025-07-17T21:39:04.717+0000] {processor.py:186} INFO - Started process (PID=1437) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:04.718+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:39:04.720+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.720+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:04.792+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.792+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:04.800+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:04.896+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.896+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:04.907+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.906+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:39:04.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.216 seconds
[2025-07-17T21:39:35.285+0000] {processor.py:186} INFO - Started process (PID=1573) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:35.286+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:39:35.288+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.288+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:35.355+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.355+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:39:35.364+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:39:35.460+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.460+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:39:35.471+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.471+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:39:35.491+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.212 seconds
[2025-07-17T21:40:06.029+0000] {processor.py:186} INFO - Started process (PID=1709) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:40:06.031+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-17T21:40:06.036+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:40:06.140+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.139+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-17T21:40:06.158+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-17T21:40:06.292+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.292+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-17T21:40:06.303+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.302+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-17T21:40:06.321+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.300 seconds
