import asyncio
import httpx
from fastapi import HTTPException

AIRFLOW_API_URL = "http://airflow-webserver:8080/api/v1"
AIRFLOW_USERNAME = "airflow"
AIRFLOW_PASSWORD = "airflow"

# Можно использовать глобальный клиент или создавать новый при каждом запросе.
# Здесь сделаем глобальный клиент для упрощения.
async_client = httpx.AsyncClient(timeout=30.0, auth=(AIRFLOW_USERNAME, AIRFLOW_PASSWORD))

async def run_dag_and_get_xcom(dag_id: str, conf: dict, xcom_task_id: str, xcom_key: str = "return_value") -> str:
    # Запуск DAG
    dag_run_response = await async_client.post(
        f"{AIRFLOW_API_URL}/dags/{dag_id}/dagRuns",
        json={"conf": conf}
    )

    if dag_run_response.status_code != 200:
        raise HTTPException(
            status_code=dag_run_response.status_code,
            detail=f"Ошибка запуска DAG {dag_id}: {dag_run_response.text}"
        )

    dag_run_id = dag_run_response.json().get("dag_run_id")
    if not dag_run_id:
        raise HTTPException(
            status_code=500,
            detail=f"Не удалось получить dag_run_id для DAG {dag_id}"
        )

    # Ожидание завершения DAG
    while True:
        dag_status_response = await async_client.get(
            f"{AIRFLOW_API_URL}/dags/{dag_id}/dagRuns/{dag_run_id}"
        )

        if dag_status_response.status_code != 200:
            raise HTTPException(
                status_code=dag_status_response.status_code,
                detail=f"Ошибка проверки статуса DAG {dag_id}: {dag_status_response.text}"
            )

        dag_status = dag_status_response.json().get("state")
        if dag_status == "success":
            break
        elif dag_status in {"failed", "error"}:
            raise HTTPException(
                status_code=500,
                detail=f"DAG {dag_id} завершился с ошибкой. Статус: {dag_status}"
            )

        await asyncio.sleep(0.5)

    # Получаем результат из XCom
    xcom_response = await async_client.get(
        f"{AIRFLOW_API_URL}/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{xcom_task_id}/xcomEntries/{xcom_key}"
    )

    if xcom_response.status_code != 200:
        raise HTTPException(
            status_code=xcom_response.status_code,
            detail=f"Ошибка получения XCom из DAG {dag_id}: {xcom_response.text}"
        )

    xcom_data = xcom_response.json()
    return xcom_data.get('value', '')