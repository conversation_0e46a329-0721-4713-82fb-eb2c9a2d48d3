from __future__ import annotations

import asyncio
import configparser
import json
import re
from datetime import datetime, time as dt_time, timedelta, timezone
from typing import Any

import httpx
import redis
from airflow.decorators import dag, task

# --- Конфигурация ---
CONFIG_FILE_PATH = "/opt/airflow/config/config.ini"
PROMPTS_FILE_PATH = "/opt/airflow/config/prompts.ini"
TOKEN_CONFIG_FILE_PATH = "/opt/airflow/pipe/config.ini" # Правильный путь к конфигу с токенами

config = configparser.ConfigParser()
config.read(CONFIG_FILE_PATH)

# Загружаем конфиг с токенами отдельно
token_config = configparser.ConfigParser()
token_config.read(TOKEN_CONFIG_FILE_PATH)

# Загружаем промпты
prompts = configparser.ConfigParser()
prompts.read(PROMPTS_FILE_PATH)

BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
REDIS_HOST = config.get('REDIS', 'HOST', fallback='redis')
REDIS_PORT = config.getint('REDIS', 'PORT', fallback=6379)
API_TOKEN_GPT = token_config.get('TOKENS', 'API_TOKEN_GPT')
API_BASE_URL = "https://root.biome-dev-api.work/api"

CALORIES_ACHIEVEMENTS = prompts.get('PROMPTS', 'CALORIES_ACHIEVEMENTS')
CALORIES_BIG_TREND = prompts.get('PROMPTS', 'CALORIES_BIG_TREND')
CALORIES_SMALL_TREND = prompts.get('PROMPTS', 'CALORIES_SMALL_TREND')

# --- Клиенты ---
# Настройка прокси и повторных попыток для OpenAI
# В идеале, URL прокси следует вынести в config.ini
PROXY_URL = "http://**************:8888"
proxies = {
    "http://": PROXY_URL,
    "https://": PROXY_URL,
}
# Транспорт с 2 повторными попытками при сбоях сети или ошибках 5xx
transport = httpx.AsyncHTTPTransport(retries=2)

redis_pool = redis.ConnectionPool(host=REDIS_HOST, port=REDIS_PORT, db=0, decode_responses=True)
# Клиент для внутреннего API, без прокси
biome_api_client = httpx.AsyncClient(timeout=90.0)
# Клиент для OpenAI, с прокси и ретраями
openai_api_client = httpx.AsyncClient(timeout=90.0, proxies=proxies, transport=transport)

# --- Вспомогательные функции ---

async def call_openai(prompt: str) -> str:
    """Выполняет вызов к API OpenAI через прокси с повторными попытками."""
    headers = {
        "Authorization": f"Bearer {API_TOKEN_GPT}",
        "Content-Type": "application/json"
    }
    data = {
        "model": "gpt-4o",
        "temperature": 0.5,  # Как в вашем примере
        "messages": [{"role": "system", "content": prompt}]
    }
    try:
        # Используем клиент, настроенный для OpenAI
        response = await openai_api_client.post("https://api.openai.com/v1/chat/completions", json=data, headers=headers)
        response.raise_for_status()  # Вызовет исключение для статусов 4xx/5xx
        content = response.json()["choices"][0]["message"]["content"]

        # Очистка ответа для получения чистого JSON
        start = content.find('{')
        end = content.rfind('}')
        if start != -1 and end != -1:
            return content[start:end + 1].strip()
        return content  # Возвращаем как есть, если JSON не найден
    except (httpx.RequestError, httpx.HTTPStatusError) as e:
        print(f"OpenAI API request failed: {e}")
        # Перевыбрасываем исключение, чтобы оно было поймано в `process_all_users`
        raise
    except (KeyError, IndexError, json.JSONDecodeError) as e:
        print(f"Failed to parse OpenAI response: {e}")
        # Перевыбрасываем исключение
        raise

def get_redis_client():
    return redis.Redis(connection_pool=redis_pool)

async def get_last_list_item(key: str) -> str | None:
    # Эта функция теперь асинхронна, но использует синхронную библиотеку.
    # Для настоящей асинхронности нужен aioredis, но для Airflow это может быть избыточно.
    # Запускаем синхронный код в асинхронном исполнителе.
    loop = asyncio.get_running_loop()
    r = get_redis_client()
    items = await loop.run_in_executor(None, r.lrange, key, -1, -1)
    return items[0] if items else None

async def wait_for_metrics(user_id: int): return await get_last_list_item(f"user:metrics:{user_id}")
async def get_goals(user_id: int): return await get_last_list_item(f"user:goals:{user_id}")
async def get_timezone(user_id: int): return await get_last_list_item(f"user:timezone:{user_id}")

async def get_user_foods(user_id: int):
    headers = {"Authorization": f"Bearer {BEARER_TOKEN}"}
    params = {
        "filters[user][id][$eq]": user_id,
        "pagination[page]": 1, "pagination[pageSize]": 1000, "sort": "createdAt:desc"
    }
    try:
        response = await biome_api_client.get(f"{API_BASE_URL}/foods", headers=headers, params=params)
        response.raise_for_status()
        items = response.json().get("data", [])
        foods = []
        for entry in items:
            record = entry.get("attributes", entry)
            # Убедимся, что у записи есть дата для сортировки и анализа
            record['addedAt'] = record.get('addedAt') or record.get('createdAt', '')
            foods.append(record)
        return foods
    except httpx.RequestError as e:
        print(f"Failed to fetch user foods for user {user_id}: {e}")
        return []

async def upload_trend(user_id: int, trend_data: dict):
    headers = {"Authorization": f"Bearer {BEARER_TOKEN}"}
    payload = {"data": {"user": user_id, **trend_data}}
    try:
        response = await biome_api_client.post(f"{API_BASE_URL}/trends", headers=headers, json=payload)
        response.raise_for_status()
        print(f"Trend for user {user_id} uploaded successfully.")
    except httpx.RequestError as e:
        print(f"Failed to upload trend for user {user_id}: {e}")

async def add_system_message(user_id: int, message: str):
    loop = asyncio.get_running_loop()
    r = get_redis_client()
    key = f"system:messages-general::{user_id}" # Упрощенный ключ для общего чата
    await loop.run_in_executor(None, r.rpush, key, message)


# --- Основной DAG ---
default_args = {'owner': 'Nikita Litvinov', 'retries': 1, 'retry_delay': timedelta(minutes=5), 'start_date': datetime(2023, 1, 1)}

@dag(
    dag_id='set_calories_trends_pipeline',
    default_args=default_args,
    description='Hourly check for users to generate daily calorie trends.',
    schedule_interval="0 * * * *",
    catchup=False,
    tags=['calories', 'trends', 'daily'],
)
def set_calories_trends_dag():
    @task
    def get_eligible_users() -> list[int]:
        r = get_redis_client()
        user_ids = []
        now_utc = datetime.now(timezone.utc)
        for key in r.scan_iter("user:timezone:*"):
            try:
                user_id = int(key.split(":")[-1])
                timezone_values = r.lrange(key, -1, -1)
                if not timezone_values: continue
                
                offset_hours = int(timezone_values[0])
                user_tz = timezone(timedelta(hours=offset_hours))
                user_now = now_utc.astimezone(user_tz)

                if user_now.hour == 23:
                    user_ids.append(user_id)
            except (ValueError, IndexError, TypeError) as e:
                print(f"Could not process key {key}: {e}")
                continue
        print(f"Found {len(user_ids)} eligible users: {user_ids}")
        return user_ids

    @task
    def generate_trends_for_users(user_ids: list[int]):
        if not user_ids:
            print("No users to process.")
            return
        asyncio.run(process_all_users(user_ids))

    # --- Логика генерации ---

    async def process_all_users(user_ids: list[int]):
        tasks = [generate_trends_for_user(uid) for uid in user_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"Failed to process user {user_ids[i]}: {result}")

    async def generate_trends_for_user(user_id: int):
        print(f"Starting trend generation for user {user_id}...")
        metrics_str, goals_str, foods = await asyncio.gather(
            wait_for_metrics(user_id), get_goals(user_id), get_user_foods(user_id)
        )
        if not all([metrics_str, goals_str]):
            raise ValueError(f"Missing metrics or goals for user {user_id}")

        metrics = json.loads(metrics_str)
        goals = json.loads(goals_str)

        total_calories_today, calories_by_day, foods_today = calculate_calories(foods)
        average_calories_last_week = round(sum(calories_by_day.values()) / max(len(calories_by_day), 1), 1)
        target_calories = calculate_target_calories(metrics, goals)
        
        insufficient_daily = (total_calories_today == 0.0)
        insufficient_weekly = (len(calories_by_day) < 4)
        insufficient_data_response = json.dumps({"title": "Слишком мало данных", "description": "Добавьте больше записей о питании для анализа."})

        kz_profile = format_health_profile(metrics)
        prompt_data = {
            "total_calories_today": total_calories_today, "average_calories_last_week": average_calories_last_week,
            "target_calories": target_calories, "goal": goals.get('goal', 'Не указана'), "kz": kz_profile,
            "foods_today": foods_today
        }
        
        async def get_trend(base_prompt, addition):
            if insufficient_daily or insufficient_weekly: return insufficient_data_response
            return await call_openai(addition + ' ' + base_prompt)

        small_trend_raw, big_trend_raw, achievements_raw = await asyncio.gather(
            get_trend(CALORIES_SMALL_TREND, get_small_trend_prompt(**prompt_data)),
            get_trend(CALORIES_BIG_TREND, get_big_trend_prompt(**prompt_data)),
            get_trend(CALORIES_ACHIEVEMENTS, get_achievements_prompt(**prompt_data))
        )
        
        small_trend = json.loads(small_trend_raw.replace("'", '"'))
        big_trend = json.loads(big_trend_raw.replace("'", '"'))
        achievements = json.loads(achievements_raw.replace("'", '"'))

        trend_content = {"smallTrend": small_trend, "bigTrend": big_trend, "achievements": achievements}
        timezone_str = await get_timezone(user_id)
        user_tz = timezone(timedelta(hours=int(timezone_str)))
        trend_data = {
            "content": trend_content,
            "type": "calories",
            "addedAt": datetime.now(user_tz).isoformat()
        }

        await upload_trend(user_id, trend_data)
        summary_message = (f"Малый тренд:\n{small_trend['title']}\n{small_trend['description']}\n\n"
                           f"Большой тренд:\n{big_trend['title']}\n{big_trend['description']}\n\n"
                           f"Вывод и мотивация:\n{achievements['title']}\n{achievements['description']}")
        await add_system_message(user_id, summary_message)
        print(f"Successfully generated and saved trends for user {user_id}.")

    # --- Вспомогательные функции для расчетов и форматирования (полная версия) ---

    def parse_calories(val: Any) -> float:
        if val is None: return 0.0
        if isinstance(val, (int, float)): return float(val)
        if isinstance(val, str):
            nums = re.findall(r"\d+(?:[.,]\d+)?", val.replace(",", "."))
            return sum(float(n) for n in nums) / len(nums) if nums else 0.0
        return 0.0

    def calculate_calories(foods: list[dict]):
        now_utc = datetime.now(timezone.utc)
        start_of_day = datetime.combine(now_utc.date(), dt_time.min, tzinfo=timezone.utc)
        last_7d_cutoff = now_utc - timedelta(days=7)
        foods_today, calories_by_day = [], {}
        for f in foods:
            raw_ts = f.get("addedAt", "")
            if not raw_ts: continue
            try:
                dt = datetime.fromisoformat(raw_ts.replace("Z", "+00:00")).astimezone(timezone.utc)
            except ValueError: continue
            if start_of_day <= dt < start_of_day + timedelta(days=1): foods_today.append(f)
            if dt >= last_7d_cutoff:
                calories_by_day.setdefault(dt.date(), 0)
                calories_by_day[dt.date()] += parse_calories(f.get("calories"))
        return sum(parse_calories(f.get("calories")) for f in foods_today), calories_by_day, foods_today

    def calculate_target_calories(metrics: dict, goals: dict) -> float:
        pi = metrics.get('personal_information', {})
        try:
            weight, height, dob_str, gender = float(pi.get('weight',0)), float(pi.get('height',0)), pi.get('date_of_birth'), pi.get('gender')
            if not all([weight, height, dob_str, gender]): return 0.0
            age = (datetime.today().date() - datetime.strptime(dob_str, "%d.%m.%Y").date()).days // 365
            base = (10 * weight) + (6.25 * height) - (5 * age)
            return (base + 5 if gender == "М" else base - 161) * float(goals.get('goal_coefficient', 1.0))
        except (ValueError, TypeError): return 0.0

    def format_health_profile(user_metrics: dict) -> str:
        fields_order = [
            "name", "gender", "date_of_birth", "height", "weight", "activity_level",
            "food_level", "smoking", "alcohol", "sleep_recovery", "stress_level",
            "chronic_conditions", "allergies", "sports", "regular_medicine",
            "genetic_conditions", "sleep_quality", "injuries", "diet_balance",
            "calorie_intake", "sleep_schedule", "work_schedule", "blood_pressure",
            "preferred_dishes", "diet_schedule", "food_intolerances",
            "blood_analysis", "biochemical_analysis", "urine_analysis", "lipid_profile",
            "glucose_tolerance_test", "thyroid_test", "glycated_hemoglobin",
            "coagulogram", "inflammatory_markers"
        ]
        addition_parts = []
        
        for field in fields_order:
            field_display, field_value = None, None
            for section in user_metrics.values():
                if isinstance(section, dict) and field in section:
                    field_value = section[field]
                    field_display = section.get(f"{field}_field_name", field.replace('_', ' ').capitalize())
                    break
            
            if not field_value or (isinstance(field_value, str) and not field_value.strip()):
                field_value = "Не заполнено"
            elif isinstance(field_value, list):
                field_value = ", ".join(map(str, field_value)) if field_value else "Не заполнено"
            
            if field_display:
                addition_parts.append(f" - {field_display}: {field_value}")
        return "\n".join(addition_parts)

    def get_small_trend_prompt(**kwargs) -> str:
        return f'''
                    Ты владеешь следующей информацией:
                    total_calories_today: {kwargs["total_calories_today"]} – сколько калорий съел пользователь сегодня
                    average_calories_last_week: {kwargs["average_calories_last_week"]} – средний дневной калораж за последние 7 дней
                    target_calories: {kwargs["target_calories"]} - целевая дневная норма калорий

                    ФОРМАТ ВЫВОДА:
                    Анализ тренда калорий  
                    [одно короткое предложение — 12–20 слов, дружелюбное и по делу]

                    ТВОЯ ЛОГИКА:
                    Сравни total_calories_today с average_calories_last_week (тренд).
                    Сравни total_calories_today с target_calories (норма).
                    На основе этого выбери один из следующих типов фразы:
                    Калорийность значительно выше среднего (+15%+):
                    – Предположи насыщенность, активность или праздничный день.
                    – Избегай оценки — поддержи.

                    Калорийность значительно ниже среднего (−15%+):
                    – Отметь лёгкость дня или осознанный подход.
                    – Мягко предложи следить за насыщением.

                    Калорийность в пределах ±15% от среднего и от нормы:
                    – Отметь стабильность, сбалансированность, удержание привычки.
                    Если калорийность превышает цель более чем на 40%:
                    – Аккуратно подчеркни переедание, не стыдя. Предложи завтра вернуться к цели.

                    СТИЛЬ:
                    – Нейтрально-дружелюбный, без приписок "молодец"
                    – Тон — заботливый, с уважением к выбору пользователя
                    – Не больше одного предложения

                    ПРИМЕР ВХОДА:
                    {{
                      "total_calories_today": 2900,
                      "average_calories_last_week": 1950,
                      "target_calories": 2000
                    }}
                    
                    Выводи тренды СТРОГО в формате JSON в СТРОГО таком формате и СТРОГО с такими данными, самостоятельно заполняя title и description:
                    ПРИМЕР ВЫХОДА:
                    {{
                    "title": "(2-3 слова, описывающие description)"
                    "description": "Сегодня рацион был особенно плотным — вы превысили привычный и целевой уровень. Завтра можно немного сбалансировать."
                    }}
'''

    def get_big_trend_prompt(**kwargs) -> str:
        foods_today_str = ", ".join([f.get('name', 'Неизвестный продукт') for f in kwargs.get('foods_today', [])])
        return f'''
                    ВХОДНЫЕ ДАННЫЕ:
                    total_calories_today: {kwargs["total_calories_today"]} – сколько калорий потребил пользователь за день
                    average_calories_last_week: {kwargs["average_calories_last_week"]} – средний дневной калораж за последние 7 дней
                    target_calories: {kwargs["target_calories"]} – целевая дневная норма калорий
                    user_goal: {kwargs["goal"]} – цель пользователя 
                    health_data_json: {kwargs["kz"]} – карта здоровья
                    {foods_today_str}: опциональный список еды, которую пользователь употребил сегодня

                    ВЫХОДНОЙ ФОРМАТ:
                    Глубокий анализ калорий и глубокий анализ БЖУ
                    [два–три предложения с акцентом на перекосы, сбалансированность и вклад белков/жиров/углеводов]

                    ТВОЯ ЛОГИКА:
                    – Вычисли % белков, жиров и углеводов от общего калоража (1 г белка/углеводов = 4 ккал, жиров = 9 ккал)
                    – Обрати внимание на перекосы с учетом личного профиля — например, избыток жиров или дефицит белка.
                    – Сделай упор на то, как это могло повлиять на насыщение, уровень энергии и восстановление, предлагай конкретные сценарии решений / питания.
                    – Избегай обвиняющего тона — используй стиль поддержки.
                    
                    Выводи тренды СТРОГО в формате JSON в СТРОГО таком формате и СТРОГО с такими данными, самостоятельно заполняя title и description:
                    ПРИМЕР ВЫХОДА:
                    {{
                    "title": "(2-3 слова, описывающие description)"
                    "description": "Рацион оказался жирнее привычного — более 40% калорий пришлись на жиры, в основном из курицы с картошкой и карбонары. Белков и углеводов было меньше, что могло сказаться на энергии и восстановлении. Сбалансировать это можно лёгким белковым ужином завтра."
                    }}
'''

    def get_achievements_prompt(**kwargs) -> str:
        return f'''
                    ВХОДНЫЕ ДАННЫЕ:
                    total_calories_today: {kwargs["total_calories_today"]} – сколько съел за день
                    average_calories_last_week: {kwargs["average_calories_last_week"]} – сколько ел в среднем
                    target_calories: {kwargs["target_calories"]} – целевая дневная норма
                    user_goal: {kwargs["goal"]} – цель пользователя 
                    health_data_json: {kwargs["kz"]} – карта здоровья
                    ВЫХОДНОЙ ФОРМАТ:
                    Вывод по питанию  
                    [один связный параграф из 2 предложений: 1 — наблюдение, 2 — интерпретация/последствие]
                    Логика:
                    – Сравни день с нормой и со средним.
                    – Если день — отклонение от стабильного шаблона (вверх/вниз), укажи это.
                    – Сделай вывод о причине (насыщенность, компенсация, хаотичное питание, смена паттерна).
                    – В интерпретации — мягко предположи эффект: энергичность, переутомление, риск срывов, компенсация, сбой графика и т.п. Учитывай цель пользователя и карту здоровья для общего контекста.
                    Избегай банальных фраз типа "всё хорошо" или "продолжайте в том же духе". Твоя задача — заметить тренд, объяснить, что он значит, и чему может привести.

                    СТИЛЬ:
                    – Умный, но доступный.
                    – Не пугающий, но не пустой.
                    – Как заботливый аналитик или диетолог.
                    
                    Выводи тренды СТРОГО в формате JSON в СТРОГО таком формате и СТРОГО с такими данными, самостоятельно заполняя title и description:
                    ПРИМЕР ВЫХОДА:
                    {{
                    "title": "(2-3 слова, описывающие description)"
                    "description": "Сегодня вы потребили значительно больше, чем обычно, и вышли далеко за пределы целевой нормы. Это может указывать на эпизод переедания, связанный с эмоциональной нагрузкой или нерегулярным графиком — стоит понаблюдать за следующими днями, особенно учитывая вашу цель пробежать марафон через неделю."
                    }}
'''

    eligible_users = get_eligible_users()
    generate_trends_for_users(eligible_users)

set_calories_trends_pipeline_dag = set_calories_trends_dag()