from datetime import datetime
import configparser
import requests
from airflow.decorators import dag, task
import json
import tiktoken

# Читаем конфиг и извлекаем токен
config = configparser.ConfigParser()
config.read("/opt/airflow/pipe/config.ini")
API_TOKEN_GPT = config.get('TOKENS', 'API_TOKEN_GPT')

default_args = {
    'owner': '<PERSON><PERSON> Lit<PERSON>ov',
    'retries': 0,
}

@dag(dag_id='chatgpt_image_pipeline',
     default_args=default_args,
     start_date=datetime.now(),
     catchup=False,
     schedule_interval=None)
def chatgpt():
    @task()
    def generate_openai_response(**kwargs):
        def analyze_token_usage(input_prompt: str, output_response: str, cost_rate_input: float = 0.03,
                                cost_rate_output: float = 0.06, tokens_unit: int = 1000, model: str = "gpt-4o"):
            """
            Анализирует использование токенов для входного промпта и выходного ответа с использованием tiktoken.
            Вычисляет:
              - Точное количество токенов для входного промпта
              - Точное количество токенов для выходного ответа
              - Стоимость токенов на основе заданных тарифов
            """
            encoder = tiktoken.encoding_for_model(model)
            tokens_input = len(encoder.encode(input_prompt))
            tokens_output = len(encoder.encode(output_response))

            cost_input = (tokens_input / tokens_unit) * cost_rate_input
            cost_output = (tokens_output / tokens_unit) * cost_rate_output

            print(f"Input tokens: {tokens_input}, Output tokens: {tokens_output}")
            print(f"Cost for input tokens: ${cost_input:.4f}, Cost for output tokens: ${cost_output:.4f}")

            return {
                "tokens_input": tokens_input,
                "tokens_output": tokens_output,
                "cost_input": cost_input,
                "cost_output": cost_output
            }

        conf = kwargs.get('dag_run').conf

        if not conf:
            raise ValueError("Конфигурация DAG не предоставлена.")

        image_base64 = conf.get("image")
        prompt = conf.get("prompt")

        if not image_base64:
            raise ValueError("Не найдено поле 'image' в конфигурации DAG.")
        if not prompt:
            raise ValueError("Не найдено поле 'prompt' в конфигурации DAG.")

        belgium_proxies = {
            "http": "http://77.221.142.174:8888",
            "https": "http://77.221.142.174:8888"
        }
        germany_proxies = {
            "http": "http://77.221.142.174:8888",
            "https": "http://77.221.142.174:8888"
        }

        headers = {
            "Authorization": f"Bearer {API_TOKEN_GPT}",
            "Content-Type": "application/json"
        }

        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": str(prompt)},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"{image_base64}"}
                    }
                ],
            }
        ]

        data = {
            "model": "gpt-4o",
            "temperature": 0,
            "messages": messages
        }

        url = "https://api.openai.com/v1/chat/completions"
        try:
            response = requests.post(url, json=data, headers=headers, proxies=germany_proxies)
        except Exception as e:
            response = requests.post(url, json=data, headers=headers, proxies=belgium_proxies)
            response.raise_for_status()
        # response = requests.post(url, json=data, headers=headers)

        response = response.json()["choices"][0]["message"]["content"]
        # stats = analyze_token_usage(prompt, response)
        # print(stats)
        if response is None:
            response = "{'Данные из изображения': 'Нет полученных данных'}"
        response = response.replace("```json\n", "").replace("\n```", "")
        return response.strip('"')

    @task
    def get_json_metrics(response):
        response = response.strip()
        start = response.find('{')
        end = response.rfind('}')

        if start != -1 and end != -1:
            json_string = response[start:end + 1].strip()
            content = json_string.replace("True", "true").replace("False", "false").replace("None", "null").replace(
                "[]", "null").replace("'", '"')
            try:
                return content
            except json.JSONDecodeError:
                raise ValueError("Failed to parse JSON from cleaned string")
        else:
            try:
                response_cleaned = response.strip()
                content = json.loads(response_cleaned)
                return content
            except json.JSONDecodeError:
                response = '{"Данные из изображения": "Нет полученных данных"}'
                response_cleaned = response.strip()
                content = json.loads(response_cleaned)
                return content

    response = generate_openai_response(provide_context=True)
    get_json_metrics(response)

dag_chatgpt = chatgpt()
