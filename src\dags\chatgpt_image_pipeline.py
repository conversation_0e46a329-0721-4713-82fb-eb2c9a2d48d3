from datetime import datetime
import configparser
import requests
from airflow.decorators import dag, task
import json
import tiktoken
from cost_tracking import track_openai_request, OperationType
from cost_tracking import track_openai_cost

# Читаем конфиг и извлекаем токен
config = configparser.ConfigParser()
config.read("/opt/airflow/pipe/config.ini")
API_TOKEN_GPT = config.get('TOKENS', 'API_TOKEN_GPT')

default_args = {
    'owner': '<PERSON><PERSON>',
    'retries': 0,
}

@dag(dag_id='chatgpt_image_pipeline',
     default_args=default_args,
     start_date=datetime.now(),
     catchup=False,
     schedule_interval=None)
def chatgpt():
    @task()
    def generate_openai_response(**kwargs):
        

        conf = kwargs.get('dag_run').conf

        if not conf:
            raise ValueError("Конфигурация DAG не предоставлена.")

        image_base64 = conf.get("image")
        prompt = conf.get("prompt")

        if not image_base64:
            raise ValueError("Не найдено поле 'image' в конфигурации DAG.")
        if not prompt:
            raise ValueError("Не найдено поле 'prompt' в конфигурации DAG.")

        belgium_proxies = {
            "http": "http://**************:8888",
            "https": "http://**************:8888"
        }
        germany_proxies = {
            "http": "http://**************:8888",
            "https": "http://**************:8888"
        }

        headers = {
            "Authorization": f"Bearer {API_TOKEN_GPT}",
            "Content-Type": "application/json"
        }

        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": str(prompt)},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"{image_base64}"}
                    }
                ],
            }
        ]

        data = {
            "model": "gpt-4o",
            "temperature": 0,
            "messages": messages
        }

        url = "https://api.openai.com/v1/chat/completions"
        try:
            response = requests.post(url, json=data, headers=headers, proxies=germany_proxies)
        except Exception as e:
            response = requests.post(url, json=data, headers=headers, proxies=belgium_proxies)
            response.raise_for_status()
        # response = requests.post(url, json=data, headers=headers)

        response_json = response.json()
        content = response_json["choices"][0]["message"]["content"]

        # Мониторинг затрат OpenAI
        try:
            cost_analysis = track_openai_cost(
                response_data=response_json,
                context={
                    "dag_id": "chatgpt_image_pipeline",
                    "task_id": "generate_openai_response"
                }
            )

            print(f"💰 Стоимость операции: ${cost_analysis.get('total_cost', 0):.6f}")
            print(f"🔢 Токены: {cost_analysis.get('total_tokens', 0)}")

        except Exception as e:
            print(f"⚠️ Ошибка отслеживания затрат: {e}")

        # Отслеживаем затраты с помощью централизованной системы
        try:
            # Получаем user_id из конфигурации DAG, если доступен
            user_id = conf.get("user_id") if conf else None
            session_id = conf.get("session_id") if conf else None
            
            # Отслеживаем операцию
            cost_analysis = track_openai_request(
                operation_type=OperationType.IMAGE_ANALYSIS,
                input_prompt=prompt,
                output_response=content,
                model="gpt-4o",
                user_id=user_id,
                session_id=session_id,
                additional_metadata={
                    "dag_id": "chatgpt_image_pipeline",
                    "task_id": "generate_openai_response"
                }
            )
            
            print(f"💰 Стоимость операции: ${cost_analysis.get('total_cost', 0):.6f}")
            print(f"🔢 Токены: {cost_analysis.get('total_tokens', 0)}")
            
        except Exception as e:
            print(f"⚠️ Ошибка отслеживания затрат: {e}")
        if response is None:
            response = "{'Данные из изображения': 'Нет полученных данных'}"
        response = response.replace("```json\n", "").replace("\n```", "")
        return content.strip('"')

    @task
    def get_json_metrics(response):
        response = response.strip()
        start = response.find('{')
        end = response.rfind('}')

        if start != -1 and end != -1:
            json_string = response[start:end + 1].strip()
            content = json_string.replace("True", "true").replace("False", "false").replace("None", "null").replace(
                "[]", "null").replace("'", '"')
            try:
                return content
            except json.JSONDecodeError:
                raise ValueError("Failed to parse JSON from cleaned string")
        else:
            try:
                response_cleaned = response.strip()
                content = json.loads(response_cleaned)
                return content
            except json.JSONDecodeError:
                response = '{"Данные из изображения": "Нет полученных данных"}'
                response_cleaned = response.strip()
                content = json.loads(response_cleaned)
                return content

    response = generate_openai_response(provide_context=True)
    get_json_metrics(response)

dag_chatgpt = chatgpt()
