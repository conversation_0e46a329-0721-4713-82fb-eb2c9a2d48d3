"""
API эндпоинты для мониторинга затрат Biome AI
Безопасные эндпоинты для получения статистики по затратам
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dags'))

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
from enum import Enum

# Импортируем нашу систему мониторинга затрат
try:
    from cost_tracking import CostTracker, OperationType, UsageScenarios, get_cost_summary
except ImportError as e:
    print(f"⚠️ Ошибка импорта cost_tracking: {e}")
    # Создаем заглушки для случая, если модуль недоступен
    class OperationType(str, Enum):
        GENERAL_CHAT = "general_chat"
    
    class UsageScenarios:
        @staticmethod
        def get_scenario_definitions():
            return {}
    
    def get_cost_summary(days=7):
        return {"error": "Cost tracking module not available"}

# Создаем роутер для API мониторинга
router = APIRouter(prefix="/cost-monitoring", tags=["Cost Monitoring"])

# Модели данных для API
class CostStatsRequest(BaseModel):
    """Запрос статистики по затратам"""
    days: int = Field(default=7, ge=1, le=90, description="Количество дней для анализа (1-90)")
    operation_type: Optional[str] = Field(default=None, description="Тип операции для фильтрации")

class CostStatsResponse(BaseModel):
    """Ответ со статистикой по затратам"""
    period_days: int
    generated_at: str
    statistics: Dict[str, Any]
    total_cost: float = Field(description="Общая стоимость за период")
    total_operations: int = Field(description="Общее количество операций")

class ScenarioStatsResponse(BaseModel):
    """Ответ со статистикой по сценариям"""
    scenarios: Dict[str, Dict]
    estimated_monthly_cost: float
    most_expensive_scenario: str
    most_frequent_scenario: str

class HealthCheckResponse(BaseModel):
    """Ответ проверки здоровья системы"""
    status: str
    redis_connected: bool
    cost_tracking_available: bool
    timestamp: str

# Зависимость для получения трекера затрат
def get_cost_tracker() -> CostTracker:
    """
    Получает экземпляр трекера затрат
    """
    try:
        return CostTracker()
    except Exception as e:
        raise HTTPException(
            status_code=503, 
            detail=f"Система мониторинга затрат недоступна: {e}"
        )

@router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """
    Проверка здоровья системы мониторинга затрат
    """
    try:
        tracker = CostTracker()
        redis_connected = tracker.redis_client is not None
        
        if redis_connected:
            # Проверяем соединение с Redis
            tracker.redis_client.ping()
        
        return HealthCheckResponse(
            status="healthy" if redis_connected else "degraded",
            redis_connected=redis_connected,
            cost_tracking_available=True,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        return HealthCheckResponse(
            status="unhealthy",
            redis_connected=False,
            cost_tracking_available=False,
            timestamp=datetime.now().isoformat()
        )

@router.get("/stats", response_model=CostStatsResponse)
async def get_cost_stats(
    days: int = Query(default=7, ge=1, le=90, description="Количество дней для анализа"),
    operation_type: Optional[str] = Query(default=None, description="Тип операции для фильтрации"),
    tracker: CostTracker = Depends(get_cost_tracker)
):
    """
    Получает статистику по затратам за указанный период
    
    Args:
        days: Количество дней для анализа (1-90)
        operation_type: Тип операции для фильтрации (опционально)
        tracker: Экземпляр трекера затрат
    
    Returns:
        Статистика по затратам
    """
    try:
        # Валидируем тип операции, если указан
        if operation_type:
            try:
                op_type = OperationType(operation_type)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Неверный тип операции. Доступные типы: {[op.value for op in OperationType]}"
                )
        else:
            op_type = None
        
        # Получаем статистику
        stats = tracker.get_operation_stats(operation_type=op_type, days=days)
        
        if "error" in stats:
            raise HTTPException(status_code=500, detail=stats["error"])
        
        # Вычисляем общие метрики
        total_cost = 0.0
        total_operations = 0
        
        for op_stats in stats.get("statistics", {}).values():
            total_cost += op_stats.get("total_cost", 0)
            total_operations += op_stats.get("total_operations", 0)
        
        return CostStatsResponse(
            period_days=stats["period_days"],
            generated_at=stats["generated_at"],
            statistics=stats["statistics"],
            total_cost=round(total_cost, 6),
            total_operations=total_operations
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка получения статистики: {e}")

@router.get("/scenarios", response_model=ScenarioStatsResponse)
async def get_scenario_stats():
    """
    Получает информацию об основных сценариях использования и их стоимости
    
    Returns:
        Статистика по сценариям использования
    """
    try:
        scenarios = UsageScenarios.get_scenario_definitions()
        
        if not scenarios:
            raise HTTPException(status_code=404, detail="Сценарии использования не найдены")
        
        # Вычисляем метрики
        total_monthly_cost = 0.0
        most_expensive = ""
        max_cost = 0.0
        
        for scenario_id, scenario in scenarios.items():
            cost = scenario.get("estimated_cost", 0)
            # Предполагаем использование каждого сценария 10 раз в месяц
            monthly_cost = cost * 10
            total_monthly_cost += monthly_cost
            
            if cost > max_cost:
                max_cost = cost
                most_expensive = scenario.get("name", scenario_id)
        
        return ScenarioStatsResponse(
            scenarios=scenarios,
            estimated_monthly_cost=round(total_monthly_cost, 2),
            most_expensive_scenario=most_expensive,
            most_frequent_scenario="Ежедневное взаимодействие"  # Предполагаемый наиболее частый сценарий
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка получения сценариев: {e}")

@router.get("/operation-types")
async def get_operation_types():
    """
    Получает список доступных типов операций
    
    Returns:
        Список типов операций с описаниями
    """
    try:
        operation_types = []
        for op_type in OperationType:
            operation_types.append({
                "value": op_type.value,
                "name": op_type.value.replace("_", " ").title(),
                "description": f"Операции типа {op_type.value}"
            })
        
        return {
            "operation_types": operation_types,
            "total_count": len(operation_types)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка получения типов операций: {e}")

@router.get("/summary")
async def get_cost_summary_endpoint(
    days: int = Query(default=7, ge=1, le=90, description="Количество дней для анализа")
):
    """
    Получает краткую сводку по затратам (упрощенная версия)
    
    Args:
        days: Количество дней для анализа
    
    Returns:
        Краткая сводка по затратам
    """
    try:
        summary = get_cost_summary(days=days)
        
        if "error" in summary:
            raise HTTPException(status_code=500, detail=summary["error"])
        
        return {
            "period_days": days,
            "summary": summary,
            "generated_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка получения сводки: {e}")

# Функция для добавления роутера к основному приложению FastAPI
def add_cost_monitoring_routes(app):
    """
    Добавляет роуты мониторинга затрат к приложению FastAPI
    
    Args:
        app: Экземпляр FastAPI приложения
    """
    app.include_router(router)
