import asyncio
import re
import json
import configparser
from typing import Optional, Any, Dict
from enum import Enum
import base64
import math
from typing import Mapping
import httpx
client = httpx.AsyncClient(timeout=10.0)
from io import BytesIO
from PIL import Image
import random
import time
from datetime import datetime, timedelta, timezone, time as dt_time
import pymupdf
import redis.asyncio as aioredis
from fastapi import FastAPI, HTTPException, Request
from pydantic import BaseModel, model_validator, validator
from collections import Counter
from typing import List, Dict

from run_chatgpt_pipelines import (
    run_chatgpt_message_pipeline,
    run_chatgpt_chat_open_pipeline,
    run_chatgpt_recount_calories_pipeline,
    run_chatgpt_clarify_pipeline,
    run_chatgpt_classify_pipeline,
    run_chatgpt_chat_open_rec_risk_pipeline,
    run_chatgpt_message_recommendation_pipeline,
    run_chatgpt_message_risk_pipeline,
    run_chatgpt_metrics_pipeline,
    run_chatgpt_goals_pipeline,
    run_perplexity_pipeline,
    run_chatgpt_image_object_pipeline,
    run_chatgpt_image_pipeline,
    run_chatgpt_pdf_pipeline,
    run_chatgpt_analyze_food_pipeline,
    run_chatgpt_generate_challenges_pipeline,
    run_chatgpt_describe_challenge_pipeline,
    run_chatgpt_calories_achievements_pipeline,
    run_chatgpt_calories_small_trend_pipeline,
    run_chatgpt_calories_big_trend_pipeline,
    run_chatgpt_challenge_classify_pipeline,
    run_chatgpt_challenge_ask_pipeline,
    run_chatgpt_unified_question_pipeline
)

from db_manipulation import (
    update_health_profile,
    update_goals_profile,
    fetch_user_data,
    upload_recommendation,
    upload_risk,
    fetch_all_recommendations,
    fetch_all_risks,
    fetch_risk_by_id,
    fetch_recommendation_by_id,
    delete_user_risks_from_db,
    delete_user_recommendations_from_db,
    upload_chat_messages_for_closing,
    fetch_all_chats,
    delete_existing_chats_by_document_id,
    get_last_uploaded_image,
    upload_food,
    upload_challenge,
    get_user_challenges,
    get_user_challenges_mechanics,
    get_user_foods,
    upload_trend
)

import logging
import os
from contextlib import asynccontextmanager

LOGS_DIR = "logs/user_performance"
if not os.path.exists(LOGS_DIR):
    os.makedirs(LOGS_DIR)

_loggers = {}

def get_user_logger(user_id: int):
    logger_name = f"user_{user_id}"
    if logger_name in _loggers:
        return _loggers[logger_name]

    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.INFO)
    logger.propagate = False # Чтобы избежать дублирования логов в root логгере

    # Очищаем хендлеры, если они уже были добавлены, чтобы избежать дублирования
    if logger.hasHandlers():
        logger.handlers.clear()

    handler = logging.FileHandler(f"{LOGS_DIR}/user_{user_id}.log")
    formatter = logging.Formatter('%(asctime)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    _loggers[logger_name] = logger
    return logger

@asynccontextmanager
async def log_time(logger: logging.Logger, endpoint: str, stage: str, **kwargs):
    start_time = time.time()
    # Собираем все дополнительные параметры в одну строку для лога
    log_info = f"endpoint=[{endpoint}] stage=[{stage}]"
    if kwargs:
        log_info += " " + " ".join([f"{k}=[{v}]" for k, v in kwargs.items()])

    logger.info(f"{log_info} - START")
    try:
        yield
    finally:
        duration = time.time() - start_time
        logger.info(f"{log_info} - END - duration=[{duration:.4f}s]")

async def log_message(user_id: int, message: str):
    """Logs a simple message to the user's specific log file."""
    logger = get_user_logger(user_id)
    logger.info(message)

################################
# ПРОМПТЫ НА ВСЕ СЛУЧАИ ЖИЗНИ =)
################################
prompts = configparser.ConfigParser()
prompts.read("prompts.ini")

# ПРОМПТ НА КЛАССИФИКАЦИЮ
CLASSIFY_CHATTING_PROMPT = prompts.get('PROMPTS', 'CLASSIFY_CHATTING_PROMPT')
CLARIFY_PROMPT = prompts.get('PROMPTS', 'CLARIFY_PROMPT')
REORGANIZE_PROMPT = prompts.get('PROMPTS', 'REORGANIZE_PROMPT')

# СЦЕНАРИЙ ОБЩЕГО ЧАТА
CONVERSATION_PROMPT = prompts.get('PROMPTS', 'CONVERSATION_PROMPT')
FINAL_PROMPT_COMPLETED = prompts.get('PROMPTS', 'FINAL_PROMPT_COMPLETED')
FINAL_PROMPT_NOT_COMPLETED = prompts.get('PROMPTS', 'FINAL_PROMPT_NOT_COMPLETED')

# ЗАПОЛНЕНИЕ МЕТРИК
METRICS_PROMPT = prompts.get('PROMPTS', 'METRICS_PROMPT')
CHANGE_PROMPT = prompts.get('PROMPTS', 'CHANGE_PROMPT')

# ЗАПОЛНЕНИЕ ЦЕЛИ
GOALS_PROMPT = prompts.get('PROMPTS', 'GOALS_PROMPT')

# ПРОМПТ ОБЩЕНИЯ ПО РИСКАМ И РЕКОМЕНДАЦИЯМ
CHAT_RISK_RECOMMENDATION = prompts.get('PROMPTS', 'CHAT_RISK_RECOMMENDATION')

# ПРОМПТ НА ГЕНЕРАЦИЮ РИСКОВ И РЕКОМЕНДАЦИЙ
RECOMMENDATIONS_PROMPT = prompts.get('PROMPTS', 'RECOMMENDATIONS_PROMPT')
RISKS_PROMPT = prompts.get('PROMPTS', 'RISKS_PROMPT')

# АНАЛИЗ ИЗОБРАЖЕНИЯ
IMAGE_PROMPT = prompts.get('PROMPTS', 'IMAGE_PROMPT')
OBJECT_PROMPT = prompts.get('PROMPTS', 'OBJECT_PROMPT')
ANALYZE_FOOD = prompts.get('PROMPTS', 'ANALYZE_FOOD')

# КОСТЫЛИ
GENERATE_CHALLENGES_PROMPT = prompts.get('PROMPTS', 'GENERATE_CHALLENGES_PROMPT')

# ЧЕЛЛЕНДЖИ
DESCRIBE_CHALLENGE_PROMPT = prompts.get('PROMPTS', 'DESCRIBE_CHALLENGE_PROMPT')
CHALLENGE_CLASSIFY = prompts.get('PROMPTS', 'CHALLENGE_CLASSIFY')
CHALLENGE_ASK = prompts.get('PROMPTS', 'CHALLENGE_ASK')

# КАЛОРАЖ
CALORIES_ACHIEVEMENTS = prompts.get('PROMPTS', 'CALORIES_ACHIEVEMENTS')
CALORIES_BIG_TREND = prompts.get('PROMPTS', 'CALORIES_BIG_TREND')
CALORIES_SMALL_TREND = prompts.get('PROMPTS', 'CALORIES_SMALL_TREND')


##################
# СИСТЕМНЫЕ ТЕКСТЫ
##################
config = configparser.ConfigParser()
config.read("config.ini")

BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
TEST_METRICS = config.get('DATA', 'TEST_METRICS')

# 1ый ЭТАП ВОПРОСОВ
# questions_first_stage = {
#     "name": "**Как вас зовут?** \n\n*| введите ваше полное имя*",
#     "gender": "**Какой ваш пол?** \n\n*| м / ж*",
#     "date_of_birth": "**Укажите вашу дату рождения.** \n\n*| введите в формате ДД.ММ.ГГГГ*",
#     "height": "**Какой у вас рост?** \n\n*| укажите в сантиметрах*",
#     "weight": "**Какой у вас вес?** \n\n*| в кг*",
#     "activity_level": "**Каков ваш уровень физической активности?** \n\n*| где 0 - низкий, 10 - высокий*",
#     "food_level": "**Как вы в целом оцениваете качество вашего питания?** \n\n*| где 0 - очень плохое (несбалансированное и нерегулярное), 10 - очень хорошее (сбалансированное, регулярное)*",
#     "smoking": "**Вы курите? Если да, то как часто.** \n\n*| где 0 - никогда, 10 - регулярно*",
#     "alcohol": "**Употребляете ли вы алкоголь? Если да, то как часто.** \n\n*| где 0 - никогда, 10 - регулярно*",
#     "sleep_recovery": "**Насколько хорошо сон помогает вам восстановиться?** \n\n*| 0 - просыпаюсь уставшим, 10 - просыпаюсь полным сил*",
#     "stress_level": "**Как вы оцениваете уровень стресса в вашей жизни?** \n\n*| где 0 - вообще нет, 10 - постоянно много стресса*",
#     "chronic_conditions": "**Есть ли у вас хронические заболевания? Если да, укажите их.** \n\n*| пример - диабет, астма*",
#     "allergies": "**Есть ли у вас аллергии? Если да, укажите их.** \n\n*| пример - аллергия на пыльцу*",
#     "sports": "**Занимаетесь ли вы спортом? Если да, укажите вид спорта и частоту тренировок.** \n\n*| пример - бег, 3 раза в неделю*",
#     "blood_pressure": "**Знаете ли вы свой уровень артериального давления?** Если да, укажите последние значения. \n\n*| пример - 120/80 мм рт. ст.*",
#     "regular_medicine": "**Принимаете ли вы регулярно лекарства?** Если да, укажите их названия и дозировку. \n\n*| пример - метформин 500 мг*",
#     "dietary_supplement": "**Принимаете ли вы пищевые добавки?** Если да, укажите их названия и дозировки. \n\n*| пример - рыбий жир 1000 мг*",
#     "genetic_conditions": "**Есть ли у вас наследственные заболевания? Если да, укажите их.** \n\n*| пример - гипертония*",
#     "work_schedule": "**Какой у вас рабочий график?** \n\n*| где 0 - утренний, 1 - вечерний, 2 - сменный, 3 - нерегулярный*",
#     "sleep_quality": "**Как вы оцениваете качество вашего сна?** \n\n*| где 0 - очень плохо, 10 - очень хорошо*",
#     "sleep_schedule": "**Сколько часов вы спите в среднем за ночь?** \n\n*| пример -  7 ( часов )*",
#     "diet_balance": "**Как вы оцениваете сбалансированность вашего рациона?** \n\n*| где 0 - плохо, 10 - отлично*",
#     "diet_schedule": "**Следуете ли вы определенному графику питания?** Если да, опишите его. \n\n*| пример - 3 раза в день*",
#     "calorie_intake": "**Знаете ли вы ваш примерный дневной калораж?** \n\n*| укажите в калориях, пример - 2000 ккал*",
#     "preferred_dishes": "**Какие блюда вы предпочитаете?** \n\n*| пример - салаты, паста*",
#     "injuries": "**Были ли у вас серьезные травмы? Если да, укажите, когда и какие.** \n\n*| пример - перелом ноги, 2018 г.*",
#     "food_intolerances": "**Есть ли у вас непереносимость определенных продуктов?** Если да, укажите их. \n\n*| пример - лактоза*",
#     "blood_analysis": "Если у вас какие-либо **результаты анализов**, которые вы сдавали, пожалуйста, пришлите мне их файлом для анализа"
# }

questions_first_stage = {
    "name": "Укажи твое Имя.",
    "gender": "Какой у тебя пол?",
    "date_of_birth": "Подскажите дату вашего рождения | день, месяц, год",
    "height": "Ваш рост? | укажи в сантиметрах",
    "weight": "Какой у тебя вес? | в кг",
    "activity_level": "Какой в целом твой уровень физической активности? | где 0 - низкий, 10 - высокий",
    "food_level": "Как ты в целом оцениваешь качество своего питания? | где 0 - очень плохое (несбалансированное и нерегулярное), 10 - очень хорошее (сбалансированное, регулярное)",
    "smoking": "Ты куришь? Если да, то как часто. | где 0 - никогда, 10 - регулярно",
    "alcohol": "Употребляешь ли ты алкоголь? Если да, то как часто. | где 0 - никогда, 10 - регулярно",
    "sleep_recovery": "Насколько хорошо сон помогает тебе восстановиться? | 0 - просыпаюсь уставшим, 10 - просыпаюсь полным сил",
    "stress_level": "Как ты оцениваешь уровень стресса в твоей жизни? | где 0 - вообще нет, 10 - постоянно много стресса",
    "chronic_conditions": "Есть ли у вас хронические заболевания? Если да, укажите их. \n\n| пример - диабет, астма",
    "allergies": "Есть ли у вас аллергии? Если да, укажите их. \n\n| пример - аллергия на пыльцу",
    "sports": "Занимаетесь ли вы спортом? Если да, укажите вид спорта и частоту тренировок. \n\n| пример - бег, 3 раза в неделю",
    "blood_pressure": "Знаете ли вы свой уровень артериального давления? Если да, укажите последние значения. \n\n| пример - 120/80 мм рт. ст.",
    "regular_medicine": "Принимаете ли вы регулярно лекарства? Если да, укажите их названия и дозировку. \n\n| пример - метформин 500 мг",
    "dietary_supplement": "Принимаете ли вы пищевые добавки? Если да, укажите их названия и дозировки. \n\n| пример - рыбий жир 1000 мг",
    "genetic_conditions": "Есть ли у вас наследственные заболевания? Если да, укажите их. \n\n| пример - гипертония",
    "work_schedule": "Какой у вас рабочий график? \n\n| где 0 - утренний, 1 - вечерний, 2 - сменный, 3 - нерегулярный",
    "sleep_quality": "Как вы оцениваете качество вашего сна? \n\n| где 0 - очень плохо, 10 - очень хорошо",
    "sleep_schedule": "Сколько часов вы спите в среднем за ночь? \n\n| пример - 7 ( часов )",
    "diet_balance": "Как вы оцениваете сбалансированность вашего рациона? \n\n| где 0 - плохо, 10 - отлично",
    "diet_schedule": "Следуете ли вы определенному графику питания? Если да, опишите его. \n\n| пример - 3 раза в день",
    "calorie_intake": "Знаете ли вы ваш примерный дневной калораж? \n\n| укажите в калориях, пример - 2000 ккал",
    "preferred_dishes": "Какие блюда вы предпочитаете? \n\n| пример - салаты, паста",
    "injuries": "Были ли у вас серьезные травмы? Если да, укажите, когда и какие. \n\n| пример - перелом ноги, 2018 г.",
    "food_intolerances": "Есть ли у вас непереносимость определенных продуктов? Если да, укажите их. \n\n| пример - лактоза"
    # "blood_analysis": "Если у вас есть какие-либо результаты анализов, которые вы сдавали, пожалуйста, пришлите мне их файлом для анализа"
}

metrics_format = {
    "name": "Имя в формате VARCHAR. Пример: 'Никита'",
    "gender": "Значение из справочника [М, Ж] в формате VARCHAR. Пример: 'М'",
    "date_of_birth": "Дата рождения от 01.01.1925 до 01.01.2026 в формате VARCHAR. Пример: '01.02.2002''",
    "height": "Число от 50 до 250 в формате VARCHAR. Пример: '181'",
    "weight": "Число от 30 в формате VARCHAR. Пример: '81.5'",
    "activity_level": "Десятибалльная шкала в формате VARCHAR. Пример: '5/10'",
    "food_level": "Десятибалльная шкала в формате VARCHAR. Пример: '5/10'",
    "smoking": "Десятибалльная шкала в формате VARCHAR. Пример: '5/10'",
    "alcohol": "Десятибалльная шкала в формате VARCHAR. Пример: '5/10'",
    "sleep_recovery": "Десятибалльная шкала в формате VARCHAR. Пример: '5/10'",
    "stress_level": "Десятибалльная шкала в формате VARCHAR. Пример: '5/10'",
    "chronic_conditions": "Массив строк в формате VARCHAR ARRAY []. Пример: ['Риск рака']",
    "allergies": "Массив строк в формате VARCHAR ARRAY []. Пример ['Молочные продукты']",
    "sports": "Строка в ``формате VARCHAR. Пример: 'Спортзал'",
    "regular_medicine": "Массив строк в формате VARCHAR ARRAY []. Пример: ['Регулярно принимаемое лекарство 1']",
    "genetic_conditions": "Массив строк в формате VARCHAR ARRAY []. Пример: ['Наследственное заболевание 1']",
    "sleep_quality": "Десятибальная шкала в формате VARCHAR. Пример: '5/10'",
    "injuries": "Cтрока в формате VARCHAR. Пример: 'Сломанный палец'",
    "diet_balance": "Десятибальная шкала в формате VARCHAR. Пример: '5/10'",
    "calorie_intake": "Строка в формате VARCHAR. Пример: '2000'",
    "sleep_schedule": "Количество часов в формате VARCHAR. Пример: '8'",
    "work_schedule": "Cтрока в формате VARCHAR. Пример: 'Утренний'",
    "blood_pressure": "Значение из справочника [Высокое, Нормальное, Пониженное] в формате VARCHAR. Пример: 'Высокое'",
    "preferred_dishes": "Массив строк в формате VARCHAR ARRAY []. Пример: ['Мясо']",
    "diet_schedule": "Строка в формате VARCHAR. Пример: 2 раза в день",
    "food_intolerances": "Массив строк в формате VARCHAR ARRAY []. Пример: ['Чипсы']",
    "dietary_supplement": "Строка в формате VARCHAR. Пример: 'Да'"
}

metrics_format_for_reasking = {
    "name": "Имя. Пример: Никита",
    "gender": "Значение М или Ж Пример: М",
    "date_of_birth": "Дата рождения от 01.01.1925. Пример: 01.02.2002",
    "height": "Число от 50 до 250. Пример: 181",
    "weight": "Число от 30. Пример: 81.5",
    "activity_level": "Десятибалльная шкала. Пример: 5/10",
    "food_level": "Десятибалльная шкала. Пример: 5/10",
    "smoking": "Десятибалльная шкала. Пример: 5/10",
    "alcohol": "Десятибалльная шкала. Пример: 5/10",
    "sleep_recovery": "Десятибалльная шкала. Пример: 5/10",
    "stress_level": "Десятибалльная шкала. Пример: 5/10",
    "chronic_conditions": "Хронические заболевания человека. Пример: Риск рака",
    "allergies": "Аллергии . Пример: Молочные продукты",
    "sports": "Спорт. Пример: Спортзал",
    "regular_medicine": "Регулярно принимаемое лекарство. Пример: Регулярно принимаемое лекарство 1",
    "genetic_conditions": "Генетические заболевания. Пример: Гипертония",
    "sleep_quality": "Десятибальная шкала. Пример: 5/10",
    "injuries": "Травмы. Пример: Сломанный палец",
    "diet_balance": "Десятибальная шкала. Пример: 5/10",
    "calorie_intake": "Калораж. Пример: 2000",
    "sleep_schedule": "Количество часов сна. Пример: 8",
    "work_schedule": "Трехбальная шкала. Пример: 2",
    "blood_pressure": "Значение из справочника Высокое, Нормальное, Пониженное. Пример: Высокое",
    "preferred_dishes": "Предпочитаемые блюда. Пример: Паста Карбонара, Кофе",
    "diet_schedule": "График питания. Пример: 2 раза в день",
    "food_intolerances": "Непереносимость продуктов. Пример: Чипсы",
    "dietary_supplement": "Пищевые добавки. Пример: Рыбий Жир 500мг"
}

chatting_ending = [
    "Когда будете готовы продолжить анкету — просто дайте мне знать!",
    "Чтобы вернуться к анкете, напишите мне — я всегда на связи!",
    "Если захотите продолжить заполнение анкеты, я буду здесь и с радостью помогу!",
    "Захотите вернуться к анкете — просто напишите, я всегда готов помочь!",
    "Если решите продолжить анкетирование — напишите, и мы сразу же продолжим!",
    "Анкета доступна в любое время — просто скажите, когда продолжить!",
    "Я всегда рад вернуться к анкете, сообщите, когда захотите продолжить!",
    "Чтобы вернуться к анкете, достаточно просто написать мне. Я сразу подключусь!",
    "Как только захотите продолжить работу с анкетой — напишите, и мы начнём!",
    "Буду ждать вашего сообщения, чтобы вместе продолжить анкету!",
    "Анкета всегда под рукой — просто напишите, когда захотите вернуться!",
    "Как только решите продолжить с анкетой, сразу же сообщите — я буду рядом!",
    "Чтобы продолжить заполнять анкету, напишите мне в любое время!",
    "Всегда готов вернуться к анкете — просто скажите, и мы продолжим!",
    "Когда вам будет удобно продолжить анкету — сообщите, я здесь для вас!",
    "Если появится желание снова открыть анкету, просто сообщите мне!",
    "В любое время готов помочь вам продолжить заполнение анкеты!",
    "Напишите, когда захотите вернуться — с радостью продолжу анкету вместе с вами!",
    "Как только появится желание продолжить анкету — сообщите, и я сразу подключусь!",
    "Если будете готовы двигаться дальше, напишите — и мы сразу продолжим анкету!",
    "Сообщите, как только захотите вернуться к анкете — я буду ждать вашего сообщения!"
]

app = FastAPI()
r = aioredis.from_url("redis://redis:6379", decode_responses=True)


###########################
# Helper functions for Redis
###########################

async def get_user_weight(user_id: int) -> int:
    weight_str = await get_last_list_item(f"user:weights:{user_id}")
    if weight_str is None:
        return 0
    try:
        return int(weight_str)
    except ValueError:
        return 0


async def remove_last_message(user_id: int, chat_type: str, type_id):
    key = f"user:messages-{chat_type}{type_id}:{user_id}"
    length = await r.llen(key)
    if length > 0:
        await r.ltrim(key, 0, length - 2)


async def get_last_list_item(key: str):
    return await r.lindex(key, -1)


async def get_metrics(user_id: int):
    return await get_last_list_item(f"user:metrics:{user_id}")


async def get_goals(user_id: int):
    return await get_last_list_item(f"user:goals:{user_id}")

async def get_timezone(user_id: int):
    return await get_last_list_item(f"user:timezone:{user_id}")


async def add_metric(user_id: int, metrics: str):
    await r.rpush(f"user:metrics:{user_id}", metrics)


async def add_object_analysis(user_id: int, analysis: str):
    await r.rpush(f"user:object_analysis:{user_id}", analysis)


async def get_object_analysis(user_id: int):
    return await get_last_list_item(f"user:object_analysis:{user_id}")


async def add_goals(user_id: int, goals: str):
    await r.rpush(f"user:goals:{user_id}", goals)


async def add_timezone(user_id: int, timezone: str):
    await r.delete(f"user:timezone:{user_id}")
    await r.rpush(f"user:timezone:{user_id}", timezone)


async def add_question_order(user_id: int, order: str):
    await r.rpush(f"user:question_order:{user_id}", order)


async def add_user_message(user_id: int, message: str, chat_type: str, type_id):
    await r.rpush(f"user:messages-{chat_type}{type_id}:{user_id}", message)


async def add_system_message(user_id: int, message: str, chat_type: str, type_id):
    await r.rpush(f"system:messages-{chat_type}{type_id}:{user_id}", message)


async def get_last_user_messages(user_id: int, chat_type: str, type_id, count=10):
    return await r.lrange(f"user:messages-{chat_type}{type_id}:{user_id}", -count, -1)


async def get_last_system_messages(user_id: int, chat_type: str, type_id, count=10):
    return await r.lrange(f"system:messages-{chat_type}{type_id}:{user_id}", -count, -1)


async def add_metric_update_to_queue(user_id: int, update: Mapping[str, Any]) -> None:
    """
    Кладёт готовый metrics_update в Redis.
    Его забирает фонoвый воркер и пишет в профиль.
    Формат `update`:
    {"metric_key": "...", "metric_value": "...", "metric_weight": 0.9}
    """
    # один глобальный «pipe» — BLPOP ждёт именно его
    await r.rpush(
        "metrics_updates",
        json.dumps({"user_id": user_id, **update}, ensure_ascii=False)
    )


async def add_message_to_queue(user_id: int, message: str):
    task = {"user_id": user_id, "message": message}
    await r.rpush("pending_metrics", json.dumps(task))


async def add_risks_to_queue(user_id: int):
    task = {"user_id": user_id}
    await r.rpush("pending_risks", json.dumps(task))


async def add_recommendations_to_queue(user_id: int):
    task = {"user_id": user_id}
    await r.rpush("pending_recommendations", json.dumps(task))


async def add_user_weights(user_id: int, weight: int):
    await r.rpush(f"user:weights:{user_id}", weight)


async def set_awaiting_goals(user_id: int):
    await r.set(f"user:awaiting_goals:{user_id}", 1)


async def is_awaiting_goals(user_id: int) -> bool:
    return await r.exists(f"user:awaiting_goals:{user_id}") == 1


async def set_chatting_mode(user_id: int, mode: str):
    await r.rpush(f"user:chatting_mode:{user_id}", mode)


async def get_chatting_mode(user_id: int):
    return await get_last_list_item(f"user:chatting_mode:{user_id}")


async def clear_awaiting_goals(user_id: int):
    await r.delete(f"user:awaiting_goals:{user_id}")


async def add_recommendation_local(user_id: int, recommendation: str):
    await r.rpush(f"user:recommendations:{user_id}", recommendation)


async def add_risk_local(user_id: int, risk: str):
    await r.rpush(f"user:risks:{user_id}", risk)


async def get_recommendation_by_id_local(user_id: int, recommendation_id: int):
    last_rec = await get_last_list_item(f"user:recommendations:{user_id}")
    if last_rec:
        last_rec = json.loads(last_rec)
        recommendation_key = f"recommendation {recommendation_id}"
        return last_rec.get(recommendation_key)
    return None


async def get_risk_by_id_local(user_id: int, risk_id: int):
    last_rec = await get_last_list_item(f"user:risks:{user_id}")
    if last_rec:
        last_rec = json.loads(last_rec)
        risk_key = f"risk {risk_id}"
        return last_rec.get(risk_key)
    return None


async def already_generated_for_range(user_id: int, range_id: str) -> bool:
    key = f"user:{user_id}:generated:{range_id}"
    return await r.exists(key) == 1


async def mark_as_generated_for_range(user_id: int, range_id: str):
    key = f"user:{user_id}:generated:{range_id}"
    await r.set(key, "1")


async def instruction_added_for_range(user_id: int, range_id: str) -> bool:
    key = f"user:{user_id}:instruction_added:{range_id}"
    return await r.exists(key) == 1


async def mark_instruction_added_for_range(user_id: int, range_id: str):
    key = f"user:{user_id}:instruction_added:{range_id}"
    await r.set(key, "1")


async def instruction_triggered_for_range(user_id: int, range_id: str):
    key = f"user:{user_id}:instruction_triggered:{range_id}"
    return await r.exists(key) == 1


async def mark_instruction_triggered_for_range(user_id: int, range_id: str):
    key = f"user:{user_id}:instruction_triggered:{range_id}"
    await r.set(key, "1")


###########################
# Background tasks
###########################

async def calculate_health_score(user_metrics: Dict[str, Any], base_metrics: Dict[str, Any]) -> int:
    def is_valid_and_weight_ok(key: str, data: dict) -> bool:
        """
        Возвращает True, если значение по ключу key заполнено (для строки – непустое, для чисел – не 0, для списка – не пустой)
        и, если существует соответствующий вес (ключ key + "_weight"), то его значение (приведённое к float)
        не меньше 0.7.
        """
        value = data.get(key)
        # Проверка заполненности поля
        if isinstance(value, str):
            if value.strip() == "" or value.lower() == "null":
                return False
        elif isinstance(value, list):
            if len(value) == 0:
                return False
        elif isinstance(value, (int, float)):
            if value == 0:
                return False
        elif value is None:
            return False

        # Если есть поле веса, проверяем его
        weight_key = key + "_weight"
        if weight_key in data:
            try:
                weight_value = float(data.get(weight_key))
            except (ValueError, TypeError):
                return False
            if weight_value < 0.7:
                return False
        return True

    def compare_fields(user_data: Any, base_data: Any) -> int:
        score = 0
        # Если у нас словари – пробегаемся по ключам, исключая ключи с суффиксом _weight
        if isinstance(base_data, dict) and isinstance(user_data, dict):
            for key, base_value in base_data.items():
                if key.endswith("_weight"):
                    continue  # пропускаем весовые поля
                if key.endswith("_field_name"):
                    continue  # пропускаем именные поля
                if key not in user_data:
                    continue
                if key == "name":
                    continue
                # Если значение само по себе – словарь, рекурсивно вычисляем сумму баллов
                if isinstance(base_value, dict) and isinstance(user_data.get(key), dict):
                    score += compare_fields(user_data.get(key), base_value)
                else:
                    if is_valid_and_weight_ok(key, user_data):
                        score += 1
        # Для списков – если список не пустой, считаем за 1 балл (без проверки веса)
        elif isinstance(base_data, list) and isinstance(user_data, list):
            if len(user_data) > 0:
                score += 1
        # Для строк и чисел (если мы попали сюда, то оценка проводится без сопутствующего веса)
        elif isinstance(base_data, str):
            if isinstance(user_data, str) and user_data.strip() != "" and user_data.lower() != "null":
                score += 1
        elif isinstance(base_data, (int, float)):
            if isinstance(user_data, (int, float)) and user_data != 0:
                score += 1
        return score

    total_score = compare_fields(user_metrics, base_metrics)
    return total_score


def is_filled(value: Any) -> bool:
    if isinstance(value, str):
        return value.strip() != "" and value.lower() != "null"
    elif isinstance(value, list):
        return len(value) > 0
    elif isinstance(value, (int, float)):
        return value != 0
    return value is not None

def clean_llm_json(raw: str) -> dict:
    """
    Принимает строку от LLM; возвращает dict.
    Убирает обрамляющие ```json … ```, комментарии и прочий мусор.
    """
    # забираем самый первый {...}
    m = re.search(r"\{.*\}", raw, re.S)
    if not m:
        raise ValueError("No JSON found in LLM output")
    return json.loads(m.group(0))


async def handle_pending_field_answer(
    user_id: int,
    pending_field: str,
    user_answer: str,
    history: list,
    metrics_dict: dict
) -> dict:
    """
    Автономная HPA: один вызов unified-pipeline, обновление метрик,
    пересчёт score, и вычисление следующего pending_field через списки.
    Лимиты на переспрос и префиксы/итоги по блокам.
    """
    final_added = await r.get(f"user:{user_id}:final_added")
    resp = {
        "metrics_update": None,
        "action": {
            "needs_clarification": False,
            "clarification_question": ""
        },
        "pending_field": "allergies"
    }
    if not final_added:
        fixed_keys = [
            "name", "gender", "date_of_birth", "height", "weight",
            "activity_level", "food_level", "smoking", "alcohol",
            "sleep_recovery", "stress_level", "chronic_conditions", "allergies"
        ]
        sports_str = await r.get(f"user:{user_id}:shuffled_sports_keys")
        sports_keys = json.loads(sports_str) if sports_str else []

        # объединяем все поля
        all_keys = fixed_keys + sports_keys

        # строим список только незаполненных или с малым весом метрик
        questions_order = []
        for k in all_keys:
            # находим секцию в metrics_dict, где хранится k
            section = next(
                (sec for sec in metrics_dict.values() if isinstance(sec, dict) and k in sec),
                {}
            )
            val = section.get(k)
            wt = section.get(f"{k}_weight", 0.0)
            # если нет значения или вес < 0.7 — добавляем в очередь
            if not is_filled(val) or wt < 0.7:
                questions_order.append(k)

        # --- 2) Работа с счётчиком переспросов ---
        reask_key = f"user:{user_id}:reask_count:{pending_field}"
        reask_count = int(await r.get(reask_key) or 0)
        reask_counts = {}
        for k in questions_order:
            reask_counts[k] = int(await r.get(f"user:{user_id}:reask_count:{k}") or 0)

        # --- 1) Подготовка и единый вызов LLM ---
        unified_input = {
            "METRIC_NAME": pending_field,
            "METRIC_QUESTION": questions_first_stage[pending_field],
            "METRIC_ANSWER": user_answer,
            "REASK_COUNTS": reask_counts,
            "METRIC_FORMAT": metrics_format[pending_field],
            "USER_HEALTH_PROFILE": metrics_dict,
            "CHAT_HISTORY": history,
            "QUESTIONS_ORDER": questions_order
        }
        raw = await run_chatgpt_unified_question_pipeline({"unified_input": unified_input})
        resp = clean_llm_json(raw)

        # --- 3) Обработка metrics_update ---
        if upd := resp.get("metrics_update"):
            patch = {
                upd["metric_key"]: upd["metric_value"],
                f"{upd['metric_key']}_weight": upd["metric_weight"]
            }
            merged = merge_metrics(metrics_dict, patch)
            final_json = json.dumps(merged, ensure_ascii=False)
            # Сохраняем метрики
            await r.delete(f"user:metrics:{user_id}")
            await asyncio.gather(
                add_metric(user_id, final_json),
                update_health_profile(user_id, BEARER_TOKEN, final_json)
            )

            compare_metrics = await get_metrics(user_id)
            score = await calculate_health_score(json.loads(compare_metrics), json.loads(TEST_METRICS))

            # Удаляем ключи весов одним вызовом, если они есть
            keys_to_delete = [key async for key in r.scan_iter(f"user:weights:{user_id}")]
            if keys_to_delete:
                await r.delete(*keys_to_delete)
            await add_user_weights(user_id, score)

            async def generate_and_upload(conf_recommendations, conf_risks):
                delete_task = asyncio.gather(
                    delete_user_recommendations_from_db(BEARER_TOKEN, user_id),
                    delete_user_risks_from_db(BEARER_TOKEN, user_id),
                )
                rec_pipeline = asyncio.create_task(run_perplexity_pipeline(conf_recommendations))
                risk_pipeline = asyncio.create_task(run_perplexity_pipeline(conf_risks))
                await delete_task
                raw_recs, raw_risks = await asyncio.gather(rec_pipeline, risk_pipeline)
                rec_dict = json.loads(raw_recs.replace("'", '"'))
                risk_dict = json.loads(raw_risks.replace("'", '"'))
                tasks = []
                for rec in rec_dict.values():
                    rec_json = json.dumps(rec)
                    tasks.append(add_recommendation_local(user_id, rec_json))
                    tasks.append(upload_recommendation(user_id, rec, BEARER_TOKEN))
                for risk in risk_dict.values():
                    risk_json = json.dumps(risk)
                    tasks.append(add_risk_local(user_id, risk_json))
                    tasks.append(upload_risk(user_id, risk, BEARER_TOKEN))
                await asyncio.gather(*tasks)

            # Выбор диапазона по баллам и генерация рекомендаций/рисков
            if 12 <= score <= 18:
                range_id = "12-18"
            elif 19 <= score <= 25:
                range_id = "19-25"
            elif 26 <= score <= 31:
                range_id = "26-31"
            else:
                range_id = None
            print("КОНЕЦ ЗАДАНИЯ НА АНАЛИЗ МЕТРИК, ВРЕМЯ:", datetime.now())
            if range_id and not await already_generated_for_range(user_id, range_id):
                print("НАЧАЛО ЗАДАНИЯ НА ГЕНЕРАЦИЮ РИСКОВ И РЕКОММЕНДАЦИЙ, ВРЕМЯ:", datetime.now())
                goals, metrics_str = await asyncio.gather(get_goals(user_id), get_metrics(user_id))
                conf_recommendations = {"healthProfile": metrics_str, "prompt": RECOMMENDATIONS_PROMPT, "goals": goals}
                conf_risks = {"healthProfile": metrics_str, "prompt": RISKS_PROMPT, "goals": goals}
                await asyncio.gather(
                    generate_and_upload(conf_recommendations, conf_risks),
                    mark_as_generated_for_range(user_id, range_id),
                )
                print("КОНЕЦ ЗАДАНИЯ НА ГЕНЕРАЦИЮ РИСКОВ И РЕКОММЕНДАЦИЙ, ВРЕМЯ:", datetime.now())

        # --- 4) Решение: уточнить или идти дальше ---
        needs_clarify = resp.get("action", {}).get("needs_clarification", False)
        prefix = ""

        # если просим уточнить и не превысили лимит
        if needs_clarify:
            if reask_count < 2:
                await r.incr(reask_key)
            else:
                # превысили лимит: пропускаем поле
                prefix = "Поле осталось незаполненным. Ничего, вернемся к вопросу позже.\n&#8203;\n\n&#8203;\n\n"
                needs_clarify = False
            # оставляем pending_field без изменений
            next_field = pending_field
        else:
            # Ответ принят или исчерпан лимит уточнений
            # CASE A: есть upd + это не первый ход (был хотя бы один reask) → благодарим
            if upd and reask_count > 0 and upd.get("metric_value") and upd.get("metric_key") != "name":
                prefix = f"Спасибо за ответ, записал: {upd['metric_value']}. \n\n"

            # CASE B: исчерпан лимит уточнений и upd нет или пустая строка → skip
            elif reask_count >= 2 and (not upd or not upd.get("metric_value")) and upd.get("metric_key") != "name":
                prefix = "Поле осталось незаполненным. Ничего, вернемся к вопросу позже.\n\n"
            # вычисляем следующий field
            fixed = ["name","gender","date_of_birth","height","weight",
                     "activity_level","food_level","smoking","alcohol",
                     "sleep_recovery","stress_level","chronic_conditions","allergies"]
            sports_str = await r.get(f"user:{user_id}:shuffled_sports_keys")
            sports = json.loads(sports_str) if sports_str else []
            all_keys = fixed + sports
            idx_map = {k: i for i, k in enumerate(all_keys)}
            cur_idx = idx_map.get(pending_field, -1)
            next_field = None
            for k in all_keys[cur_idx+1:]:
                # проверяем в merged (metrics_dict пока не перезаписан, но значение из upd уже применилось логически)
                sec = next((s for s in merged.values() if isinstance(s, dict) and k in s), None)
                if sec:
                    val = sec[k]
                    wt = get_weight_value(sec.get(f"{k}_weight"))
                    if not is_filled(val) or wt < 0.7:
                        next_field = k
                        break
            # итоги по блокам
            if pending_field == "smoking":
                prefix += "Отлично! Вы ответили на **7** вопросов из **12**, осталось всего **5**! \n&#8203;\n\n&#8203;\n\n"
            elif sports and pending_field == sports[len(sports)//2]:
                prefix += "Супер! Пройдена половина второго блока анкеты. Осталось совсем немного! \n&#8203;\n\n&#8203;\n\n"

        # --- 5) Сохраняем pending_field ---
        await r.delete(f"user:{user_id}:pending_field")
        if next_field:
            await r.set(f"user:{user_id}:pending_field", next_field)

        # --- 6) Добавляем префикс к вопросу ---
        if resp.get("action", {}).get("clarification_question"):
            resp["action"]["clarification_question"] = prefix + resp["action"]["clarification_question"]
        # Обновляем поле в ответе для клиента
        resp["pending_field"] = next_field

        user_goals = await r.lrange(f"user:goals:{user_id}", 0, -1)
        allergies_weight = json.loads(final_json)['health_status']['allergies_weight']
        reask_allergies = await r.get(f"user:{user_id}:reask_count:allergies")
        if upd.get("metric_value") and user_goals and upd.get("metric_key") == str(sports_keys[-1]):
            completion = generate_completion_summary(metrics_dict)
            if not final_added:
                await r.set(f"user:{user_id}:final_added", "1")
                if completion == True:  # Если заполнена анкета
                    conf_message = {"prompt": f"{FINAL_PROMPT_COMPLETED}"}
                    # "prompt": f"{CONVERSATION_PROMPT}. ТВОЯ ТЕКУЩАЯ ЗАДАЧА, это ПЕРЕФРАЗИРОВАТЬ данные слова, сохранив все цифры: {addition}."}
                else:
                    conf_message = {"prompt": f"{FINAL_PROMPT_NOT_COMPLETED}"}
                msg = await run_chatgpt_chat_open_pipeline(conf_message)
                resp["action"]["clarification_question"] = msg
                await set_chatting_mode(user_id, "conversation")
                return resp
        if not reask_allergies:
            reask_allergies = "0"
        if not user_goals and (str(allergies_weight) in ["0.7", "0.8", "0.9", "1.0", "1"] or int(reask_allergies) >= 3):
            await set_awaiting_goals(user_id)
            first_seven_fields = [
                "gender",
                "date_of_birth",
                "height",
                "weight",
                "activity_level",
                "food_level",
                "smoking",
                "alcohol",
                "sleep_recovery",
                "stress_level",
                "chronic_conditions",
                "allergies"
            ]
            addition_parts = []
            metrics = await get_metrics(user_id)
            user_metrics_dict = json.loads(str(metrics))
            for field in first_seven_fields:
                field_display = None
                field_value = None
                for section in user_metrics_dict.values():
                    if isinstance(section, dict) and field in section:
                        field_value = section[field]
                        field_display = section.get(field + "_field_name", field)
                        break
                # Если значение не заполнено или пустая строка – подставляем "Не заполнено"
                if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
                    field_value = "Не заполнено"
                # Если значение — список, преобразуем его в строку через запятую
                elif isinstance(field_value, list):
                    if len(field_value) == 0:
                        field_value = "Не заполнено"
                    else:
                        field_value = ", ".join(str(item) for item in field_value)
                addition_parts.append(f" - {field_display}: {field_value}")

            addition = "\n".join(addition_parts)
            resp["action"]["clarification_question"] = ("**Отличная работа! Записал:** \n" + addition + "\n&#8203;\n\n&#8203;\n\nТеперь, когда у нас есть все необходимые данные о тебе, **расскажи, каких целей ты хочешь достичь в сфере здоровья.** \n&#8203;\nЭто может быть улучшение физических показателей, "
                                                                    "преодоление вредных привычек или решение существующих проблем со здоровьем.")
    else:
        first_seven_fields = [
            "name", "gender", "date_of_birth", "height", "weight", "activity_level",
            "food_level", "smoking", "alcohol", "sleep_recovery", "stress_level", "chronic_conditions",
            "allergies", "sports", "regular_medicine", "genetic_conditions", "sleep_quality",
            "injuries", "diet_balance", "calorie_intake", "sleep_schedule", "work_schedule",
            "blood_pressure", "preferred_dishes", "diet_schedule", "food_intolerances",
            "blood_analysis", "biochemical_analysis", "urine_analysis", "lipid_profile", "glucose_tolerance_test",
            "thyroid_test", "glycated_hemoglobin", "coagulogram", "inflammatory_markers"
        ]
        goals = await get_goals(user_id) or ""
        metrics = await get_metrics(user_id)
        user_metrics_dict = json.loads(str(metrics))
        addition_parts = []
        for field in first_seven_fields:
            field_display = None
            field_value = None
            for section in user_metrics_dict.values():
                if isinstance(section, dict) and field in section:
                    field_value = section[field]
                    field_display = section.get(field + "_field_name", field)
                    break
            # Если значение не заполнено или пустая строка – подставляем "Не заполнено"
            if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
                field_value = "Не заполнено"
            # Если значение — список, преобразуем его в строку через запятую
            elif isinstance(field_value, list):
                if len(field_value) == 0:
                    field_value = "Не заполнено"
                else:
                    field_value = ", ".join(str(item) for item in field_value)
            addition_parts.append(f" - {field_display}: {field_value}")
        current_date = datetime.now().strftime("%Y:%m:%d")
        addition = f" ЦЕЛИ: {str(goals)}, " + f"ДАТА НА СЕГОДНЯ, КОТОРУЮ НАДО УЧИТЫВАТЬ, ДЛЯ ЛУЧШЕГО ПОНИМАНИЯ ВРЕМЕННЫХ РАМОК: {current_date}, " + f"КОНТЕКСТ АНКЕТЫ ЗДОРОВЬЯ:" + "\n".join(
            addition_parts) + """
                                                7. ПРИОРИТЕТЫ КОНТЕКСТА
                                                Всегда сначала обращай внимание нарисковые и отклоняющиеся параметры(давление, пульс, сахар, генетика, тревожность, ИМТ > 30).
                                                Затем наосновные физиологические данные(рост, вес, возраст, пол).
                                                Далее — наобраз жизни и поведение(сон, питание, активность, стресс).
                                                Затем — нацели и намерения пациента.
                                                В последнюю очередь — на эмоциональные маркеры и фон.
                                                Если пациент упомянул конкретную метрику,сфокусируйся на ней, но можешь деликатно упомянуть одну-две связанные метрики из более высокого приоритета, если они критичны.
                                                Не дублируй, не уточняй и не проси метрики напрямую, но можешь корректно подводить к обсуждению важных параметров, если они критичны для здоровья.
                                                """
        # Fetch last 10 system messages and last 9 user messages
        user_msgs_task = get_last_user_messages(user_id, "general", '', count=10)
        system_msgs_task = get_last_system_messages(user_id, "general", '', count=10)
        user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)

        history = []
        if system_messages:
            # Add the initial system message (e.g. greeting)
            history.append({"role": "assistant", "content": system_messages[0]})
        # For remaining messages, pair each system message with its preceding user message
        for i in range(1, len(system_messages)):
            if i - 1 < len(user_messages):
                history.append({"role": "user", "content": user_messages[i - 1]})
            history.append({"role": "assistant", "content": system_messages[i]})
        # Finally, add the latest user message
        history.append({"role": "user", "content": user_answer})
        conf_message = {
            "usr_msg": user_answer,
            "prompt": CONVERSATION_PROMPT + addition + f" ВОТ ИСТОРИЯ СООБЩЕНИЙ: {history}"
        }
        await set_chatting_mode(user_id, "conversation")
        resp["action"]["clarification_question"] = await run_chatgpt_message_pipeline(conf_message)

    return resp

def _find_next_field(
    all_keys: List[str],
    current: str,
    metrics: dict
) -> Optional[str]:
    """
    Ищет в all_keys после current первый незаполненный
    или с низким весом (<0.7) и возвращает его.
    """
    idx_map = {k: i for i, k in enumerate(all_keys)}
    start = idx_map.get(current, -1) + 1
    for k in all_keys[start:]:
        section = next((s for s in metrics.values() if isinstance(s, dict) and k in s), None)
        if not section:
            continue
        val = section[k]
        wt = get_weight_value(section.get(f"{k}_weight"))
        if not is_filled(val) or wt < 0.7:
            return k
    return None

def get_weight_value(weight_field: Any) -> float:
    if not weight_field:
        return 0.0
    if isinstance(weight_field, str) and weight_field.lower() == "null":
        return -1.0
    try:
        return float(weight_field)
    except (ValueError, TypeError):
        return 0.0


def generate_completion_summary(user_metrics: dict) -> bool:
    sections = {
        'personal_information': 'Личная информация',
        'lifestyle': 'Образ жизни',
        'health_status': 'Состояние здоровья',
        'nutrition': 'Питание'
    }
    for section_key in sections:
        section_data = user_metrics.get(section_key, {})
        for field, value in section_data.items():
            if field.endswith('_name') or field.endswith('_weight'):
                continue
            if value == "":
                return False
    return True


async def find_first_unfilled_question(
        user_id: int,
        questions_dict: Dict[str, str],
        message: str,
        sys_msg_for_dict: str,
        history
) -> Optional[str]:
    """
    Улучшенная функция поиска первого незаполненного (или недостаточно уверенного) поля.
    Выполняет единожды запрос метрик и переиспользует полученный JSON.
    """
    metrics_str = await get_metrics(user_id)
    user_metrics = json.loads(metrics_str)

    # Определяем нужный порядок вопросов:
    fixed_keys_order = [
        "name", "gender", "date_of_birth", "height", "weight",
        "activity_level", "food_level", "smoking", "alcohol",
        "sleep_recovery", "stress_level", "chronic_conditions", "allergies"
    ]
    sports_keys_random_str = await r.get(f"user:{user_id}:shuffled_sports_keys")
    sports_keys_random = json.loads(sports_keys_random_str) if sports_keys_random_str else []

    all_question_stages = [fixed_keys_order, sports_keys_random]

    # ### КЛЮЧЕВОЕ ИЗМЕНЕНИЕ: Единый цикл по всем этапам ###
    for stage_idx, question_keys in enumerate(all_question_stages):
        for key_idx, key in enumerate(question_keys):
            question_text = questions_first_stage.get(key)
            if not question_text:
                continue

            # Поиск значения и веса в текущих метриках
            field_value, field_weight = None, None
            for section_data in user_metrics.values():
                if isinstance(section_data, dict) and key in section_data:
                    field_value = section_data.get(key)
                    field_weight = section_data.get(key + "_weight")
                    break

            reask_key = f"user:{user_id}:reask_count:{key}"
            reask_count = int(await r.get(reask_key) or 0)
            weight_val = get_weight_value(field_weight)

            is_unfilled = not is_filled(field_value)
            is_unclear = is_filled(field_value) and weight_val < 0.7

            # --- Логика переспроса при нечетком ответе ---
            if is_unclear and reask_count <= 2:
                # ### КЛЮЧЕВОЕ ИЗМЕНЕНИЕ ###
                # Вместо ручного сбора промпта, мы формируем единый запрос к новому пайплайну
                # Он сам решит, как лучше переспросить, основываясь на всей информации.

                await r.incr(reask_key)  # Увеличиваем счетчик переспросов

                # Последний ответ пользователя, который был нечетким
                last_user_answer = await get_last_list_item(f"user:messages-general:{user_id}")

                unified_input = {
                    "task_type": "clarify",  # Указываем, что задача - уточнить
                    "current_question_key": str(key),
                    "current_question_text": str(question_text),
                    "user_answer": str(last_user_answer),
                    "user_health_profile": str(user_metrics),
                    "chat_history": str(history)
                }

                # Вызываем новый единый пайплайн
                unified_config = {"unified_input": unified_input}
                generated_question = await run_chatgpt_unified_question_pipeline(unified_config)
                return generated_question

            # --- Логика для первого незаполненного вопроса ---
            if is_unfilled and reask_count == 0:
                # ### КЛЮЧЕВОЕ ИЗМЕНЕНИЕ ###
                # Генерация нового вопроса также происходит через единый пайплайн.
                # Это позволяет делать его более контекстным и разнообразным.
                await r.incr(reask_key)
                await r.set(f"user:{user_id}:pending_field", key)

                prefix = ""  # Логика префиксов остается
                prev_field = await r.get(f"user:{user_id}:last_pending_field")
                if prev_field:
                    prev_reask = int(await r.get(f"user:{user_id}:reask_count:{prev_field}") or "0")
                    if prev_reask > 1 and reask_count >= 3:  # Если предыдущий вопрос был пропущен после нескольких переспросов
                        prefix = "Поле осталось незаполненным. Ничего, вернемся к вопросу позже.\n&#8203;\n\n&#8203;\n\n"
                await r.set(f"user:{user_id}:last_pending_field", key)

                # Собираем данные для генерации нового вопроса
                unified_input = {
                    "task_type": "ask_new",  # Указываем, что задача - задать новый вопрос
                    "current_question_key": str(key),
                    "current_question_text": str(question_text),
                    "user_health_profile": str(user_metrics),
                    "chat_history": str(history)
                }

                # Проверяем, нужно ли добавить промежуточный итог
                summary_message = ""
                # Онбординг: после 7 вопросов (ключ "alcohol" - 8-й по счету)
                if stage_idx == 0 and key == "alcohol":
                    summary_message = "Отлично! Вы ответили на **7** вопросов из **12**, осталось всего **5**! \n&#8203;\n\n&#8203;\n\n"
                    # Тут можно добавить логику отображения уже заполненных полей

                # Пост-онбординг: на середине списка
                if stage_idx == 1 and key_idx == (len(question_keys) // 2):
                    summary_message = "Супер! Пройдена половина второго блока анкеты. Осталось совсем немного! \n&#8203;\n\n&#8203;\n\n"

                unified_config = {"unified_input": unified_input}
                generated_question = await run_chatgpt_unified_question_pipeline(unified_config)
                generated_question = json.loads(generated_question)
                generated_question["action"]["clarification_question"] = prefix + summary_message + generated_question["action"]["clarification_question"]
                return generated_question


    await set_chatting_mode(user_id, "conversation")
    goals = await get_goals(user_id) or ""
    user_msgs_task = get_last_user_messages(user_id, "general", '', count=10)
    system_msgs_task = get_last_system_messages(user_id, "general", '', count=10)
    user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)
    final_added = await r.get(f"user:{user_id}:final_added")
    completion = generate_completion_summary(user_metrics)
    # addition = (" Карта здоровья заполнена достаточно подробно.\n\n"
    #             f"{completion}"
    #             " Biome Ai может давать наиболее персонализированные и точные рекомендации.\n\n"
    #             " - Вы также можете добавить результаты своих анализов в формате PNG, HEIC (HEIF) или JPEG (JPG). Biome внимательно их изучит, чтобы сделать рекомендации ещё точнее!\n\n"
    #             " - Ты можешь в любое время обращаться с любыми вопросами о своем здоровье и образе жизни — мы всегда на связи!\n\n"
    #             " - А еще вы можете оценить калорийность вашей еды по фотографии. Просто загрузите ее в чат и попросите оценить калорийность.")
    msg = ""
    if not final_added:
        await r.set(f"user:{user_id}:final_added", "1")
        if completion == True:  # Если заполнена анкета
            conf_message = {"prompt": f"{FINAL_PROMPT_COMPLETED}"}
            # "prompt": f"{CONVERSATION_PROMPT}. ТВОЯ ТЕКУЩАЯ ЗАДАЧА, это ПЕРЕФРАЗИРОВАТЬ данные слова, сохранив все цифры: {addition}."}
        else:
            conf_message = {"prompt": f"{FINAL_PROMPT_NOT_COMPLETED}"}
        msg = await run_chatgpt_chat_open_pipeline(conf_message)
    else:
        first_seven_fields = [
            "name", "gender", "date_of_birth", "height", "weight", "activity_level",
            "food_level", "smoking", "alcohol", "sleep_recovery", "stress_level", "chronic_conditions",
            "allergies", "sports", "regular_medicine", "genetic_conditions", "sleep_quality",
            "injuries", "diet_balance", "calorie_intake", "sleep_schedule", "work_schedule",
            "blood_pressure", "preferred_dishes", "diet_schedule", "food_intolerances",
            "blood_analysis", "biochemical_analysis", "urine_analysis", "lipid_profile", "glucose_tolerance_test",
            "thyroid_test", "glycated_hemoglobin", "coagulogram", "inflammatory_markers"
        ]
        metrics = await get_metrics(user_id)
        user_metrics_dict = json.loads(str(metrics))
        addition_parts = []
        for field in first_seven_fields:
            field_display = None
            field_value = None
            for section in user_metrics_dict.values():
                if isinstance(section, dict) and field in section:
                    field_value = section[field]
                    field_display = section.get(field + "_field_name", field)
                    break
            # Ели значение не заполнено или пустая строка – подставляем "Не заполнено"
            if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
                field_value = "Не заполнено"
            # Если значение — список, преобразуем его в строку через запятую
            elif isinstance(field_value, list):
                if len(field_value) == 0:
                    field_value = "Не заполнено"
                else:
                    field_value = ", ".join(str(item) for item in field_value)
            addition_parts.append(f" - {field_display}: {field_value}")
        current_date = datetime.now().strftime("%Y:%m:%d")
        addition = f" ЦЕЛИ: {str(goals)}, " + f"ДАТА НА СЕГОДНЯ, КОТОРУЮ НАДО УЧИТЫВАТЬ, ДЛЯ ЛУЧШЕГО ПОНИМАНИЯ ВРЕМЕННЫХ РАМОК: {current_date}, " + f"КОНТЕКСТ АНКЕТЫ ЗДОРОВЬЯ:" + "\n".join(
            addition_parts) + """
                                    7. ПРИОРИТЕТЫ КОНТЕКСТА
                                    Всегда сначала обращай внимание нарисковые и отклоняющиеся параметры(давление, пульс, сахар, генетика, тревожность, ИМТ > 30).
                                    Затем наосновные физиологические данные(рост, вес, возраст, пол).
                                    Далее — наобраз жизни и поведение(сон, питание, активность, стресс).
                                    Затем — нацели и намерения пациента.
                                    В последнюю очередь — на эмоциональные маркеры и фон.
                                    Если пациент упомянул конкретную метрику,сфокусируйся на ней, но можешь деликатно упомянуть одну-две связанные метрики из более высокого приоритета, если они критичны.
                                    Не дублируй, не уточняй и не проси метрики напрямую, но можешь корректно подводить к обсуждению важных параметров, если они критичны для здоровья.
                                    """
        # Fetch last 10 system messages and last 9 user messages
        user_msgs_task = get_last_user_messages(user_id, "general", '', count=10)
        system_msgs_task = get_last_system_messages(user_id, "general", '', count=10)
        user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)

        history = []
        if system_messages:
            # Add the initial system message (e.g. greeting)
            history.append({"role": "assistant", "content": system_messages[0]})
        # For remaining messages, pair each system message with its preceding user message
        for i in range(1, len(system_messages)):
            if i - 1 < len(user_messages):
                history.append({"role": "user", "content": user_messages[i - 1]})
            history.append({"role": "assistant", "content": system_messages[i]})
        # Finally, add the latest user message
        history.append({"role": "user", "content": message})
        conf_message = {
            "usr_msg": message,
            "prompt": CONVERSATION_PROMPT + addition + f" ВОТ ИСТОРИЯ СООБЩЕНИЙ: {history}"
        }
        msg = await run_chatgpt_message_pipeline(conf_message)
    return msg


def merge_metrics(original: dict, updates: dict) -> dict:
    """
    Обновляет оригинальный двухуровневый JSON значениями из одноуровневого JSON,
    учитывая веса: новое значение применяется только если его вес >= существующего.
    Для массивов строк — объединяем списки, избегая дубликатов.
    """
    def get_weight(val):
        try:
            return float(val)
        except (TypeError, ValueError):
            return 0.0

    # Пробегаем по обновлениям
    for metric_key, updated_value in updates.items():
        # пропускаем поля веса
        if metric_key.endswith("_weight"):
            continue

        # вес обновления
        update_weight = get_weight(updates.get(f"{metric_key}_weight"))

        # ищем это поле в оригинальной карте
        for section in original.values():
            if not isinstance(section, dict) or metric_key not in section:
                continue

            current_weight = get_weight(section.get(f"{metric_key}_weight"))

            # только если новый вес >= старого — принимаем обновление
            if update_weight >= current_weight:
                if isinstance(updated_value, list):
                    # объединим массивы, убрав дубликаты
                    existing = section.get(metric_key) or []
                    merged = existing.copy()
                    for item in updated_value:
                        if item not in merged:
                            merged.append(item)
                    section[metric_key] = merged
                else:
                    # просто заменяем
                    section[metric_key] = updated_value
                # обновляем вес
                section[f"{metric_key}_weight"] = update_weight

            break

    return original



async def process_metrics():
    while True:
        try:
            # ждём данных из любой очереди
            queue_bytes, raw_bytes = await r.blpop(["metrics_updates", "pending_metrics"])

            queue = queue_bytes if isinstance(queue_bytes, str) else queue_bytes.decode()
            raw = raw_bytes if isinstance(raw_bytes, str) else raw_bytes.decode()

            if queue == "metrics_updates":
                upd = json.loads(raw)

                user_id = upd["user_id"]
                key = upd["metric_key"]
                value = upd["metric_value"]
                weight = float(upd["metric_weight"])

                current = json.loads(await get_metrics(user_id) or "{}")

                # пишем в любую подходящую секцию (health_status – default)
                placed = False
                for sect in current.values():
                    if isinstance(sect, dict) and key in sect:
                        sect[key] = value
                        sect[f"{key}_weight"] = weight
                        placed = True
                        break
                if not placed:
                    current.setdefault("health_status", {})
                    current["health_status"][key] = value
                    current["health_status"][f"{key}_weight"] = weight
            elif queue == "pending_metrics":
                print("НАЧАЛО ЗАДАНИЯ НА АНАЛИЗ МЕТРИК ПОЛУЧЕНО, ВРЕМЯ:", datetime.now())
                task = json.loads(raw)
                user_id = task["user_id"]
                current_metrics = await r.rpop(f"user:metrics:{user_id}")
                if not current_metrics:
                    current_metrics = TEST_METRICS

                conf_metrics = {"usr_msg": task["message"], "prompt": METRICS_PROMPT}
                metrics_patch = (await run_chatgpt_metrics_pipeline(conf_metrics)).replace("'", '"')
                try:
                    patch_dict = json.loads(metrics_patch)
                except json.JSONDecodeError:
                    patch_dict = {}
                current_metrics_dict = json.loads(current_metrics)
                merged = merge_metrics(current_metrics_dict, patch_dict)
                final_metrics_json = json.dumps(merged, ensure_ascii=False)

                await asyncio.gather(
                    add_metric(user_id, final_metrics_json),
                    update_health_profile(user_id, BEARER_TOKEN, final_metrics_json)
                )

                compare_metrics = await get_metrics(user_id)
                score = await calculate_health_score(json.loads(compare_metrics), json.loads(TEST_METRICS))

                # Удаляем ключи весов одним вызовом, если они есть
                keys_to_delete = [key async for key in r.scan_iter(f"user:weights:{user_id}")]
                if keys_to_delete:
                    await r.delete(*keys_to_delete)
                await add_user_weights(user_id, score)

                async def generate_and_upload(conf_recommendations, conf_risks):
                    delete_task = asyncio.gather(
                        delete_user_recommendations_from_db(BEARER_TOKEN, user_id),
                        delete_user_risks_from_db(BEARER_TOKEN, user_id),
                    )
                    rec_pipeline = asyncio.create_task(run_perplexity_pipeline(conf_recommendations))
                    risk_pipeline = asyncio.create_task(run_perplexity_pipeline(conf_risks))
                    await delete_task
                    raw_recs, raw_risks = await asyncio.gather(rec_pipeline, risk_pipeline)
                    rec_dict = json.loads(raw_recs.replace("'", '"'))
                    risk_dict = json.loads(raw_risks.replace("'", '"'))
                    tasks = []
                    for rec in rec_dict.values():
                        rec_json = json.dumps(rec)
                        tasks.append(add_recommendation_local(user_id, rec_json))
                        tasks.append(upload_recommendation(user_id, rec, BEARER_TOKEN))
                    for risk in risk_dict.values():
                        risk_json = json.dumps(risk)
                        tasks.append(add_risk_local(user_id, risk_json))
                        tasks.append(upload_risk(user_id, risk, BEARER_TOKEN))
                    await asyncio.gather(*tasks)

                # Выбор диапазона по баллам и генерация рекомендаций/рисков
                if 12 <= score <= 18:
                    range_id = "12-18"
                elif 19 <= score <= 25:
                    range_id = "19-25"
                elif 26 <= score <= 31:
                    range_id = "26-31"
                else:
                    range_id = None
                print("КОНЕЦ ЗАДАНИЯ НА АНАЛИЗ МЕТРИК, ВРЕМЯ:", datetime.now())
                if range_id and not await already_generated_for_range(user_id, range_id):
                    print("НАЧАЛО ЗАДАНИЯ НА ГЕНЕРАЦИЮ РИСКОВ И РЕКОММЕНДАЦИЙ, ВРЕМЯ:", datetime.now())
                    goals, metrics_str = await asyncio.gather(get_goals(user_id), get_metrics(user_id))
                    conf_recommendations = {"healthProfile": metrics_str, "prompt": RECOMMENDATIONS_PROMPT, "goals": goals}
                    conf_risks = {"healthProfile": metrics_str, "prompt": RISKS_PROMPT, "goals": goals}
                    await asyncio.gather(
                        generate_and_upload(conf_recommendations, conf_risks),
                        mark_as_generated_for_range(user_id, range_id),
                    )
                    print("КОНЕЦ ЗАДАНИЯ НА ГЕНЕРАЦИЮ РИСКОВ И РЕКОММЕНДАЦИЙ, ВРЕМЯ:", datetime.now())
            else:
                continue
        except Exception as e:
            print(f"Error processing metrics: {e}")


async def process_risks():
    while True:
        try:
            _, task_json = await r.brpop("pending_risks")
            if task_json:
                task = json.loads(task_json)
                user_id = task["user_id"]

                goals = await get_goals(user_id)
                metrics = await get_metrics(user_id)

                # Delete old risks
                await delete_user_risks_from_db(BEARER_TOKEN, user_id)
                async for key in r.scan_iter(f"user:risks:{user_id}"):
                    await r.delete(key)

                # Generate risks
                conf_risks = {"healthProfile": metrics, "prompt": RISKS_PROMPT, "goals": goals}
                risks = await run_perplexity_pipeline(conf_risks)
                risks = json.loads(risks.replace("'", '"'))

                for _, risk in risks.items():
                    await add_risk_local(user_id, json.dumps(risk))
                    await upload_risk(user_id, risk, BEARER_TOKEN)

        except Exception as e:
            print(f"Error processing risks: {e}")


async def process_recommendations():
    while True:
        try:
            _, task_json = await r.brpop("pending_recommendations")
            if task_json:
                task = json.loads(task_json)
                user_id = task["user_id"]

                goals = await get_goals(user_id)
                metrics = await get_metrics(user_id)

                # Delete old recommendations/risks
                await delete_user_recommendations_from_db(BEARER_TOKEN, user_id)
                async for key in r.scan_iter(f"user:recommendations:{user_id}"):
                    await r.delete(key)

                # Generate recommendations
                conf_recommendations = {"healthProfile": metrics, "prompt": RECOMMENDATIONS_PROMPT, "goals": goals}
                recommendations = await run_perplexity_pipeline(conf_recommendations)
                recommendations = json.loads(recommendations.replace("'", '"'))

                for _, rec in recommendations.items():
                    await add_recommendation_local(user_id, json.dumps(rec))
                    await upload_recommendation(user_id, rec, BEARER_TOKEN)

        except Exception as e:
            print(f"Error processing risks: {e}")


@app.on_event("startup")
async def startup_event():
    asyncio.create_task(process_metrics())
    asyncio.create_task(process_recommendations())
    asyncio.create_task(process_risks())


###################
# Pydantic models #
###################

class ExtensionEnum(str, Enum):
    jpeg = "jpeg"
    jpg = "jpg"
    png = "png"


class UploadFileV1_1Request(BaseModel):
    base64: str
    extension: ExtensionEnum
    message: Optional[str] = ""
    userId: int

    @validator('base64')
    def validate_base64(cls, v):
        try:
            base64.b64decode(v)
            return v
        except Exception:
            raise ValueError("Invalid base64 format")


class EntityType(str, Enum):
    general = "general"
    recommendation = "recommendation"
    risk = "risk"


class V2ChatMessageRequest(BaseModel):
    userId: int
    message: str
    entityType: EntityType
    entityId: Optional[int] = None

    @model_validator(mode="after")
    def validate_entity(self):
        if self.entityType != "general" and self.entityId is None:
            raise ValueError("'entityId' must be provided for 'recommendation' or 'risk' entity types")
        return self


class ClearUser(BaseModel):
    userId: str


class ResetUser(BaseModel):
    userId: str


class V2ChatOpenRequest(BaseModel):
    userId: int
    entityType: EntityType
    entityId: Optional[int] = None

    @model_validator(mode="after")
    def validate_entity(self):
        if self.entityType != "general" and self.entityId is None:
            raise ValueError("'entityId' must be provided for 'recommendation' or 'risk' entity types")
        return self


class V2ChatCloseRequest(BaseModel):
    userId: int
    entityType: EntityType
    entityId: Optional[int] = None

    @model_validator(mode="after")
    def validate_entity(self):
        if self.entityType != "general" and self.entityId is None:
            raise ValueError("'entityId' must be provided for 'recommendation' or 'risk' entity types")
        return self


class UploadRequest(BaseModel):
    userId: int
    imageId: int
    message: Optional[str] = ""

class FileRequest(BaseModel):
    userId: int
    fileId: int
    message: Optional[str] = ""

###################
# Routes
###################

@app.get("/health")
def health_check():
    return {"status": "healthy"}



async def wait_for_metrics(user_id: int, interval: float = 0.1) -> str:
    """Ожидает появления метрик в Redis, повторяя попытки до тех пор, пока они не появятся."""
    while True:
        metrics = await get_metrics(user_id)
        if metrics:
            return metrics
        await asyncio.sleep(interval)


@app.post("/v2/chat/message")
async def send_message(request: V2ChatMessageRequest):
    def is_filled(value: Any) -> bool:
        if isinstance(value, str):
            return value.strip() != "" and value.lower() != "null"
        elif isinstance(value, list):
            return len(value) > 0
        elif isinstance(value, (int, float)):
            return value != 0
        return value is not None

    user_id = request.userId
    message = request.message

    # Ждём, пока метрики появятся (и делаем параллельно запрос остальных данных)
    response_msg = ""
    system_msg = await get_last_list_item(f"system:messages-general:{user_id}")
    addition = f"""ASSISTANT: {system_msg}, USER: {message}"""
    conf_classify = {"prompt": f"{CLASSIFY_CHATTING_PROMPT}\n {addition}"}
    logger = get_user_logger(user_id)
    endpoint_name = "/v2/chat/message"
    max_attempts = 3
    attempt = 0

    mode = await get_chatting_mode(user_id)
    prefix = None
    if str(mode) == "metrics":
        prefix = "[РЕЖИМ АНКЕТИРОВАНИЯ]"
    if str(mode) == "conversation":
        prefix = "[РЕЖИМ ОБЩЕНИЯ]"
    if str(mode) == "timeout":
        prefix = "[РЕЖИМ ТАЙМАУТА]"
    if str(mode) == "question":
        prefix = "[ВСТРЕЧНЫЙ ВОПРОС]"
    await log_message(user_id, f"\n")
    await log_message(user_id, f"=====НОВОЕ СООБЩЕНИЕ, ТЕКУЩИЙ РЕЖИМ: {prefix}=====")
    async with log_time(logger, endpoint_name, "full_request"):
        while attempt < max_attempts:
            try:
                async with log_time(logger, endpoint_name, "get_initial_data"):
                    user_id = request.userId
                    message = request.message
                    message = message[0].upper() + message[1:]
                    entity_type = request.entityType
                    entity_id = request.entityId

                    goals, score, metrics, current_pending_field, awaiting_goals = await asyncio.gather(
                        get_goals(user_id),
                        get_user_weight(user_id),
                        wait_for_metrics(user_id),
                        r.get(f"user:{user_id}:pending_field"),
                        is_awaiting_goals(user_id)
                    )

                if message.strip() == "/калораж":
                    async with log_time(logger, endpoint_name, "/калораж"):
                        if attempt == 0:
                            await add_user_message(user_id, message, entity_type.value, entity_id or '')

                        def parse_calories(val) -> float:
                            if val is None:
                                return 0.0
                            if isinstance(val, (int, float)):
                                return float(val)

                            if isinstance(val, str):
                                # вытягиваем все числа (дроби тоже поддержатся)
                                nums = re.findall(r"\d+(?:[.,]\d+)?", val.replace(",", "."))
                                if not nums:
                                    return 0.0
                                nums = [float(n) for n in nums]
                                return sum(nums) / len(nums)

                            return 0.0

                        # получаем все записи о еде и распаковываем данные
                        raw_foods = await get_user_foods(user_id, BEARER_TOKEN)
                        # распаковываем данные: если элемент содержит 'data', извлекаем его, иначе используем сам элемент
                        foods = []
                        for item in raw_foods:
                            if isinstance(item, dict) and 'data' in item:
                                foods.append(item['data'])
                            else:
                                foods.append(item)

                        now_utc = datetime.now(timezone.utc)
                        # ---------- калории за текущий день (00:00–23:59 UTC) ----------
                        current_date = now_utc.date()
                        start_of_day = datetime.combine(current_date, dt_time.min, tzinfo=timezone.utc)
                        end_of_day = start_of_day + timedelta(days=1)

                        # фильтрация блюд за сегодня с правильным парсингом addedAt
                        foods_today = []
                        for f in foods:
                            raw_ts = f.get("addedAt", "").strip("'\"")
                            if not raw_ts:
                                continue
                            ts = raw_ts.replace("Z", "+00:00") if raw_ts.endswith("Z") else raw_ts + "+00:00"
                            try:
                                dt = datetime.fromisoformat(ts)
                            except ValueError:
                                continue
                            if start_of_day <= dt < end_of_day:
                                foods_today.append(f)

                        # суммирование калорий за сегодня
                        total_calories_today = sum(parse_calories(f.get("calories")) for f in foods_today)

                        # ---------- среднее за 7 дней ----------
                        last_7d_cutoff = now_utc - timedelta(days=7)
                        calories_by_day = {}
                        for f in foods:
                            raw_ts = f.get("addedAt", "").strip("'\"")
                            if not raw_ts:
                                continue
                            ts = raw_ts.replace("Z", "+00:00") if raw_ts.endswith("Z") else raw_ts + "+00:00"
                            try:
                                dt = datetime.fromisoformat(ts)
                            except ValueError:
                                continue
                            if dt >= last_7d_cutoff:
                                day = dt.date()
                                calories_by_day[day] = calories_by_day.get(day, 0) + parse_calories(f.get("calories"))

                        average_calories_last_week = round(
                            sum(calories_by_day.values()) / max(len(calories_by_day), 1),
                            1
                        )

                        print("RAW FOOD", raw_foods)
                        print("FOODS", foods)
                        print("FOODS_TODAY", foods_today)
                        print("TOTAL_CALORIES_TODAY", total_calories_today)
                        print("CALORIES_BY_DAY", calories_by_day)
                        print("AVERAGE_CALORIES_LAST_WEEK", average_calories_last_week)

                        goal_coefficient = json.loads(goals)['goal_coefficient']
                        goal = json.loads(goals)['goal']
                        gender = json.loads(metrics)['personal_information']['gender']
                        weight = int(json.loads(metrics)['personal_information']['weight'])
                        height = int(json.loads(metrics)['personal_information']['height'])
                        date_of_birth = json.loads(metrics)['personal_information']['date_of_birth']
                        birthdate = datetime.strptime(date_of_birth, "%d.%m.%Y").date()
                        today = datetime.today().date()
                        age = today.year - birthdate.year - ((today.month, today.day) < (birthdate.month, birthdate.day))

                        if gender == "М":
                            target_calories = ((10 * weight) + (6.25 * height) - (5 * age) + 5) * goal_coefficient
                        else:
                            target_calories = ((10 * weight) + (6.25 * height) - (5 * age) - 161) * goal_coefficient

                        first_seven_fields = [
                            "name", "gender", "date_of_birth", "height", "weight", "activity_level",
                            "food_level", "smoking", "alcohol", "sleep_recovery", "stress_level", "chronic_conditions",
                            "allergies", "sports", "regular_medicine", "genetic_conditions", "sleep_quality",
                            "injuries", "diet_balance", "calorie_intake", "sleep_schedule", "work_schedule",
                            "blood_pressure", "preferred_dishes", "diet_schedule", "food_intolerances",
                            "blood_analysis", "biochemical_analysis", "urine_analysis", "lipid_profile",
                            "glucose_tolerance_test",
                            "thyroid_test", "glycated_hemoglobin", "coagulogram", "inflammatory_markers"
                        ]
                        metrics = await get_metrics(user_id)
                        user_metrics_dict = json.loads(str(metrics))
                        addition_parts = []
                        for field in first_seven_fields:
                            field_display = None
                            field_value = None
                            for section in user_metrics_dict.values():
                                if isinstance(section, dict) and field in section:
                                    field_value = section[field]
                                    field_display = section.get(field + "_field_name", field)
                                    break
                            # Если значение не заполнено или пустая строка – подставляем "Не заполнено"
                            if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
                                field_value = "Не заполнено"
                            # Если значение — список, преобразуем его в строку через запятую
                            elif isinstance(field_value, list):
                                if len(field_value) == 0:
                                    field_value = "Не заполнено"
                                else:
                                    field_value = ", ".join(str(item) for item in field_value)
                            addition_parts.append(f" - {field_display}: {field_value}")
                        kz = "\n".join(addition_parts)

                        addition_small_trend = f'''
                            Ты владеешь следующей информацией:
                            total_calories_today: {total_calories_today} – сколько калорий съел пользователь сегодня
                            average_calories_last_week: {average_calories_last_week} – средний дневной калораж за последние 7 дней
                            target_calories: {target_calories} - целевая дневная норма калорий
        
                            ФОРМАТ ВЫВОДА:
                            Анализ тренда калорий  
                            [одно короткое предложение — 12–20 слов, дружелюбное и по делу]
        
                            ТВОЯ ЛОГИКА:
                            Сравни total_calories_today с average_calories_last_week (тренд).
                            Сравни total_calories_today с target_calories (норма).
                            На основе этого выбери один из следующих типов фразы:
                            Калорийность значительно выше среднего (+15%+):
                            – Предположи насыщенность, активность или праздничный день.
                            – Избегай оценки — поддержи.
        
                            Калорийность значительно ниже среднего (−15%+):
                            – Отметь лёгкость дня или осознанный подход.
                            – Мягко предложи следить за насыщением.
        
                            Калорийность в пределах ±15% от среднего и от нормы:
                            – Отметь стабильность, сбалансированность, удержание привычки.
                            Если калорийность превышает цель более чем на 40%:
                            – Аккуратно подчеркни переедание, не стыдя. Предложи завтра вернуться к цели.
        
                            СТИЛЬ:
                            – Нейтрально-дружелюбный, без приписок "молодец"
                            – Тон — заботливый, с уважением к выбору пользователя
                            – Не больше одного предложения
        
                            ПРИМЕР ВХОДА:
                            {{
                              "total_calories_today": 2900,
                              "average_calories_last_week": 1950,
                              "target_calories": 2000
                            }}
                            
                            Выводи тренды СТРОГО в формате JSON в СТРОГО таком формате и СТРОГО с такими данными, самостоятельно заполняя title и description:
                            ПРИМЕР ВЫХОДА:
                            {{
                            "title": "(2-3 слова, описывающие description)"
                            "description": "Сегодня рацион был особенно плотным — вы превысили привычный и целевой уровень. Завтра можно немного сбалансировать."
                            }}
                            '''
                        addition_big_trend = f'''
                            ВХОДНЫЕ ДАННЫЕ:
                            total_calories_today: {total_calories_today} – сколько калорий потребил пользователь за день
                            average_calories_last_week: {average_calories_last_week} – средний дневной калораж за последние 7 дней
                            target_calories: {target_calories} – целевая дневная норма калорий
                            user_goal: {goal} – цель пользователя 
                            health_data_json: {kz} – карта здоровья
                            {foods_today}: опциональный список еды, которую пользователь употребил сегодня
        
                            ВЫХОДНОЙ ФОРМАТ:
                            Глубокий анализ калорий и глубокий анализ БЖУ
                            [два–три предложения с акцентом на перекосы, сбалансированность и вклад белков/жиров/углеводов]
        
                            ТВОЯ ЛОГИКА:
                            – Вычисли % белков, жиров и углеводов от общего калоража (1 г белка/углеводов = 4 ккал, жиров = 9 ккал)
                            – Обрати внимание на перекосы с учетом личного профиля — например, избыток жиров или дефицит белка.
                            – Сделай упор на то, как это могло повлиять на насыщение, уровень энергии и восстановление, предлагай конкретные сценарии решений / питания.
                            – Избегай обвиняющего тона — используй стиль поддержки.
                            
                            Выводи тренды СТРОГО в формате JSON в СТРОГО таком формате и СТРОГО с такими данными, самостоятельно заполняя title и description:
                            ПРИМЕР ВЫХОДА:
                            {{
                            "title": "(2-3 слова, описывающие description)"
                            "description": "Рацион оказался жирнее привычного — более 40% калорий пришлись на жиры, в основном из курицы с картошкой и карбонары. Белков и углеводов было меньше, что могло сказаться на энергии и восстановлении. Сбалансировать это можно лёгким белковым ужином завтра."
                            }}
                            '''
                        addition_achievements = f'''
                            ВХОДНЫЕ ДАННЫЕ:
                            total_calories_today: {total_calories_today} – сколько съел за день
                            average_calories_last_week: {average_calories_last_week} – сколько ел в среднем
                            target_calories: {target_calories} – целевая дневная норма
                            user_goal: {goal} – цель пользователя 
                            health_data_json: {kz} – карта здоровья
                            ВЫХОДНОЙ ФОРМАТ:
                            Вывод по питанию  
                            [один связный параграф из 2 предложений: 1 — наблюдение, 2 — интерпретация/последствие]
                            Логика:
                            – Сравни день с нормой и со средним.
                            – Если день — отклонение от стабильного шаблона (вверх/вниз), укажи это.
                            – Сделай вывод о причине (насыщенность, компенсация, хаотичное питание, смена паттерна).
                            – В интерпретации — мягко предположи эффект: энергичность, переутомление, риск срывов, компенсация, сбой графика и т.п. Учитывай цель пользователя и карту здоровья для общего контекста.
                            Избегай банальных фраз типа "всё хорошо" или "продолжайте в том же духе". Твоя задача — заметить тренд, объяснить, что он значит, и чему может привести.
        
                            СТИЛЬ:
                            – Умный, но доступный.
                            – Не пугающий, но не пустой.
                            – Как заботливый аналитик или диетолог.
                            
                            Выводи тренды СТРОГО в формате JSON в СТРОГО таком формате и СТРОГО с такими данными, самостоятельно заполняя title и description:
                            ПРИМЕР ВЫХОДА:
                            {{
                            "title": "(2-3 слова, описывающие description)"
                            "description": "Сегодня вы потребили значительно больше, чем обычно, и вышли далеко за пределы целевой нормы. Это может указывать на эпизод переедания, связанный с эмоциональной нагрузкой или нерегулярным графиком — стоит понаблюдать за следующими днями, особенно учитывая вашу цель пробежать марафон через неделю."
                            }}
                            '''
                        conf_small_trend = {"prompt": addition_small_trend + ' ' + CALORIES_SMALL_TREND}
                        conf_big_trend = {"prompt": addition_big_trend + ' ' + CALORIES_BIG_TREND}
                        conf_achievements = {"prompt": addition_achievements + ' ' + CALORIES_ACHIEVEMENTS}

                        # Define insufficiency conditions
                        insufficient_daily_data = (total_calories_today == 0.0)
                        # calories_by_day is a dict, keys are dates, values are calories for that day.
                        # Check if data exists for less than 4 distinct days in the last week (i.e. < 50% of 7 days).
                        insufficient_weekly_data = (len(calories_by_day) < 4)

                        insufficient_data_response_dict = {"title": "Слишком мало данных для анализа", "description": "Добавьте больше записей для получения персонализированного анализа"}
                        insufficient_data_json_str = json.dumps(insufficient_data_response_dict)

                        # Helper async functions for conditional GPT calls
                        async def get_small_trend_conditional():
                            if insufficient_daily_data or insufficient_weekly_data:
                                return insufficient_data_json_str
                            # CALORIES_SMALL_TREND must be in scope
                            conf_small_trend = {"prompt": addition_small_trend + ' ' + CALORIES_SMALL_TREND}
                            return await run_chatgpt_calories_small_trend_pipeline(conf_small_trend)

                        async def get_big_trend_conditional():
                            if insufficient_daily_data or insufficient_weekly_data:
                                return insufficient_data_json_str
                            # CALORIES_BIG_TREND must be in scope
                            conf_big_trend = {"prompt": addition_big_trend + ' ' + CALORIES_BIG_TREND}
                            return await run_chatgpt_calories_big_trend_pipeline(conf_big_trend)

                        async def get_achievements_conditional():
                            if insufficient_daily_data or insufficient_weekly_data:
                                return insufficient_data_json_str
                            # CALORIES_ACHIEVEMENTS must be in scope
                            conf_achievements = {"prompt": addition_achievements + ' ' + CALORIES_ACHIEVEMENTS}
                            return await run_chatgpt_calories_achievements_pipeline(conf_achievements)

                        # Call the conditional helpers
                        small_trend_raw, big_trend_raw, achievements_raw = await asyncio.gather(
                            get_small_trend_conditional(),
                            get_big_trend_conditional(),
                            get_achievements_conditional()
                        )

                        small_trend = json.loads(small_trend_raw.replace("'", '"'))
                        big_trend = json.loads(big_trend_raw.replace("'", '"'))
                        achievements = json.loads(achievements_raw.replace("'", '"'))
                        trend_data = {}
                        trend_data["content"] = {
                            "smallTrend": small_trend,
                            "bigTrend": big_trend,
                            "achievements": achievements
                        }
                        trend_data["type"] = "calories"
                        timezone_str = await get_timezone(user_id)
                        user_tz = timezone(timedelta(hours=int(timezone_str)))
                        added_at_dt = datetime.now(user_tz)
                        trend_data["addedAt"] = str(added_at_dt.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3])
                        await upload_trend(user_id, trend_data, BEARER_TOKEN),
                        challenges_result = ("Малый тренд:\n\n" + small_trend["title"] + "\n" + small_trend["description"]
                                            + "\n\nБольшой тренд:\n\n" + big_trend["title"] + "\n" + big_trend["description"]
                                            + "\n\nВывод и мотивация:\n\n" + achievements["title"] + "\n" + achievements["description"])
                        await add_system_message(user_id, challenges_result, entity_type.value, entity_id or '')
                        return {"message": challenges_result}

                if message.strip() == "/челлендж":
                    async with log_time(logger, endpoint_name, "/челлендж"):
                        if attempt == 0:
                            await add_user_message(user_id, message, entity_type.value, entity_id or '')
                        first_seven_fields = [
                            "name", "gender", "date_of_birth", "height", "weight", "activity_level",
                            "food_level", "smoking", "alcohol", "sleep_recovery", "stress_level", "chronic_conditions",
                            "allergies", "sports", "regular_medicine", "genetic_conditions", "sleep_quality",
                            "injuries", "diet_balance", "calorie_intake", "sleep_schedule", "work_schedule",
                            "blood_pressure", "preferred_dishes", "diet_schedule", "food_intolerances",
                            "blood_analysis", "biochemical_analysis", "urine_analysis", "lipid_profile",
                            "glucose_tolerance_test",
                            "thyroid_test", "glycated_hemoglobin", "coagulogram", "inflammatory_markers"
                        ]
                        metrics = await get_metrics(user_id)
                        user_metrics_dict = json.loads(str(metrics))
                        addition_parts = []
                        for field in first_seven_fields:
                            field_display = None
                            field_value = None
                            for section in user_metrics_dict.values():
                                if isinstance(section, dict) and field in section:
                                    field_value = section[field]
                                    field_display = section.get(field + "_field_name", field)
                                    break
                            # Если значение не заполнено или пустая строка – подставляем "Не заполнено"
                            if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
                                field_value = "Не заполнено"
                            # Если значение — список, преобразуем его в строку через запятую
                            elif isinstance(field_value, list):
                                if len(field_value) == 0:
                                    field_value = "Не заполнено"
                                else:
                                    field_value = ", ".join(str(item) for item in field_value)
                            addition_parts.append(f" - {field_display}: {field_value}")

                        user_msgs_task = get_last_user_messages(user_id, "general", '', count=10)
                        system_msgs_task = get_last_system_messages(user_id, "general", '', count=10)
                        user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)

                        history = []
                        if system_messages:
                            # Add the initial system message (e.g. greeting)
                            history.append({"role": "assistant", "content": system_messages[0]})
                        # For remaining messages, pair each system message with its preceding user message
                        for i in range(1, len(system_messages)):
                            if i - 1 < len(user_messages):
                                history.append({"role": "user", "content": user_messages[i - 1]})
                            history.append({"role": "assistant", "content": system_messages[i]})
                        # Finally, add the latest user message
                        history.append({"role": "user", "content": message})

                        existing_challenges = await get_user_challenges(user_id, BEARER_TOKEN)
                        availableMechanicsDistribution = await get_user_challenges_mechanics(user_id, BEARER_TOKEN)
                        print("EXISTING_CHALLENGES", existing_challenges)

                        def format_mechanic_counts(data: List[Dict[str, str]]) -> str:
                            all_mechanics = [
                                'dailyChecklist',
                                'instantAction',
                                'timedAction',
                                'goalProgress',
                                'countdownToTime'
                            ]
                            counts = Counter(item['mechanic'] for item in data)
                            parts = [f"{mech}: {counts.get(mech, 0)}" for mech in all_mechanics]
                            return ", ".join(parts)

                        availableMechanicsDistribution = format_mechanic_counts(availableMechanicsDistribution)
                        print("availableMechanicsDistribution", availableMechanicsDistribution)
                        risks = await fetch_all_risks(BEARER_TOKEN, user_id)
                        recommendations = await fetch_all_recommendations(BEARER_TOKEN, user_id)
                        kz = "\n".join(addition_parts)
                        addition = f'''
                                    Ты — умный ассистент в приложении по улучшению здоровья. Сгенерируй до 5 простых и понятных базовых челленджей, персонализированных под пользователя.
                                    ВХОДНЫЕ ДАННЫЕ:
                                    - Цель пользователя: {json.loads(goals)["goal"]}
                                    - Карта Здоровья: {kz}
                                    - Контекст последних сообщений: {history}
                                    - Риски здоровья: {risks}
                                    - Рекомендации: {recommendations}
                                    - Текущий баланс механик: {availableMechanicsDistribution}
                                    - Тематики, по которым возможны челленджи: ["Активность", "Спорт", "Питание", "Медицина", "Стресс", "Сон"]
                                    - Список уже существующих челленджей (чтобы избежать повторов): {existing_challenges}
                                    '''
                        prompt = addition + " " + GENERATE_CHALLENGES_PROMPT
                        conf_message = {"prompt": prompt}
                        challenges = await run_chatgpt_generate_challenges_pipeline(conf_message)

                        def parse_challenges(raw_output):
                            """
                            Парсит исходную строку с челленджами и возвращает список кортежей (title, category).
                            """
                            # Ищем все вхождения вида {"Текст задания"; "Категория"}
                            pattern = r'\{\s*"([^"]+)"\s*;\s*"([^"]+)"\s*\}'
                            parsed = re.findall(pattern, raw_output)
                            return [list(item) for item in parsed]

                        challenges_list = parse_challenges(challenges)
                        # Если список не пуст, обрабатываем только первый челлендж
                        if challenges_list and len(challenges_list) > 0:
                            title, theme = challenges_list[0]
                            addition = f'''
                                        ТЫ — ЭКСПЕРТНЫЙ АССИСТЕНТ ПО ЗДОРОВЬЮ в мобильном приложении. Твоя задача — сгенерировать один персонализированный и технически валидный челлендж на основе пользовательских данных. Челлендж должен быть мотивирующим, структурированным, и готовым к отображению в приложении и запуску через API.
                                        ВХОДНЫЕ ДАННЫЕ:
                                        - Краткое описание/намерение пользователя: {title}
                                        - Предложенная тематика (если указана): {theme}
                                        - Цель пользователя: {json.loads(goals)["goal"]}
                                        - Карта здоровья: {kz}
                                        - Контекст последних сообщений в чате и поведение: {history}
                                        - Риски здоровья: {risks}
                                        - Рекомендации: {recommendations}
                                        - Текущий баланс механик: {availableMechanicsDistribution}
                                        '''
                            prompt = addition + " " + DESCRIBE_CHALLENGE_PROMPT
                            conf_metrics = {"usr_msg": message, "prompt": prompt}
                            described = await run_chatgpt_describe_challenge_pipeline(conf_metrics)

                            # Добавляем поле condition и загружаем челлендж
                            challenge_data = json.loads(described.replace("'", '"'))
                            challenge_data["condition"] = "notStarted"
                            await upload_challenge(user_id, challenge_data, BEARER_TOKEN)

                            # Формируем результат для ответа
                            print("DESCRIBED CHALLENGE:", described)
                            await add_system_message(user_id, "Сгенерированный челлендж:\n\n" + described, entity_type.value, entity_id or '')
                            return {"message": "Сгенерированный челлендж:\n\n" + described}
                        else:
                            await add_system_message(user_id, "Не удалось найти ни одного челленджа.", entity_type.value, entity_id or '')
                            return {"message": "Не удалось найти ни одного челленджа."}

                if message.startswith("[id-1]") and message[7:] != "Не нужно, спасибо":
                    async with log_time(logger, endpoint_name, "[id-1]"):
                        max_attempts = 3
                        attempt = 0
                        while attempt < max_attempts:
                            try:
                                if attempt == 0:
                                    await add_user_message(user_id, message, entity_type.value, entity_id or '')
                                message = message[7:]
                                if message == "Запомнить данные":
                                    final_added = await r.get(f"user:{user_id}:final_added")
                                    if final_added:
                                        # Добавляем калораж в виджет жира
                                        example = """📌 Отлично! Записал:
                                            ✅ Порция: 400 г (бургер + картошка)
                                            ✅ Калорийность: ≈ 900–1200 ккал
                                            ✅ БЖУ:
                                                •	Белки: ≈ 50–65 г
                                                •	Жиры: ≈ 60–80 г
                                                •	Углеводы: ≈ 55–75 г
                                            В течение суток они будут пересчитаны на усредненные с учетом других приемов пищи
                                            💪🔥 Если в следующий раз захочешь добавить ещё информацию о питании или уточнить данные – обращайся.
        
                                            Что еще могу для тебя сделать? 😊"""
                                        prompt = (
                                                    CONVERSATION_PROMPT + ". ТВОЯ ТЕКУЩАЯ ЗАДАЧА: Скажи что записал конкретные Каллории и БКЖУ из данного сообщения: " + str(
                                                system_msg) + ". И скажи похожее, но не 1 в 1: "
                                                    + """В течение суток они будут пересчитаны на усредненные с учетом других приемов пищи
                                        💪🔥 Если в следующий раз захочешь добавить ещё информацию о питании или уточнить данные – обращайся.""" + ". Вот пример как надо: " + example)
                                        conf_message = {"prompt": prompt}
                                        promtp_food = ANALYZE_FOOD + " " + system_msg
                                        conf_food_message = {"prompt": promtp_food}
                                        msg, food_analysis = await asyncio.gather(
                                            run_chatgpt_chat_open_pipeline(conf_message),
                                            run_chatgpt_analyze_food_pipeline(conf_food_message)
                                        )
                                        await add_system_message(user_id, msg, entity_type.value, entity_id or '')
                                        food_analysis = json.loads(food_analysis.replace("'", '"'))
                                        food_analysis["proteins"] = int(food_analysis["proteins"])
                                        food_analysis["fats"] = int(food_analysis["fats"])
                                        food_analysis["carbohydrates"] = int(food_analysis["carbohydrates"])
                                        food_analysis["calories"] = (int(food_analysis["proteins"]) * 4) + (
                                                    int(food_analysis["carbohydrates"]) * 4) + (int(food_analysis["fats"]) * 9)
                                        timezone_str = await get_timezone(user_id)
                                        user_tz = timezone(timedelta(hours=int(timezone_str)))
                                        added_at_dt = datetime.now(user_tz)
                                        food_analysis["addedAt"] = str(added_at_dt.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3])
                                        await upload_food(user_id, food_analysis, BEARER_TOKEN)
                                        return {"message": msg}
                                    else:
                                        # Добавляем калораж в виджет жира
                                        example = """📌 Отлично! Записал:
                                                                    ✅ Порция: 400 г (бургер + картошка)
                                                                    ✅ Калорийность: ≈ 900–1200 ккал
                                                                    ✅ БЖУ:
                                                                        •	Белки: ≈ 50–65 г
                                                                        •	Жиры: ≈ 60–80 г
                                                                        •	Углеводы: ≈ 55–75 г
                                                                    В течение суток они будут пересчитаны на усредненные с учетом других приемов пищи
                                                                    💪🔥 Если в следующий раз захочешь добавить ещё информацию о питании или уточнить данные – обращайся.
                                                                    """
                                        prompt = (
                                                CONVERSATION_PROMPT + ". ТВОЯ ТЕКУЩАЯ ЗАДАЧА: Скажи что записал конкретные Каллории и БКЖУ из данного сообщения: " + str(
                                            system_msg) + ". И скажи похожее, но не 1 в 1: "
                                                + """В течение суток они будут пересчитаны на усредненные с учетом других приемов пищи
                                                                💪🔥 Если в следующий раз захочешь добавить ещё информацию о питании или уточнить данные – обращайся.""" + ". Вот пример как надо: " + example)
                                        conf_message = {"prompt": prompt}
                                        promtp_food = ANALYZE_FOOD + " " + system_msg
                                        conf_food_message = {"prompt": promtp_food}
                                        msg, food_analysis = await asyncio.gather(
                                            run_chatgpt_chat_open_pipeline(conf_message),
                                            run_chatgpt_analyze_food_pipeline(conf_food_message)
                                        )
                                        await add_system_message(user_id, "[id-3]" + msg, entity_type.value, entity_id or '')
                                        food_analysis = json.loads(food_analysis.replace("'", '"'))
                                        food_analysis["proteins"] = int(food_analysis["proteins"])
                                        food_analysis["fats"] = int(food_analysis["fats"])
                                        food_analysis["carbohydrates"] = int(food_analysis["carbohydrates"])
                                        food_analysis["calories"] = (int(food_analysis["proteins"]) * 4) + (
                                                    int(food_analysis["carbohydrates"]) * 4) + (int(food_analysis["fats"]) * 9)
                                        timezone_str = await get_timezone(user_id)
                                        user_tz = timezone(timedelta(hours=int(timezone_str)))
                                        added_at_dt = datetime.now(user_tz)
                                        food_analysis["addedAt"] = str(added_at_dt.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3])
                                        await upload_food(user_id, food_analysis, BEARER_TOKEN)
                                        return {"message": "[id-3]" + msg}
                                if message == "Поменять вес порции":
                                    prompt = (
                                            CONVERSATION_PROMPT + ". ТВОЯ ТЕКУЩАЯ ЗАДАЧА: Cкажи похожее, но не 1 в 1: " +
                                            """✨ Окей, я почти готов посчитать. Сейчас жду от тебя вес порции — просто напиши, сколько примерно ты съел в граммах.""")
                                    conf_message = {"prompt": prompt}
                                    msg = await run_chatgpt_chat_open_pipeline(conf_message)
                                    await add_system_message(user_id, msg, entity_type.value, entity_id or '')
                                    return {"message": "[id-2]" + msg}
                            except Exception as e:
                                if "NoneType" in str(e):
                                    attempt += 1
                                    await asyncio.sleep(0.2)
                                    continue
                                raise HTTPException(status_code=500, detail=str(e))

                if message.startswith("[id-2]"):
                    async with log_time(logger, endpoint_name, "[id-2]"):
                        max_attempts = 3
                        attempt = 0
                        while attempt < max_attempts:
                            try:
                                if attempt == 0:
                                    await add_user_message(user_id, message, entity_type.value, entity_id or '')
                                message = message[7:]
                                penultimate_sys_message = await r.lindex(f"system:messages-general:{user_id}", -2)
                                example = """[id-1] Супер, теперь точнее! 💡
                                            Если общая порция (бургер + картошка) весила примерно X г, а ты съел X г, это около X% от всего блюда. 
                                            ✅ Калорийность твоей порции: примерно X ккал
                                            ✅ БЖУ:
                                                •	Белки: ≈ X г
                                                •	Жиры: ≈ X г
                                                •	Углеводы: ≈ X г
                                            Вполне плотный приём пищи! 🍔🔥 У тебя цель – похудение, такой бургер лучше вписать в дневную норму и сбалансировать остальными приёмами. 💪"""
                                prompt = (
                                        CONVERSATION_PROMPT +
                                        ". ТВОЯ ТЕКУЩАЯ ЗАДАЧА: " +
                                        "1. Если сообщение пользователя явно относится к уточнению съеденной порции/граммовки (то есть содержит слова или фразы типа 'грамм', 'порция', 'съел', 'количество', 'калории', 'БЖУ' и т.п.), то в начале ответа обязательно выведи '[id-1]'. Вот сообщение пользователя: " + str(
                                    message) +
                                        " 2. Если сообщение пользователя соответствует условию пункта 1, пересчитай его потребленные калории и БЖУ, опираясь на изначальное сообщение. Вот исходное сообщение: " + str(
                                    penultimate_sys_message) +
                                        f". Пример формата вывода (с использованием эмодзи): {example}" +
                                        " 3. Если сообщение пользователя не содержит элементов, указывающих на уточнение съеденной порции (граммовки), то просто ответь на него, и в таком случае НЕ добавляй '[id-1]' в начале ответа."
                                )
                                conf_message = {"prompt": prompt}
                                msg = await run_chatgpt_recount_calories_pipeline(conf_message)
                                await add_system_message(user_id, msg, entity_type.value, entity_id or '')
                                return {"message": msg}
                            except Exception as e:
                                if "NoneType" in str(e):
                                    attempt += 1
                                    await asyncio.sleep(0.2)
                                    continue
                                raise HTTPException(status_code=500, detail=str(e))

                if message == "[id-5] Принимаю испытание":
                    async with log_time(logger, endpoint_name, "[id-5]"):
                        if attempt == 0:
                            await add_user_message(user_id, message, entity_type.value, entity_id or '')
                        first_seven_fields = [
                            "name", "gender", "date_of_birth", "height", "weight", "activity_level",
                            "food_level", "smoking", "alcohol", "sleep_recovery", "stress_level", "chronic_conditions",
                            "allergies", "sports", "regular_medicine", "genetic_conditions", "sleep_quality",
                            "injuries", "diet_balance", "calorie_intake", "sleep_schedule", "work_schedule",
                            "blood_pressure", "preferred_dishes", "diet_schedule", "food_intolerances",
                            "blood_analysis", "biochemical_analysis", "urine_analysis", "lipid_profile",
                            "glucose_tolerance_test",
                            "thyroid_test", "glycated_hemoglobin", "coagulogram", "inflammatory_markers"
                        ]
                        metrics = await get_metrics(user_id)
                        user_metrics_dict = json.loads(str(metrics))
                        addition_parts = []
                        for field in first_seven_fields:
                            field_display = None
                            field_value = None
                            for section in user_metrics_dict.values():
                                if isinstance(section, dict) and field in section:
                                    field_value = section[field]
                                    field_display = section.get(field + "_field_name", field)
                                    break
                            # Если значение не заполнено или пустая строка – подставляем "Не заполнено"
                            if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
                                field_value = "Не заполнено"
                            # Если значение — список, преобразуем его в строку через запятую
                            elif isinstance(field_value, list):
                                if len(field_value) == 0:
                                    field_value = "Не заполнено"
                                else:
                                    field_value = ", ".join(str(item) for item in field_value)
                            addition_parts.append(f" - {field_display}: {field_value}")

                        user_msgs_task = get_last_user_messages(user_id, "general", '', count=10)
                        system_msgs_task = get_last_system_messages(user_id, "general", '', count=10)
                        user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)

                        history = []
                        if system_messages:
                            # Add the initial system message (e.g. greeting)
                            history.append({"role": "assistant", "content": system_messages[0]})
                        # For remaining messages, pair each system message with its preceding user message
                        for i in range(1, len(system_messages)):
                            if i - 1 < len(user_messages):
                                history.append({"role": "user", "content": user_messages[i - 1]})
                            history.append({"role": "assistant", "content": system_messages[i]})
                        # Finally, add the latest user message
                        history.append({"role": "user", "content": message})

                        existing_challenges = await get_user_challenges(user_id, BEARER_TOKEN)
                        availableMechanicsDistribution = await get_user_challenges_mechanics(user_id, BEARER_TOKEN)
                        print("EXISTING_CHALLENGES", existing_challenges)

                        def format_mechanic_counts(data: List[Dict[str, str]]) -> str:
                            all_mechanics = [
                                'dailyChecklist',
                                'instantAction',
                                'timedAction',
                                'goalProgress',
                                'countdownToTime'
                            ]
                            counts = Counter(item['mechanic'] for item in data)
                            parts = [f"{mech}: {counts.get(mech, 0)}" for mech in all_mechanics]
                            return ", ".join(parts)

                        availableMechanicsDistribution = format_mechanic_counts(availableMechanicsDistribution)
                        print("availableMechanicsDistribution", availableMechanicsDistribution)
                        risks = await fetch_all_risks(BEARER_TOKEN, user_id)
                        recommendations = await fetch_all_recommendations(BEARER_TOKEN, user_id)
                        kz = "\n".join(addition_parts)
                        addition = f'''
                                                            Ты — умный ассистент в приложении по улучшению здоровья. Сгенерируй до 5 простых и понятных базовых челленджей, персонализированных под пользователя.
                                                            ВХОДНЫЕ ДАННЫЕ:
                                                            - Цель пользователя: {json.loads(goals)["goal"]}
                                                            - Карта Здоровья: {kz}
                                                            - Тема челленджа по контексту последней пары сообщений: {history[-2]}, {history[-3]}
                                                            - Риски здоровья: {risks}
                                                            - Рекомендации: {recommendations}
                                                            - Текущий баланс механик: {availableMechanicsDistribution}
                                                            - Тематики, по которым возможны челленджи: ["Активность", "Спорт", "Питание", "Медицина", "Стресс", "Сон"]
                                                            - Список уже существующих челленджей (чтобы избежать повторов): {existing_challenges}
                                                            '''
                        prompt = addition + " " + GENERATE_CHALLENGES_PROMPT
                        conf_message = {"prompt": prompt}
                        # challenges = await run_chatgpt_generate_challenges_pipeline(conf_message)

                        def parse_challenges(raw_output):
                            """
                            Парсит исходную строку с челленджами и возвращает список кортежей (title, category).
                            """
                            # Ищем все вхождения вида {"Текст задания"; "Категория"}
                            pattern = r'\{\s*"([^"]+)"\s*;\s*"([^"]+)"\s*\}'
                            parsed = re.findall(pattern, raw_output)
                            return [list(item) for item in parsed]

                        # title, theme = challenges_list[0]
                        addition = f'''
                                                            ТЫ — ЭКСПЕРТНЫЙ АССИСТЕНТ ПО ЗДОРОВЬЮ в мобильном приложении. Твоя задача — сгенерировать один персонализированный и технически валидный челлендж на основе пользовательских данных. Челлендж должен быть мотивирующим, структурированным, и готовым к отображению в приложении и запуску через API.
                                                            ВХОДНЫЕ ДАННЫЕ:
                                                            - Контекст нужного челленджа от пользователя: {history[-2]}, {history[-3]}
                                                            - Цель пользователя: {json.loads(goals)["goal"]}
                                                            - Карта здоровья: {kz}
                                                            - Риски здоровья: {risks}
                                                            - Рекомендации: {recommendations}
                                                            - Текущий баланс механик: {availableMechanicsDistribution}
                                                            '''
                        prompt = addition + " " + DESCRIBE_CHALLENGE_PROMPT
                        conf_metrics = {"usr_msg": message, "prompt": prompt}
                        described = await run_chatgpt_describe_challenge_pipeline(conf_metrics)

                        # Добавляем поле condition и загружаем челлендж
                        challenge_data = json.loads(described.replace("'", '"'))
                        challenge_data["condition"] = "notStarted"
                        await upload_challenge(user_id, challenge_data, BEARER_TOKEN)
                        name = json.loads(metrics)['personal_information']['name']
                        msg = f'[id-6] {name.title()}, сгенерировал тебе челлендж!\n\n"{json.loads(described)["title"]}"'
                        # Формируем результат для ответа
                        await add_system_message(user_id, msg, entity_type.value, entity_id or '')
                        return {"message": msg}

                user_goals = await r.lrange(f"user:goals:{user_id}", 0, -1)
                async with log_time(logger, endpoint_name, "get_secondary_data"):
                    goals = goals or ""
                    current_weight = None
                    user_metrics = json.loads(str(metrics))
                    for section in user_metrics.values():
                        if isinstance(section, dict):
                            if current_pending_field in section:
                                current_weight = get_weight_value(section.get(current_pending_field + "_weight"))
                    print("current_pending_field:", current_pending_field, current_weight)
                    await log_message(user_id, f"ВОПРОС ПО ПОЛЮ: {current_pending_field}")
                    mode = await get_chatting_mode(user_id)

                async with log_time(logger, endpoint_name, "classifiers"):
                    if user_goals or "Если хочешь, можем просто пообщаться или продолжить анкетирование" in system_msg:
                        # response_msg = await run_chatgpt_classify_pipeline(conf_classify)
                        if message.strip().lower() in ["ща", "подожди", "погодь", "момент", "ща, погодь", "ща, подожди",
                                                       "ща, момент"]:
                            await set_chatting_mode(user_id, "timeout")
                        elif message.strip().lower() in ["анкета", "в анкету", "вернемся в анкету", "продолжим с анкетой",
                                                         "вернёмся в анкету"]:
                            await set_chatting_mode(user_id, "metrics")
                        else:
                            challenge_addition = f'''
                            Ты — экспертный ассистент по здоровью в мобильном приложении. Твоя задача — классифицировать пользовательское сообщение как сигнал к предложению челленджа (или нет).
                             ВХОДНЫЕ ДАННЫЕ:
                             - Сообщение пользователя: {message}
                            '''
                            challenge_conf_message = {"prompt": challenge_addition + CHALLENGE_CLASSIFY}
                            # response_msg = await run_chatgpt_classify_pipeline(conf_classify)
                            challenge_classify = '{"challenge_relevant": 0}'
                            response_msg = "conversation 0.0"
                            final_added = await r.get(f"user:{user_id}:final_added")
                            if not final_added:
                                if mode != "metrics":
                                    response_msg, challenge_classify = await asyncio.gather(
                                        run_chatgpt_classify_pipeline(conf_classify),
                                        run_chatgpt_challenge_classify_pipeline(challenge_conf_message)
                                    )
                                else:
                                    response_msg = await run_chatgpt_classify_pipeline(conf_classify)
                            else:
                                challenge_classify = await run_chatgpt_challenge_classify_pipeline(challenge_conf_message)

                            challenge_classify = json.loads(challenge_classify)
                            if challenge_classify['challenge_relevant'] == 1 and message != "[id-5] Принимаю испытание":
                                first_seven_fields = [
                                    "name", "gender", "date_of_birth", "height", "weight", "activity_level",
                                    "food_level", "smoking", "alcohol", "sleep_recovery", "stress_level", "chronic_conditions",
                                    "allergies", "sports", "regular_medicine", "genetic_conditions", "sleep_quality",
                                    "injuries", "diet_balance", "calorie_intake", "sleep_schedule", "work_schedule",
                                    "blood_pressure", "preferred_dishes", "diet_schedule", "food_intolerances",
                                    "blood_analysis", "biochemical_analysis", "urine_analysis", "lipid_profile",
                                    "glucose_tolerance_test",
                                    "thyroid_test", "glycated_hemoglobin", "coagulogram", "inflammatory_markers"
                                ]
                                metrics = await get_metrics(user_id)
                                user_metrics_dict = json.loads(str(metrics))
                                addition_parts = []
                                for field in first_seven_fields:
                                    field_display = None
                                    field_value = None
                                    for section in user_metrics_dict.values():
                                        if isinstance(section, dict) and field in section:
                                            field_value = section[field]
                                            field_display = section.get(field + "_field_name", field)
                                            break
                                    # Если значение не заполнено или пустая строка – подставляем "Не заполнено"
                                    if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
                                        field_value = "Не заполнено"
                                    # Если значение — список, преобразуем его в строку через запятую
                                    elif isinstance(field_value, list):
                                        if len(field_value) == 0:
                                            field_value = "Не заполнено"
                                        else:
                                            field_value = ", ".join(str(item) for item in field_value)
                                    addition_parts.append(f" - {field_display}: {field_value}")

                                user_msgs_task = get_last_user_messages(user_id, "general", '', count=10)
                                system_msgs_task = get_last_system_messages(user_id, "general", '', count=10)
                                user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)

                                history = []
                                if system_messages:
                                    # Add the initial system message (e.g. greeting)
                                    history.append({"role": "assistant", "content": system_messages[0]})
                                # For remaining messages, pair each system message with its preceding user message
                                for i in range(1, len(system_messages)):
                                    if i - 1 < len(user_messages):
                                        history.append({"role": "user", "content": user_messages[i - 1]})
                                    history.append({"role": "assistant", "content": system_messages[i]})
                                # Finally, add the latest user message
                                history.append({"role": "user", "content": message})

                                existing_challenges = await get_user_challenges(user_id, BEARER_TOKEN)
                                risks = await fetch_all_risks(BEARER_TOKEN, user_id)
                                recommendations = await fetch_all_recommendations(BEARER_TOKEN, user_id)
                                kz = "\n".join(addition_parts)
                                challenge_addition = f'''
                                Ты — экспертный ассистент по здоровью в мобильном приложении. Твоя задача — мягко предложить пользователю сгенерировать челлендж, если в его сообщении замечен интерес, жалоба, прогресс или цель и с учетом его контекста диалога.
                                ВХОДНЫЕ ДАННЫЕ:
                                - Цель пользователя: {json.loads(goals)["goal"]}
                                - Карта Здоровья: {kz}
                                - Контекст последних сообщений: {history}
                                - Риски здоровья: {risks}
                                - Рекомендации: {recommendations}
                                - Тематики, по которым нужно предлагать челленджи: ["Питание", "Активность", "Сон", "Стресс и эмоции", "Ментальное здоровье", "Цифровая гигиена", "Занятие спортом"]
                                - Активные челленджи: {existing_challenges}
                                '''
                                conf_challenge_ask = {"prompt": challenge_addition + CHALLENGE_ASK}
                                current_date = datetime.now().strftime("%Y:%m:%d")
                                addition = f" ЦЕЛИ: {str(goals)}, " + f"ДАТА НА СЕГОДНЯ, КОТОРУЮ НАДО УЧИТЫВАТЬ, ДЛЯ ЛУЧШЕГО ПОНИМАНИЯ ВРЕМЕННЫХ РАМОК: {current_date}, " + f"КОНТЕКСТ АНКЕТЫ ЗДОРОВЬЯ:" + "\n".join(
                                    addition_parts) + """
                                                                                            7. ПРИОРИТЕТЫ КОНТЕКСТА
                                                                                            Всегда сначала обращай внимание нарисковые и отклоняющиеся параметры(давление, пульс, сахар, генетика, тревожность, ИМТ > 30).
                                                                                            Затем наосновные физиологические данные(рост, вес, возраст, пол).
                                                                                            Далее — наобраз жизни и поведение(сон, питание, активность, стресс).
                                                                                            Затем — нацели и намерения пациента.
                                                                                            В последнюю очередь — на эмоциональные маркеры и фон.
                                                                                            Если пациент упомянул конкретную метрику,сфокусируйся на ней, но можешь деликатно упомянуть одну-две связанные метрики из более высокого приоритета, если они критичны.
                                                                                            Не дублируй, не уточняй и не проси метрики напрямую, но можешь корректно подводить к обсуждению важных параметров, если они критичны для здоровья.
                                                                                            """
                                conf_message = {
                                    "usr_msg": message,
                                    "prompt": CONVERSATION_PROMPT + addition + f" ВОТ ИСТОРИЯ СООБЩЕНИЙ: {history}"
                                }
                                msg, challenge_ask = await asyncio.gather(
                                    run_chatgpt_chat_open_pipeline(conf_message),
                                    run_chatgpt_challenge_ask_pipeline(conf_challenge_ask)
                                )
                                challenge_ask = json.loads(challenge_ask)
                                await add_user_message(user_id, message, entity_type.value, entity_id or '')
                                msg = "[id-5]" + msg + "\n\n" + challenge_ask["reply"]
                                await add_system_message(user_id, msg, entity_type.value, entity_id or '')
                                return {"message": msg}
                            parts = response_msg.split()
                            print("CLASSIFY_PARTS:", parts)
                            if len(parts) >= 2:
                                mode_value = parts[0].strip()
                                try:
                                    mode_weight = float(parts[1])
                                except ValueError:
                                    mode_weight = 0.0
                                # Активируем смену режима только если вес >= 0.7 и mode_value соответствует ожидаемым значениям
                                if (
                                        (current_pending_field and
                                         mode_weight >= 0.7 and mode_value in ["metrics", "conversation", "timeout",
                                                                               "question"]) or "Если хочешь, можем просто пообщаться или продолжить анкетирование" in system_msg or current_pending_field == "allergies"
                                ):
                                    if mode_value != "nothing":
                                        await set_chatting_mode(user_id, mode_value)
                                    mode = await get_chatting_mode(user_id)

                prefix = None
                if str(mode) == "metrics":
                    prefix = "[РЕЖИМ АНКЕТИРОВАНИЯ]"
                if str(mode) == "conversation":
                    prefix = "[РЕЖИМ ОБЩЕНИЯ]"
                if str(mode) == "timeout":
                    prefix = "[РЕЖИМ ТАЙМАУТА]"
                if str(mode) == "question":
                    prefix = "[ВСТРЕЧНЫЙ ВОПРОС]"

                await log_message(user_id, f"РЕЖИМ ПОСЛЕ КЛАССИФИКАЦИИ: {prefix}")

                # Сохраняем сообщение пользователя
                if attempt == 0:
                    await add_user_message(user_id, message, entity_type.value, entity_id or '')

                # consent_key = f"user:{user_id}:consent"
                # existing_consent = await r.get(consent_key)
                # if not existing_consent:
                #     consent = determine_consent(message)
                #     if consent == "no":
                #         msg = ("Как жаль, что сейчас вы не готовы ответить на вопросы, **но вы всегда сможете вернуться к "
                #                "заполнению вашей карты здоровья позднее!** \n\nНапишите в чат **продолжить** и мы продолжим "
                #                "тогда, когда Вам будет удобно.")
                #         return {"message": msg}
                #     else:
                #         await set_chatting_mode(user_id, "metrics")
                #         await r.set(consent_key, consent)

                # Если ожидается ответ по целям, сразу обрабатываем их
                if awaiting_goals:
                    async with log_time(logger, endpoint_name, "if_awaiting_for_goal_input"):
                        conf_goals = {"usr_goals": message, "prompt": GOALS_PROMPT}
                        new_goals = (await run_chatgpt_goals_pipeline(conf_goals)).replace("'", '"')
                        name = json.loads(metrics)['personal_information']['name']
                        if new_goals == "not":
                            msg = f"'{message}' не подходит как цель. Пожалуйста уточните ее."
                        else:
                            await asyncio.gather(
                                add_goals(user_id, new_goals),
                                update_goals_profile(user_id, BEARER_TOKEN, new_goals or ""),
                                clear_awaiting_goals(user_id)
                            )
                            msg = (
                                f"[id-4] **Отличная цель!**\n&#8203;\n\n&#8203;\n\n{name}, базовая карта здоровья **готова**! – классная работа!\n&#8203;\n\n&#8203;\n\n"
                                "Теперь тебе доступны: \n- Первые персональные рекомендации\n- Информация о возможных рисках\n- Первый челлендж\n&#8203;\n\n&#8203;\n\n"
                                "Все это уже на главном экране.\n\nГотов начать челлендж прямо сейчас? Нажми “Перейти в челленджи“ ниже. \n\nЕсли хочешь, можем просто пообщаться или продолжить анкетирование."
                            )
                            first_seven_fields = [
                                "name", "gender", "date_of_birth", "height", "weight", "activity_level",
                                "food_level", "smoking", "alcohol", "sleep_recovery", "stress_level",
                                "chronic_conditions",
                                "allergies", "sports", "regular_medicine", "genetic_conditions", "sleep_quality",
                                "injuries", "diet_balance", "calorie_intake", "sleep_schedule", "work_schedule",
                                "blood_pressure", "preferred_dishes", "diet_schedule", "food_intolerances",
                                "blood_analysis", "biochemical_analysis", "urine_analysis", "lipid_profile",
                                "glucose_tolerance_test",
                                "thyroid_test", "glycated_hemoglobin", "coagulogram", "inflammatory_markers"
                            ]
                            metrics = await get_metrics(user_id)
                            user_metrics_dict = json.loads(str(metrics))
                            addition_parts = []
                            for field in first_seven_fields:
                                field_display = None
                                field_value = None
                                for section in user_metrics_dict.values():
                                    if isinstance(section, dict) and field in section:
                                        field_value = section[field]
                                        field_display = section.get(field + "_field_name", field)
                                        break
                                # Если значение не заполнено или пустая строка – подставляем "Не заполнено"
                                if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
                                    field_value = "Не заполнено"
                                # Если значение — список, преобразуем его в строку через запятую
                                elif isinstance(field_value, list):
                                    if len(field_value) == 0:
                                        field_value = "Не заполнено"
                                    else:
                                        field_value = ", ".join(str(item) for item in field_value)
                                addition_parts.append(f" - {field_display}: {field_value}")

                            user_msgs_task = get_last_user_messages(user_id, "general", '', count=10)
                            system_msgs_task = get_last_system_messages(user_id, "general", '', count=10)
                            user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)

                            history = []
                            if system_messages:
                                # Add the initial system message (e.g. greeting)
                                history.append({"role": "assistant", "content": system_messages[0]})
                            # For remaining messages, pair each system message with its preceding user message
                            for i in range(1, len(system_messages)):
                                if i - 1 < len(user_messages):
                                    history.append({"role": "user", "content": user_messages[i - 1]})
                                history.append({"role": "assistant", "content": system_messages[i]})
                            # Finally, add the latest user message
                            history.append({"role": "user", "content": message})

                            existing_challenges = await get_user_challenges(user_id, BEARER_TOKEN)
                            availableMechanicsDistribution = await get_user_challenges_mechanics(user_id, BEARER_TOKEN)
                            print("EXISTING_CHALLENGES", existing_challenges)

                            def format_mechanic_counts(data: List[Dict[str, str]]) -> str:
                                all_mechanics = [
                                    'dailyChecklist',
                                    'instantAction',
                                    'timedAction',
                                    'goalProgress',
                                    'countdownToTime'
                                ]
                                counts = Counter(item['mechanic'] for item in data)
                                parts = [f"{mech}: {counts.get(mech, 0)}" for mech in all_mechanics]
                                return ", ".join(parts)

                            availableMechanicsDistribution = format_mechanic_counts(availableMechanicsDistribution)
                            print("availableMechanicsDistribution", availableMechanicsDistribution)
                            risks = await fetch_all_risks(BEARER_TOKEN, user_id)
                            recommendations = await fetch_all_recommendations(BEARER_TOKEN, user_id)
                            goals = await get_goals(user_id)
                            kz = "\n".join(addition_parts)
                            addition = f'''
                                                                Ты — умный ассистент в приложении по улучшению здоровья. Сгенерируй до 5 простых и понятных базовых челленджей, персонализированных под пользователя.
                                                                ВХОДНЫЕ ДАННЫЕ:
                                                                - Цель пользователя: {json.loads(goals)["goal"]}
                                                                - Карта Здоровья: {kz}
                                                                - Риски здоровья: {risks}
                                                                - Рекомендации: {recommendations}
                                                                - Контекст последних сообщений: {history}
                                                                - Текущий баланс механик: {availableMechanicsDistribution}
                                                                - Тематики, по которым возможны челленджи: ["Активность", "Спорт", "Питание", "Медицина", "Стресс", "Сон"]
                                                                - Список уже существующих челленджей (чтобы избежать повторов): {existing_challenges}
                                                                '''
                            prompt = addition + " " + GENERATE_CHALLENGES_PROMPT
                            conf_message = {"prompt": prompt}
                            challenges = await run_chatgpt_generate_challenges_pipeline(conf_message)

                            def parse_challenges(raw_output):
                                """
                                Парсит исходную строку с челленджами и возвращает список кортежей (title, category).
                                """
                                # Ищем все вхождения вида {"Текст задания"; "Категория"}
                                pattern = r'\{\s*"([^"]+)"\s*;\s*"([^"]+)"\s*\}'
                                parsed = re.findall(pattern, raw_output)
                                return [list(item) for item in parsed]

                            challenges_list = parse_challenges(challenges)
                            # Если список не пуст, обрабатываем только первый челлендж
                            if challenges_list and len(challenges_list) > 0:
                                title, theme = challenges_list[0]
                                addition = f'''
                                                                    ТЫ — ЭКСПЕРТНЫЙ АССИСТЕНТ ПО ЗДОРОВЬЮ в мобильном приложении. Твоя задача — сгенерировать один персонализированный и технически валидный челлендж на основе пользовательских данных. Челлендж должен быть мотивирующим, структурированным, и готовым к отображению в приложении и запуску через API.
                                                                    ВХОДНЫЕ ДАННЫЕ:
                                                                    - Краткое описание/намерение пользователя: {title}
                                                                    - Предложенная тематика (если указана): {theme}
                                                                    - Цель пользователя: {json.loads(goals)["goal"]}
                                                                    - Карта здоровья: {kz}
                                                                    - Риски здоровья: {risks}
                                                                    - Рекомендации: {recommendations}
                                                                    - Контекст последних сообщений в чате и поведение: {history}
                                                                    - Текущий баланс механик: {availableMechanicsDistribution}
                                                                    '''
                                prompt = addition + " " + DESCRIBE_CHALLENGE_PROMPT
                                conf_metrics = {"usr_msg": message, "prompt": prompt}
                                described = await run_chatgpt_describe_challenge_pipeline(conf_metrics)

                                # Добавляем поле condition и загружаем челлендж
                                challenge_data = json.loads(described.replace("'", '"'))
                                challenge_data["condition"] = "notStarted"
                                await upload_challenge(user_id, challenge_data, BEARER_TOKEN)
                            # msg = (
                            #     f"**Это отличная цель!**\n&#8203;\n\n&#8203;\n\n{name}, твоя базовая анкета здоровья **заполнена**!\n&#8203;\n\n&#8203;\n\n"
                            #     "Теперь тебе доступны: \n- Первые персональные рекомендации\n- Информация о возможных рисках\n&#8203;\n\n&#8203;\n\n"
                            #     "Ты можешь посмотреть их на главной странице.\n\nХочешь продолжить заполнение анкеты или просто пообщаться?"
                            # )
                            key_patterns = [
                                f"user:{user_id}:reask_count:gender",
                                f"user:{user_id}:reask_count:date_of_birth",
                                f"user:{user_id}:reask_count:height",
                                f"user:{user_id}:reask_count:weight",
                                f"user:{user_id}:reask_count:activity_level",
                                f"user:{user_id}:reask_count:food_level",
                                f"user:{user_id}:reask_count:smoking",
                                f"user:{user_id}:reask_count:alcohol",
                                f"user:{user_id}:reask_count:sleep_recovery",
                                f"user:{user_id}:reask_count:stress_level",
                                f"user:{user_id}:reask_count:chronic_conditions",
                                f"user:{user_id}:reask_count:allergies",
                            ]

                            for pattern in key_patterns:
                                async for key in r.scan_iter(pattern):
                                    await r.delete(key)
                            # Используем Redis для хранения перемешанного порядка ключей для пользователя
                            sports_keys_redis_key = f"user:{user_id}:shuffled_sports_keys"
                            sports_keys_shuffled = await r.get(sports_keys_redis_key)
                            if not sports_keys_shuffled:
                                goals = await get_goals(user_id) or ""
                                conf_message = {
                                    "prompt": f"{REORGANIZE_PROMPT} ЦЕЛЬ: {str(goals)}, КОНТЕКСТ: {str(metrics)}"}
                                sports_keys_random = await run_chatgpt_chat_open_pipeline(conf_message)
                                if "json" in str(
                                        sports_keys_random) and "[" in sports_keys_random and "]" in sports_keys_random:
                                    print("JSON в REORGANIZE!", sports_keys_random)
                                    sports_keys_random = sports_keys_random[
                                                         sports_keys_random.find("["):sports_keys_random.rfind("]") + 1]
                                    print("ПОПРАВИЛИ JSON в REORGANIZE!", sports_keys_random)
                                await r.set(sports_keys_redis_key, sports_keys_random)
                            await add_system_message(user_id, msg, entity_type.value, entity_id or '')
                        return {"message": msg}

                async with log_time(logger, endpoint_name, "fix_json_in_reorganize_if_applicable"):
                    sports_keys_redis_key = f"user:{user_id}:shuffled_sports_keys"
                    sports_keys_shuffled = await r.get(sports_keys_redis_key)
                    if not sports_keys_shuffled and goals:
                        goals = await get_goals(user_id) or ""
                        conf_message = {
                            "prompt": f"{REORGANIZE_PROMPT} ЦЕЛЬ: {str(goals)}, КОНТЕКСТ: {str(metrics)}"}
                        sports_keys_random = await run_chatgpt_chat_open_pipeline(conf_message)
                        if "json" in str(sports_keys_random) and "[" in sports_keys_random and "]" in sports_keys_random:
                            print("JSON в REORGANIZE!", sports_keys_random)
                            sports_keys_random = sports_keys_random[
                                                 sports_keys_random.find("["):sports_keys_random.rfind("]") + 1]
                            print("ПОПРАВИЛИ JSON в REORGANIZE!", sports_keys_random)
                        await r.set(sports_keys_redis_key, sports_keys_random)

                # Формирование истории для дальнейшего общения
                history = []
                async with log_time(logger, endpoint_name, "create_history"):
                    if entity_type == EntityType.general:
                        first_seven_fields = [
                            "name", "gender", "date_of_birth", "height", "weight", "activity_level",
                            "food_level", "smoking", "alcohol", "sleep_recovery", "stress_level", "chronic_conditions",
                            "allergies", "sports", "regular_medicine", "genetic_conditions", "sleep_quality",
                            "injuries", "diet_balance", "calorie_intake", "sleep_schedule", "work_schedule",
                            "blood_pressure", "preferred_dishes", "diet_schedule", "food_intolerances",
                            "blood_analysis", "biochemical_analysis", "urine_analysis", "lipid_profile",
                            "glucose_tolerance_test",
                            "thyroid_test", "glycated_hemoglobin", "coagulogram", "inflammatory_markers"
                        ]
                        async with log_time(logger, endpoint_name, "create_history [ожидание обновленной анкеты]"):
                            user_metrics_dict = json.loads(str(metrics))
                        addition_parts = []
                        async with log_time(logger, endpoint_name, "create_history [форматирование анкеты]"):
                            for field in first_seven_fields:
                                field_display = None
                                field_value = None
                                for section in user_metrics_dict.values():
                                    if isinstance(section, dict) and field in section:
                                        field_value = section[field]
                                        field_display = section.get(field + "_field_name", field)
                                        break
                                # Если значение не заполнено или пустая строка – подставляем "Не заполнено"
                                if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
                                    field_value = "Не заполнено"
                                # Если значение — список, преобразуем его в строку через запятую
                                elif isinstance(field_value, list):
                                    if len(field_value) == 0:
                                        field_value = "Не заполнено"
                                    else:
                                        field_value = ", ".join(str(item) for item in field_value)
                                addition_parts.append(f" - {field_display}: {field_value}")
                        current_date = datetime.now().strftime("%Y:%m:%d")
                        addition = f" ЦЕЛИ: {str(goals)}, " + f"ДАТА НА СЕГОДНЯ, КОТОРУЮ НАДО УЧИТЫВАТЬ, ДЛЯ ЛУЧШЕГО ПОНИМАНИЯ ВРЕМЕННЫХ РАМОК: {current_date}, " + f"КОНТЕКСТ АНКЕТЫ ЗДОРОВЬЯ:" + "\n".join(addition_parts) + """
                                    7. ПРИОРИТЕТЫ КОНТЕКСТА
                                    Всегда сначала обращай внимание нарисковые и отклоняющиеся параметры(давление, пульс, сахар, генетика, тревожность, ИМТ > 30).
                                    Затем наосновные физиологические данные(рост, вес, возраст, пол).
                                    Далее — наобраз жизни и поведение(сон, питание, активность, стресс).
                                    Затем — нацели и намерения пациента.
                                    В последнюю очередь — на эмоциональные маркеры и фон.
                                    Если пациент упомянул конкретную метрику,сфокусируйся на ней, но можешь деликатно упомянуть одну-две связанные метрики из более высокого приоритета, если они критичны.
                                    Не дублируй, не уточняй и не проси метрики напрямую, но можешь корректно подводить к обсуждению важных параметров, если они критичны для здоровья.
                                    """
                        # Fetch last 10 system messages and last 9 user messages
                        async with log_time(logger, endpoint_name, "create_history [получение истории сообщений]"):
                            user_msgs_task = get_last_user_messages(user_id, "general", '', count=10)
                            system_msgs_task = get_last_system_messages(user_id, "general", '', count=10)
                            user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)

                        async with log_time(logger, endpoint_name, "create_history [составление итоговой истории сообщений]"):
                            history = []
                            if system_messages:
                                # Add the initial system message (e.g. greeting)
                                history.append({"role": "assistant", "content": system_messages[0]})
                            # For remaining messages, pair each system message with its preceding user message
                            for i in range(1, len(system_messages)):
                                if i - 1 < len(user_messages):
                                    history.append({"role": "user", "content": user_messages[i - 1]})
                                history.append({"role": "assistant", "content": system_messages[i]})
                            # Finally, add the latest user message
                            history.append({"role": "user", "content": message})
                            conf_message = {
                                "usr_msg": message,
                                "prompt": CONVERSATION_PROMPT + addition + f" ВОТ ИСТОРИЯ СООБЩЕНИЙ: {history}"
                            }
                    elif entity_type == EntityType.recommendation:
                        conf_message = {"usr_msg": message, "prompt": CHAT_RISK_RECOMMENDATION}
                        recommendation = await fetch_recommendation_by_id(BEARER_TOKEN, entity_id)
                        title = recommendation["title"]
                        content = recommendation["content"]
                        user_msgs_task = get_last_user_messages(user_id, entity_type.value, entity_id, count=10)
                        system_msgs_task = get_last_system_messages(user_id, entity_type.value, entity_id, count=10)
                        user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)
                        history.append({
                            "role": "system",
                            "content": f"{CHAT_RISK_RECOMMENDATION} Название рекомендации: {title}, Описание: {content}"
                        })
                        for u_msg, s_msg in zip(user_messages, system_messages):
                            history.append({"role": "user", "content": u_msg})
                            history.append({"role": "assistant", "content": s_msg})
                        conf_message["history"] = history
                    else:  # entity_type == EntityType.risk
                        conf_message = {"usr_msg": message, "prompt": CHAT_RISK_RECOMMENDATION}
                        risk = await fetch_risk_by_id(BEARER_TOKEN, entity_id)
                        title = risk["title"]
                        content = risk["content"]
                        user_msgs_task = get_last_user_messages(user_id, entity_type.value, entity_id, count=10)
                        system_msgs_task = get_last_system_messages(user_id, entity_type.value, entity_id, count=10)
                        user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)
                        history.append({
                            "role": "system",
                            "content": f"{CHAT_RISK_RECOMMENDATION} Название риска: {title}, Описание: {content}"
                        })
                        for u_msg, s_msg in zip(user_messages, system_messages):
                            history.append({"role": "user", "content": u_msg})
                            history.append({"role": "assistant", "content": s_msg})
                        conf_message["history"] = history

                #     # if mode_weight >= 0.7 and mode_value == "nothing":
                #     #     conf_message = {"prompt": f"{CLARIFY_PROMPT} Уточнить хочет ли пациент просто поговорить или остаться в заполнении анкеты"}
                #     #     msg = await run_chatgpt_chat_open_pipeline(conf_message   )
                #     #     return {"message": msg}

                # if response_msg in ["metrics", "conversation", "timeout"]:
                #     await set_chatting_mode(user_id, response_msg)

                async with log_time(logger, endpoint_name, "add_prefix_if_changing_modes"):
                    prev_mode_key = f"user:{user_id}:prev_mode"
                    conv_prefix_key = f"user:{user_id}:conv_prefix_shown"
                    prev_mode = await r.get(prev_mode_key)
                    if not prev_mode:
                        prev_mode = str(mode)
                    conv_prefix = ""
                    if str(mode) == "conversation":
                        if prev_mode in ["metrics", "timeout"] and not await r.get(conv_prefix_key):
                            conv_prefix = random.choice(chatting_ending)
                            conv_prefix = "\n&#8203;\n\n&#8203;\n\n" + "*" + conv_prefix + "*"
                            await r.set(conv_prefix_key, "1")
                    else:
                        if str(mode) == "metrics":
                            await r.delete(conv_prefix_key)
                    await r.set(prev_mode_key, str(mode))
                # Стандартный путь обработки
                async with log_time(logger, endpoint_name, "choosing_in_which_mode_to_generate_message"):
                    if entity_type == EntityType.risk:
                        async with log_time(logger, endpoint_name, "generate_message_about_risk"):
                            msg = await run_chatgpt_message_risk_pipeline(conf_message)
                    elif entity_type == EntityType.recommendation:
                        async with log_time(logger, endpoint_name, "generate_message_about_recommendation"):
                            msg = await run_chatgpt_message_recommendation_pipeline(conf_message)
                    else:
                        if str(mode) in ["conversation", "timeout", "question"]:
                            async with log_time(logger, endpoint_name, "generate_message_in_conversation/timeout/question_mode"):
                                mode = await get_chatting_mode(user_id)
                                if mode == "question":
                                    await r.lrem(f"user:chatting_mode:{user_id}", -1, "question")
                                if mode == "timeout":
                                    await r.lrem(f"user:chatting_mode:{user_id}", -1, "timeout")
                                first_seven_fields = [
                                    "name", "gender", "date_of_birth", "height", "weight", "activity_level",
                                    "food_level", "smoking", "alcohol", "sleep_recovery", "stress_level", "chronic_conditions",
                                    "allergies", "sports", "regular_medicine", "genetic_conditions", "sleep_quality",
                                    "injuries", "diet_balance", "calorie_intake", "sleep_schedule", "work_schedule",
                                    "blood_pressure", "preferred_dishes", "diet_schedule", "food_intolerances",
                                    "blood_analysis", "biochemical_analysis", "urine_analysis", "lipid_profile",
                                    "glucose_tolerance_test",
                                    "thyroid_test", "glycated_hemoglobin", "coagulogram", "inflammatory_markers"
                                ]
                                user_metrics_dict = json.loads(str(metrics))
                                addition_parts = []
                                for field in first_seven_fields:
                                    field_display = None
                                    field_value = None
                                    for section in user_metrics_dict.values():
                                        if isinstance(section, dict) and field in section:
                                            field_value = section[field]
                                            field_display = section.get(field + "_field_name", field)
                                            break
                                    # Если значение не заполнено или пустая строка – подставляем "Не заполнено"
                                    if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
                                        field_value = "Не заполнено"
                                    # Если значение — список, преобразуем его в строку через запятую
                                    elif isinstance(field_value, list):
                                        if len(field_value) == 0:
                                            field_value = "Не заполнено"
                                        else:
                                            field_value = ", ".join(str(item) for item in field_value)
                                    addition_parts.append(f" - {field_display}: {field_value}")
                                current_date = datetime.now().strftime("%Y:%m:%d")
                                addition = f" ЦЕЛИ: {str(goals)}, " + f"ДАТА НА СЕГОДНЯ, КОТОРУЮ НАДО УЧИТЫВАТЬ, ДЛЯ ЛУЧШЕГО ПОНИМАНИЯ ВРЕМЕННЫХ РАМОК: {current_date}, " + f"КОНТЕКСТ АНКЕТЫ ЗДОРОВЬЯ:" + "\n".join(addition_parts) + """
                                                            7. ПРИОРИТЕТЫ КОНТЕКСТА
                                                            Всегда сначала обращай внимание нарисковые и отклоняющиеся параметры(давление, пульс, сахар, генетика, тревожность, ИМТ > 30).
                                                            Затем наосновные физиологические данные(рост, вес, возраст, пол).
                                                            Далее — наобраз жизни и поведение(сон, питание, активность, стресс).
                                                            Затем — нацели и намерения пациента.
                                                            В последнюю очередь — на эмоциональные маркеры и фон.
                                                            Если пациент упомянул конкретную метрику,сфокусируйся на ней, но можешь деликатно упомянуть одну-две связанные метрики из более высокого приоритета, если они критичны.
                                                            Не дублируй, не уточняй и не проси метрики напрямую, но можешь корректно подводить к обсуждению важных параметров, если они критичны для здоровья.
                                                            """
                                # Fetch last 10 system messages and last 9 user messages
                                user_msgs_task = get_last_user_messages(user_id, "general", '', count=10)
                                system_msgs_task = get_last_system_messages(user_id, "general", '', count=10)
                                user_messages, system_messages = await asyncio.gather(user_msgs_task, system_msgs_task)

                                history = []
                                if system_messages:
                                    # Add the initial system message (e.g. greeting)
                                    history.append({"role": "assistant", "content": system_messages[0]})
                                # For remaining messages, pair each system message with its preceding user message
                                for i in range(1, len(system_messages)):
                                    if i - 1 < len(user_messages):
                                        history.append({"role": "user", "content": user_messages[i - 1]})
                                    history.append({"role": "assistant", "content": system_messages[i]})
                                # Finally, add the latest user message
                                history.append({"role": "user", "content": message})
                                conf_message = {
                                    "usr_msg": message,
                                    "prompt": CONVERSATION_PROMPT + addition + f" ВОТ ИСТОРИЯ СООБЩЕНИЙ: {history}"
                                }
                                msg = await run_chatgpt_chat_open_pipeline(conf_message)
                                if str(mode) == "conversation":
                                    msg = msg + conv_prefix
                        elif str(mode) == "metrics":
                            async with log_time(logger, endpoint_name, "generate_message_in_metrics_mode"):
                                # 1) Если ждём уточнение по полю…
                                pending = await r.get(f"user:{user_id}:pending_field")
                                if pending:
                                    # собираем 5 последних реплик
                                    u5, s5 = await asyncio.gather(
                                        get_last_user_messages(user_id, "general", '', 5),
                                        get_last_system_messages(user_id, "general", '', 5)
                                    )
                                    history = []
                                    for u, s in zip(u5, s5):
                                        history.append({"role": "user", "content": u})
                                        history.append({"role": "assistant", "content": s})
                                    history.append({"role": "user", "content": message})
                                    # 2) Один вызов — ответ из handle…
                                    resp = await handle_pending_field_answer(
                                        user_id,
                                        pending.decode() if isinstance(pending, (bytes, bytearray)) else pending,
                                        message,
                                        history,
                                        json.loads(metrics)
                                    )

                                    # 3) шлём пользователю вопрос/текст из action
                                    msg = resp["action"]["clarification_question"]
                                    prefix = ""
                                    if str(mode) == "metrics":
                                        prefix = "[РЕЖИМ АНКЕТИРОВАНИЯ]\n"
                                    if str(mode) == "conversation":
                                        prefix = "[РЕЖИМ ОБЩЕНИЯ]\n"
                                    if str(mode) == "timeout":
                                        prefix = "[РЕЖИМ ТАЙМАУТА]\n"
                                    if str(mode) == "question":
                                        prefix = "[ВСТРЕЧНЫЙ ВОПРОС]\n"
                                    await add_system_message(user_id, msg, entity_type.value, entity_id or '')
                                    msg = prefix + msg
                                    return {"message": msg}

                                # 4) Если ни одного pending_field — запускаем первый вопрос
                                #    (тут просто берём первый ключ из questions_first_stage)
                                if not goals:
                                    first = next(iter(questions_first_stage))
                                else:
                                    fixed_keys = [
                                        "name", "gender", "date_of_birth", "height", "weight",
                                        "activity_level", "food_level", "smoking", "alcohol",
                                        "sleep_recovery", "stress_level", "chronic_conditions", "allergies"
                                    ]
                                    sports_str = await r.get(f"user:{user_id}:shuffled_sports_keys")
                                    sports_keys = json.loads(sports_str) if sports_str else []

                                    # объединяем все поля
                                    all_keys = fixed_keys + sports_keys

                                    # строим список только незаполненных или с малым весом метрик
                                    questions_order = []
                                    for k in all_keys:
                                        # находим секцию в metrics_dict, где хранится k
                                        section = next(
                                            (sec for sec in json.loads(metrics).values() if
                                             isinstance(sec, dict) and k in sec),
                                            {}
                                        )
                                        val = section.get(k)
                                        wt = section.get(f"{k}_weight", 0.0)
                                        # если нет значения или вес < 0.7 — добавляем в очередь
                                        if not is_filled(val) or wt < 0.7:
                                            questions_order.append(k)
                                    first = questions_order[0]
                                await r.set(f"user:{user_id}:pending_field", first)
                                resp = await handle_pending_field_answer(
                                    user_id, first, "", [], json.loads(metrics)
                                )
                                msg = resp["action"]["clarification_question"]
                                prefix = ""
                                if str(mode) == "metrics":
                                    prefix = "[РЕЖИМ АНКЕТИРОВАНИЯ]\n"
                                if str(mode) == "conversation":
                                    prefix = "[РЕЖИМ ОБЩЕНИЯ]\n"
                                if str(mode) == "timeout":
                                    prefix = "[РЕЖИМ ТАЙМАУТА]\n"
                                if str(mode) == "question":
                                    prefix = "[ВСТРЕЧНЫЙ ВОПРОС]\n"

                                await add_system_message(user_id, msg, entity_type.value, entity_id or '')
                                msg = prefix + msg
                                return {"message": msg}

                prefix = ""
                if str(mode) == "metrics":
                    prefix = "[РЕЖИМ АНКЕТИРОВАНИЯ]\n"
                if str(mode) == "conversation":
                    prefix = "[РЕЖИМ ОБЩЕНИЯ]\n"
                if str(mode) == "timeout":
                    prefix = "[РЕЖИМ ТАЙМАУТА]\n"
                if str(mode) == "question":
                    prefix = "[ВСТРЕЧНЫЙ ВОПРОС]\n"

                await add_system_message(user_id, msg, entity_type.value, entity_id or '')
                msg = prefix + msg
                return {"message": msg}
            except Exception as e:
                # Если ошибка связана с NoneType, пробуем снова
                if "NoneType" in str(e):
                    attempt += 1
                    await asyncio.sleep(0.2)
                    continue
                raise HTTPException(status_code=500, detail=str(e))
    raise HTTPException(status_code=500, detail="Ошибка при обработке запроса после нескольких попыток")


@app.post("/v1/clear")
async def clear_user(request: ClearUser):
    user_id = request.userId
    key_patterns = [
        f"*:{user_id}:*",
        f"*:{user_id}"
    ]

    for pattern in key_patterns:
        async for key in r.scan_iter(pattern):
            await r.delete(key)
    return {"message": f"{user_id}'s data deleted"}

@app.post("/v1/reset")
async def reset_user(request: ResetUser):
    import httpx
    user_id = request.userId
    key_patterns = [
        f"*:{user_id}:*",
        f"*:{user_id}"
    ]

    for pattern in key_patterns:
        async for key in r.scan_iter(pattern):
            await r.delete(key)

    class EntityType(str, Enum):
        general = "general"
        recommendation = "recommendation"
        risk = "risk"

    metrics = json.loads("""{
                   "personal_information": {
                   "name": "",
                   "name_field_name": "Имя",
                   "name_weight": "null",
                   "date_of_birth": "",
                   "date_of_birth_field_name": "Дата рождения",
                   "date_of_birth_weight": "null",
                   "gender": "",
                   "gender_field_name": "Пол",
                   "gender_weight": "null",
                   "height": "",
                   "height_field_name": "Рост",
                   "height_weight": "null",
                   "weight": "",
                   "weight_field_name": "Вес",
                   "weight_weight": "null"
                   },
                   "lifestyle": {
                   "activity_level": "",
                   "activity_level_field_name": "Уровень активности",
                   "activity_level_weight": "null",
                   "sports": "",
                   "sports_field_name": "Занятия спортом",
                   "sports_weight": "null",
                   "smoking": "",
                   "smoking_field_name": "Курение",
                   "smoking_weight": "null",
                   "alcohol": "",
                   "alcohol_field_name": "Алкоголь",
                   "alcohol_weight": "null",
                   "sleep_recovery": "",
                   "sleep_recovery_field_name": "Восстановление после сна",
                   "sleep_recovery_weight": "null",
                   "sleep_schedule": "",
                   "sleep_schedule_field_name": "Режим сна",
                   "sleep_schedule_weight": "null",
                   "sleep_quality": "",
                   "sleep_quality_field_name": "Качество сна",
                   "sleep_quality_weight": "null",
                   "work_schedule": "",
                   "work_schedule_field_name": "Режим работы",
                   "work_schedule_weight": "null",
                   "stress_level": "",
                   "stress_level_field_name": "Уровень стресса",
                   "stress_level_weight": "null"
                   },
                   "health_status": {
                   "blood_pressure": "",
                   "blood_pressure_field_name": "Давление",
                   "blood_pressure_weight": "null",
                   "chronic_conditions": [],
                   "chronic_conditions_field_name": "Хронические заболевания",
                   "chronic_conditions_weight": "null",
                   "injuries": "",
                   "injuries_field_name": "Травмы",
                   "injuries_weight": "null",
                   "genetic_conditions": [],
                   "genetic_conditions_field_name": "Генетические заболевания",
                   "genetic_conditions_weight": "null",
                   "regular_medicine": [],
                   "regular_medicine_field_name": "Лекарства",
                   "regular_medicine_weight": "null",
                   "allergies": [],
                   "allergies_field_name": "Аллергии",
                   "allergies_weight": "null"
                   },
                   "nutrition": {
                   "food_level": "",
                   "food_level_field_name": "Уровень питания",
                   "food_level_weight": "null",
                   "diet_schedule": "",
                   "diet_schedule_field_name": "Режим питания",
                   "diet_schedule_weight": "null",
                   "preferred_dishes": [],
                   "preferred_dishes_field_name": "Предпочитаемые блюда",
                   "preferred_dishes_weight": "null",
                   "diet_balance": "",
                   "diet_balance_field_name": "Баланс рациона",
                   "diet_balance_weight": "null",
                   "food_intolerances": [],
                   "food_intolerances_field_name": "Пищевая непереносимость",
                   "food_intolerances_weight": "null",
                   "calorie_intake": "",
                   "calorie_intake_field_name": "Калорийность",
                   "calorie_intake_weight": "null",
                   "dietary_supplement": "",
                   "dietary_supplement_field_name": "Пищевые добавки",
                   "dietary_supplement_weight": "null"
                   },
                   "analysis": {
                   "blood_analysis": "",
                   "blood_analysis_field_name": "Анализ крови",
                   "blood_analysis_weight": "null",
                   "biochemical_analysis": "",
                   "biochemical_analysis_field_name": "Биохимический анализ",
                   "biochemical_analysis_weight": "null",
                   "urine_analysis": "",
                   "urine_analysis_field_name": "Анализ мочи",
                   "urine_analysis_weight": "null",
                   "lipid_profile": "",
                   "lipid_profile_field_name": "Липидный профиль",
                   "lipid_profile_weight": "null",
                   "glucose_tolerance_test": "",
                   "glucose_tolerance_test_field_name": "Глюкозотолерантный тест",
                   "glucose_tolerance_test_weight": "null",
                   "thyroid_test": "",
                   "thyroid_test_field_name": "Тиреоидный тест",
                   "thyroid_test_weight": "null",
                   "glycated_hemoglobin": "",
                   "glycated_hemoglobin_field_name": "Гликированный гемоглобин",
                   "glycated_hemoglobin_weight": "null",
                   "coagulogram": "",
                   "coagulogram_field_name": "Коагулограмма",
                   "coagulogram_weight": "null",
                   "inflammatory_markers": "",
                   "inflammatory_markers_field_name": "Маркеры воспаления",
                   "inflammatory_markers_weight": "null" }
                   }""")
    # Глобальный клиент
    token = "12c082765fbe925e59e0c4496cc395398bf9f800787dba680f65f195e9dd38196b217bc693049db8b15240b7048c5860bcd74dec65fd585ab06c70b0ecdb6aa45ba62e7bb7f15edc678920b18cadefdcd36b8019205434d235a92ab1cd70882023ae55ac6a7c236e091cf206519738ab4b090964ff73c036351a400f6b83e6b8"
    API_BASE_URL = "https://root.biome-dev-api.work/api"

    async with httpx.AsyncClient() as client:
        async def update_health_profile(user_id, bearer_token, metrics):
            url = f"{API_BASE_URL}/users/{user_id}"
            headers = {
                "Authorization": f"Bearer {bearer_token}",
                "Content-Type": "application/json"
            }
            data = {"healthProfile": metrics}

            resp = await client.put(url, headers=headers, json=data)
            return resp

        async def delete_user_recommendations_from_db(bearer_token, user_id):
            url = f"{API_BASE_URL}/recommendations?filters[user][id][$eq]={user_id}"
            headers = {
                "Authorization": f"Bearer {bearer_token}",
                "Content-Type": "application/json",
            }
            try:
                response = await client.get(url, headers=headers)
                if response.status_code == 200:
                    recommendations = response.json()["data"]
                    for recommendation in recommendations:
                        rec_id = recommendation["documentId"]
                        delete_url = f"{API_BASE_URL}/recommendations/{rec_id}"
                        delete_response = await client.delete(delete_url, headers=headers)
                        if delete_response.status_code != 204:
                            print(f"Ошибка удаления рекомендации {rec_id}: {delete_response.text}")
                        else:
                            print('Удалено')
                else:
                    print(f"Failed to get recommendations for user {user_id}: {response.status_code}")
            except Exception as e:
                print(f"Ошибка при удалении рекомендаций: {e}")

        async def delete_user_risks_from_db(bearer_token, user_id):
            url = f"{API_BASE_URL}/risks?filters[user][id][$eq]={user_id}"
            headers = {
                "Authorization": f"Bearer {bearer_token}",
                "Content-Type": "application/json",
            }
            try:
                response = await client.get(url, headers=headers)
                if response.status_code == 200:
                    risks = response.json()["data"]
                    for risk in risks:
                        risk_id = risk["documentId"]
                        delete_url = f"{API_BASE_URL}/risks/{risk_id}"
                        delete_response = await client.delete(delete_url, headers=headers)
                        if delete_response.status_code != 204:
                            print(f"Ошибка удаления риска {risk_id}: {delete_response.text}")
                        else:
                            print('Удалено')
                else:
                    print(f"Failed to get risks for user {user_id}: {response.status_code}")
            except Exception as e:
                print(f"Ошибка при удалении рисков: {e}")

        async def delete_user_challenges_from_db(bearer_token, user_id):
            url = f"{API_BASE_URL}/challenges?filters[user][id][$eq]={user_id}"
            headers = {
                "Authorization": f"Bearer {bearer_token}",
                "Content-Type": "application/json",
            }
            try:
                response = await client.get(url, headers=headers)
                if response.status_code == 200:
                    challenges = response.json()["data"]
                    for challenge in challenges:
                        challenge_id = challenge["documentId"]
                        delete_url = f"{API_BASE_URL}/challenges/{challenge_id}"
                        delete_response = await client.delete(delete_url, headers=headers)
                        if delete_response.status_code != 204:
                            print(f"Ошибка удаления риска {challenge_id}: {delete_response.text}")
                        else:
                            print('Удалено')
                else:
                    print(f"Failed to get risks for user {user_id}: {response.status_code}")
            except Exception as e:
                print(f"Ошибка при удалении челленджа: {e}")

        async def update_goals_profile(user_id, bearer_token, goals):
            url = f"{API_BASE_URL}/users/{user_id}"
            headers = {
                "Authorization": f"Bearer {bearer_token}",
                "Content-Type": "application/json"
            }
            data = {"goals": goals}

            resp = await client.put(url, headers=headers, json=data)
            return resp

        async def delete_existing_chats_by_document_id(user_id: int, bearer_token: str, entity_type: Optional[str] = None,
                                                       entity_id: Optional[int] = None):
            headers = {
                "Authorization": f"Bearer {bearer_token}",
                "Content-Type": "application/json"
            }

            # Формируем параметры для фильтрации существующих чатов
            params = {
                "filters[users_permissions_user][id][$eq]": user_id
            }
            if entity_type == "risk" and entity_id is not None:
                params["filters[risk][id][$eq]"] = entity_id
            elif entity_type == "recommendation" and entity_id is not None:
                params["filters[recommendation][id][$eq]"] = entity_id
            elif entity_type == "general":
                pass  # Для общего чата дополнительных фильтров не требуется

            try:
                # Получаем существующие чаты
                fetch_response = await client.get(f"{API_BASE_URL}/chats", headers=headers, params=params)
                if fetch_response.status_code == 200:
                    existing_chats = fetch_response.json().get("data", [])
                    for chat in existing_chats:
                        document_id = chat.get("documentId")  # Получаем documentId
                        if not document_id:
                            print("Document ID отсутствует в данных чата. Пропускаем.")
                            continue

                        delete_url = f"{API_BASE_URL}/chats/{document_id}"
                        delete_response = await client.delete(delete_url, headers=headers)
                        if delete_response.status_code == 200:  # Успешное удаление
                            print(f"Чат с documentId {document_id} успешно удалён.")
                        else:
                            print(
                                f"Ошибка при удалении чата с documentId {document_id}: {delete_response.status_code}, {delete_response.text}")
                else:
                    print(f"Не удалось получить существующие чаты: {fetch_response.status_code}, {fetch_response.text}")
            except Exception as e:
                print(f"Ошибка при удалении существующих чатов: {e}")

        async def upload_risk(user_id, risk_data, bearer_token):
            headers = {
                "Authorization": f"Bearer {bearer_token}",
                "Content-Type": "application/json"
            }
            url = f"{API_BASE_URL}/risks"
            payload = {
                "data": {
                    "title": risk_data["title"],
                    "shortDescription": risk_data["shortDescription"],
                    "content": risk_data["content"],
                    "user": user_id,
                    "assessment": risk_data["assessment"],
                }
            }
            response = await client.post(url, headers=headers, json=payload)
            if response.status_code == 201:
                print(f"Risk uploaded successfully: {response.json()}")
            else:
                print(f"Failed to upload risk: {response.status_code}, {response.text}")

        risks = json.loads("""{
                           "risk 0": { "title": "Сердечно-сосудистые заболевания", "shortDescription": "Болезни сердца и сосудов, повышающие риск инфаркта, инсульта и гипертонии.", "content": "", "assessment": 0.0},
                           "risk 1": { "title": "Диабет", "shortDescription": "Хроническое нарушение сахара в крови, увеличивающее риск слепоты, ампутации и инфаркта.", "content": "", "assessment": 0.0},
                           "risk 2": { "title": "Нейродегенеративные заболевания", "shortDescription": "Патологии нервной системы, приводящие к ухудшению памяти, движения и когнитивных функций.", "content": "", "assessment": 0.0}
                           }""")

        async def process_user(user_id, bearer_token, metrics, semaphore):

            async with semaphore:
                await delete_existing_chats_by_document_id(user_id, bearer_token)
                await update_health_profile(user_id, bearer_token, None)
                await update_goals_profile(user_id, bearer_token, None)
                await delete_user_risks_from_db(bearer_token, user_id)
                await delete_user_challenges_from_db(bearer_token, user_id)
                await delete_user_recommendations_from_db(bearer_token, user_id)

                for _, risk in risks.items():
                    await upload_risk(user_id, risk, bearer_token)

        semaphore = asyncio.Semaphore(10)  # Ограничение до 10 одновременных задач
        tasks = []
        user_id = int(user_id)
        print(user_id)
        tasks.append(process_user(user_id, token, metrics, semaphore))
        await asyncio.gather(*tasks)
        return {"message": f"{user_id}'s data was reset"}


@app.post("/v2/upload")
async def upload_file(request: FileRequest, http_request: Request):
    # Получаем последнюю загруженную картинку
    user_id = request.userId
    goals = await get_goals(user_id)
    user_jwt_token = http_request.headers.get("authorization")
    print(user_jwt_token)
    logger = get_user_logger(user_id)
    endpoint_name = "/v2/upload"
    last_exception = None
    max_attempts = 3
    attempt = 0

    await log_message(user_id, f"\n")
    await log_message(user_id, f"=====НОВЫЙ АНАЛИЗ ФАЙЛА/ФОТО=====")

    async with log_time(logger, endpoint_name, "full_request"):
        while attempt < max_attempts:
            try:
                if not goals:
                    msg = "Анализ фото и пдф будет доступен после успешного прохождения первых 12 вопросов. Сейчас, пожалуйста, продолжайте."
                    await add_system_message(user_id, msg, "general", '')
                    return {"message": msg}
                async with log_time(logger, endpoint_name, "get_last_uploaded_image"):
                    last_image = await get_last_uploaded_image(request.fileId, BEARER_TOKEN, user_id, user_jwt_token)
                # Проверяем, содержит ли объект необходимый URL
                id = ""
                name = ""
                extension = ""
                image_url = ""

                if user_jwt_token:
                    id = last_image.get("id")
                    name = last_image.get("name")
                    extension = last_image.get("ext")
                else:
                    image_url = last_image.get("url")
                    extension = last_image.get("ext")
                    name = last_image.get("name")

                text = ""
                if extension in [".jpg", ".png", ".jpeg"]:
                    text = f'{{"imageId": "{request.fileId}", "text": "{request.message}"}}'
                elif extension in [".pdf"]:
                    text = f'{{"fileId": "{request.fileId}", "name": "{name}"}}'
                await add_user_message(user_id, f"{text}", "general", '')
                async with log_time(logger, endpoint_name, "download_file"):
                    if user_jwt_token:
                        file_path = f"https://root.biome-dev-api.work/api/secure-files/{id}/file"
                        headers = {
                            "Authorization": f"{user_jwt_token}",
                            "Content-Type": "application/json"
                        }
                        async with httpx.AsyncClient(timeout=60.0) as client:
                            response = await client.get(file_path, headers=headers)
                            if response.status_code != 200:
                                raise HTTPException(
                                    status_code=response.status_code,
                                    detail="Failed to download the image file"
                                )
                    else:
                        API_BASE_URL = "https://root.biome-dev-api.work"
                        file_path = f"{API_BASE_URL}{image_url}"

                        # Загружаем файл через HTTP
                        async with httpx.AsyncClient(timeout=60.0) as client:
                            response = await client.get(file_path)
                            if response.status_code != 200:
                                raise HTTPException(
                                    status_code=response.status_code,
                                    detail="Failed to download the image file"
                                )

                # Кодируем файл в Base64 (или выполняем другую необходимую обработку)
                async with log_time(logger, endpoint_name, "encode_image_to_base64"):
                    file_data = base64.b64encode(response.content).decode("utf-8")
                    if extension == ".jpg":
                        base64_image = f"data:image/jpg;base64,{file_data}"
                    elif extension == ".jpeg":
                        base64_image = f"data:image/jpeg;base64,{file_data}"
                    elif extension == ".png":
                        base64_image = f"data:image/png;base64,{file_data}"
                oa_type = ""
                if extension in [".jpg", ".png", ".jpeg"]:
                    async with log_time(logger, endpoint_name, "process_image"):
                        conf_image = {
                        "image": base64_image,
                        "prompt": IMAGE_PROMPT
                        }
                        conf_image_object = {
                            "image": base64_image,
                            "prompt": OBJECT_PROMPT
                        }
                        # Попытаемся выполнить pipeline. Если получаем ошибку 413 – сожмем изображение и попробуем снова.
                        async with log_time(logger, endpoint_name, "process_image_or_compress_and_process"):
                            for attempt in range(2):
                                try:
                                    object_analysis, analysis = await asyncio.gather(
                                        run_chatgpt_image_object_pipeline(conf_image_object),
                                        run_chatgpt_image_pipeline(conf_image)
                                    )
                                    break  # Если pipeline отработал успешно – выходим из цикла
                                except Exception as e:
                                    if "413" in str(e) and attempt == 0:
                                        # Сжимаем изображение
                                        try:
                                            # Извлекаем данные без префикса
                                            prefix, b64_data = base64_image.split(",", 1)
                                            image_data = base64.b64decode(b64_data)
                                            img = Image.open(BytesIO(image_data))
                                            # Уменьшаем изображение – например, в 2 раза
                                            new_size = (img.width // 2, img.height // 2)
                                            img = img.resize(new_size, Image.Resampling.LANCZOS)
                                            buffer = BytesIO()
                                            if extension.lower() in [".jpg", ".jpeg"]:
                                                img.save(buffer, format="JPEG", quality=75)
                                            else:
                                                img.save(buffer, format="PNG", optimize=True)
                                            compressed_bytes = buffer.getvalue()
                                            compressed_b64 = base64.b64encode(compressed_bytes).decode("utf-8")
                                            # Формируем новое значение base64_image
                                            base64_image = f"data:image/{extension.lstrip('.')};base64,{compressed_b64}"
                                            conf_image["image"] = base64_image
                                            conf_image_object["image"] = base64_image
                                            # Переходим ко второй попытке
                                            continue
                                        except Exception as e2:
                                            raise Exception(f"Ошибка при сжатии изображения: {str(e2)}")
                                    else:
                                        raise e
                            else:
                                raise Exception("Не удалось обработать изображение после сжатия.")

                        # prompt = IMAGE_RESPONSE_PROMPT + ' ' + str(analysis) + '. И вот сообщение пользователя: ' + request.message
                        # conf_message = {"prompt": prompt}
                        # message = await run_chatgpt_chat_open_pipeline(conf_message)

                        async with log_time(logger, endpoint_name, "process_analysis_that_is_not_empty"):
                            if str(analysis) != "{}":
                                try:
                                    if isinstance(object_analysis, dict):
                                        oa_type = object_analysis.get("type")
                                    else:
                                        oa_type = json.loads(object_analysis).get("type")
                                except Exception:
                                    oa_type = None
                                if oa_type != "food":
                                    metrics_msg = 'Вот мой JSON OCR фото документа, который касается каких-то из этих полей (blood_analysis, biochemical_analysis, urine_analysis, lipid_profile, glucose_tolerance_test, thyroid_test, glycated_hemoglobin, coagulogram, inflammatory_markers): ' + str(
                                        analysis)
                                    await add_message_to_queue(request.userId, metrics_msg)

                        async with log_time(logger, endpoint_name, "add_object_analysis"):
                            metrics = await get_metrics(user_id)
                            while not metrics:
                                await asyncio.sleep(0.1)
                                metrics = await get_metrics(user_id)
                            goals = await get_goals(user_id) or ""
                            await add_object_analysis(user_id, object_analysis)
                            try:
                                if isinstance(object_analysis, dict):
                                    oa_type = object_analysis.get("type")
                                else:
                                    oa_type = json.loads(object_analysis).get("type")
                            except Exception:
                                oa_type = None

                        async with log_time(logger, endpoint_name, "process_analysis_that_is_empty_food_or_photo"):
                            if analysis == "{}" or not analysis:
                                if oa_type == "food":
                                    example = """🍔 Бургер: три говяжьи котлеты, расплавленный сыр, жареный лук, соус и булочка.
                                                    🍟 Картошка фри: средняя порция в металлической корзинке.
                                                ✅ Бургер (на глаз):
                                                    •	Калорийность: ≈ X ккал
                                                    •	БЖУ:
                                                    •	Белки: ≈ X г
                                                    •	Жиры: ≈ X г
                                                    •	Углеводы: ≈ X г
                                                ✅ Картошка фри (X г):
                                                    •	Калорийность: ≈ X ккал
                                                    •	БЖУ:
                                                    •	Белки: ≈ X г
                                                    •	Жиры: ≈ X г
                                                    •	Углеводы: ≈ X г
                                                Хочешь точнее? подскажи пожалуйста:
                                                    •	Сколько ты съел от бургера и картошки? 🍔🍟
                                                    •	Знаешь ли их вес в граммах? Это поможет точнее рассчитать калории. 💪"""
                                    prompt = (
                                                CONVERSATION_PROMPT + "ТВОЯ ТЕКУЩАЯ ЗАДАЧА: Расскажи про СОДЕРЖАНИЕ ФОТО, описывая калораж и бкжу, а также сколько грамм в данном блюде, с учетом сообщения пользователя: " + request.message + ", с использованием markdown пунтов '-'. Также очень кратко привяжи данный калларож + бкжу к цели пользователя: " + str(
                                            goals)
                                                + ". Вот пример в каком формате надо выводить, также используя эмодзи: " + example + ". СОДЕРЖАНИЕ ФОТО: " + str(
                                            object_analysis))
                                else:
                                    prompt = CONVERSATION_PROMPT + "ТВОЯ ТЕКУЩАЯ ЗАДАЧА: Расскажи про СОДЕРЖАНИЕ ФОТО, С учетом сообщения пользователя:" + request.message + ". В конце ОБЯЗАТЕЛЬНО спросить чем помочь с этим фото. СОДЕРЖАНИЕ ФОТО: " + str(
                                        object_analysis)
                            else:
                                prompt = (
                                            CONVERSATION_PROMPT + "ТВОЯ ТЕКУЩАЯ ЗАДАЧА: Расскажи про JSON OCR ФОТО, с учетом СОДЕРЖАНИЯ ФОТО, и с учетом сообщения пользователя: " + request.message + ", описывая с использованием markdown пунктов '-'. "
                                            + "JSON OCR ФОТО: " + str(analysis) + ". СОДЕРЖАНИЕ ФОТО: " + str(object_analysis))
                            conf_message = {"prompt": prompt}
                            msg = await run_chatgpt_chat_open_pipeline(conf_message)

                elif extension == ".pdf":
                    async with log_time(logger, endpoint_name, "process_pdf"):
                        try:
                            doc = pymupdf.open(stream=response.content, filetype='pdf')
                            if doc.page_count > 50:
                                msg = "Данный PDF файл слишком большой. Пожалуйста сократите его, либо выберите другой файл"
                                await add_system_message(request.userId, msg, "general", '')
                                return {"message": msg}
                        except Exception as e:
                            # Если ошибка при проверке, можно залогировать, но продолжить обработку (опционально)
                            print("Ошибка при проверке числа страниц PDF: ", e)

                        redis_key = f"user:{user_id}:pdf:{request.fileId}"
                        await r.set(redis_key, response.content)
                        conf_pdf = {"redis_key": redis_key}
                        analysis = await run_chatgpt_pdf_pipeline(conf_pdf)

                        metrics_msg = 'Вот мой JSON OCR фото документа, который касается каких-то из этих полей (blood_analysis, biochemical_analysis, urine_analysis, lipid_profile, glucose_tolerance_test, thyroid_test, glycated_hemoglobin, coagulogram, inflammatory_markers): ' + str(
                            analysis)
                        await add_message_to_queue(request.userId, metrics_msg)
                        metrics = await get_metrics(user_id)
                        while not metrics:
                            await asyncio.sleep(0.1)
                            metrics = await get_metrics(user_id)
                        prompt = CONVERSATION_PROMPT + ". ТВОЯ ТЕКУЩАЯ ЗАДАЧА: Расскажи про JSON OCR ФОТО ДОКУМЕНТА, описывая с использованием markdown пунктов '-', С учетом сообщения пользователя:" + request.message + ". JSON OCR ФОТО ДОКУМЕНТА: " + str(
                            analysis)
                        conf_message = {"prompt": prompt}
                        msg = await run_chatgpt_chat_open_pipeline(conf_message)
                prefix = ""
                if oa_type == "food":
                    prefix = "[id-1] "
                msg = prefix + msg
                await add_system_message(user_id, msg, "general", '')
                return {"message": msg}
            except Exception as e:
                last_exception = e
                if "NoneType" in str(e):
                    attempt += 1
                    await asyncio.sleep(0.2)
                    continue
                raise HTTPException(status_code=500, detail=str(e))
        raise HTTPException(status_code=500, detail=f"Ошибка при обработке файла: {str(last_exception)}")


@app.post("/v2/chat/open")
async def open_chat_v2(request: V2ChatOpenRequest):
    max_attempts = 3
    attempt = 0
    while attempt < max_attempts:
        try:
            user_id = request.userId
            key_patterns = [
                # f"user:messages-*:{user_id}",
                # f"system:messages-*:{user_id}",
                f"user:metrics:{user_id}",
                f"user:goals:{user_id}",
                f"user:recommendations:{user_id}",
                f"user:risks:{user_id}"
            ]

            for pattern in key_patterns:
                async for key in r.scan_iter(pattern):
                    await r.delete(key)

            # СОБИРАЕМ ДАННЫЕ ПО ПОЛЬЗОВАТЕЛЮ
            user_data = await fetch_user_data(BEARER_TOKEN, user_id)
            health_profile = user_data.get("healthProfile", {})
            if health_profile is not None:
                health_profile_json = json.dumps(health_profile)
                await add_metric(user_id, health_profile_json)
            else:
                await add_metric(user_id, TEST_METRICS)
            goals = user_data.get("goals", {})
            if goals is not None:
                goals_json = json.dumps(goals)
                await add_goals(user_id, goals_json)
            timezone = user_data.get("timezone", "")
            if timezone is not None:
                await add_timezone(user_id, timezone)

            recommendations = await fetch_all_recommendations(BEARER_TOKEN, user_id)
            risks = await fetch_all_risks(BEARER_TOKEN, user_id)

            generated_for_range = await r.get(f"user:{user_id}:generated:26-31")
            final_added = await r.get(f"user:{user_id}:final_added")
            if not (recommendations or risks) and not generated_for_range and final_added:
                async def generate_and_upload(conf_recommendations, conf_risks):
                    await asyncio.gather(
                        delete_user_recommendations_from_db(BEARER_TOKEN, user_id),
                        delete_user_risks_from_db(BEARER_TOKEN, user_id)
                    )
                    recommendations = await run_perplexity_pipeline(conf_recommendations)
                    rec_dict = json.loads(recommendations.replace("'", '"'))
                    rec_tasks = []
                    for _, rec in rec_dict.items():
                        rec_json = json.dumps(rec)
                        rec_tasks.append(add_recommendation_local(user_id, rec_json))
                        rec_tasks.append(upload_recommendation(user_id, rec, BEARER_TOKEN))
                    risks = await run_perplexity_pipeline(conf_risks)
                    risk_dict = json.loads(risks.replace("'", '"'))
                    risk_tasks = []
                    for _, risk in risk_dict.items():
                        risk_json = json.dumps(risk)
                        risk_tasks.append(add_risk_local(user_id, risk_json))
                        risk_tasks.append(upload_risk(user_id, risk, BEARER_TOKEN))
                    await asyncio.gather(*(rec_tasks + risk_tasks))

                conf_recommendations = {"healthProfile": str(health_profile), "prompt": RECOMMENDATIONS_PROMPT, "goals": goals}
                conf_risks = {"healthProfile": str(health_profile), "prompt": RISKS_PROMPT, "goals": goals}
                await generate_and_upload(conf_recommendations, conf_risks)
                await mark_as_generated_for_range(user_id, "26-31")

            for rec in recommendations:
                rec_json = json.dumps(rec)
                await add_recommendation_local(user_id, rec_json)
            for risk in risks:
                risk_json = json.dumps(risk)
                await add_risk_local(user_id, risk_json)

            # chats = await fetch_all_chats(BEARER_TOKEN, user_id)

            # for chat in chats:
            #     messages = chat.get('content', [])
            #     for message in messages:
            #         entity_type = message.get('entity_type', 'general')
            #         if entity_type == 'general':
            #             entity_id = ''
            #         else:
            #             entity_id = message.get('entity_id', '')
            #         is_user_message = message.get('isUserMessage', False)
            #         msg_text = message.get('message', '').strip()
            #
            #         if not msg_text:
            #             continue
            #
            #         if is_user_message:
            #             await add_user_message(user_id, msg_text, entity_type, entity_id)
            #         else:
            #             await add_system_message(user_id, msg_text, entity_type, entity_id)

            if request.entityType == EntityType.general:
                entity_id = ''
            else:
                entity_id = request.entityId

            # ГЕНЕРИРУЕМ ПЕРВОЕ СООБЩЕНИЕ
            message = ""
            user_messages_key = f"user:messages-{request.entityType.value}{entity_id}:{user_id}"
            system_messages_key = f"system:messages-{request.entityType.value}{entity_id}:{user_id}"

            user_messages_count = await r.llen(user_messages_key)
            system_messages_count = await r.llen(system_messages_key)

            if user_messages_count > 0 or system_messages_count > 0:
                if request.entityType == EntityType.recommendation:
                    message = await get_last_list_item(f"system:messages-recommendation{entity_id}:{user_id}")
                elif request.entityType == EntityType.risk:
                    message = await get_last_list_item(f"system:messages-risk{entity_id}:{user_id}")
                else:
                    message = await get_last_list_item(f"system:messages-general:{user_id}")
            else:
                if request.entityType == EntityType.general:
                    prompt = None
                elif request.entityType == EntityType.recommendation:
                    recommendation = await fetch_recommendation_by_id(BEARER_TOKEN, request.entityId)
                    title = recommendation["title"]
                    content = recommendation["content"]
                    prompt = CHAT_RISK_RECOMMENDATION + "ВАЖНО Дай пошаговые инструкции и развернутые пояснения к данной рекоменадации: Название рекомендации: " + title + " Описание рекомендации " + content
                else:
                    risk = await fetch_risk_by_id(BEARER_TOKEN, request.entityId)
                    title = risk["title"]
                    content = risk["content"]
                    prompt = CHAT_RISK_RECOMMENDATION + " Название риска: " + title + " Описание риска " + content

                conf_message = {"prompt": prompt}

                if request.entityType in [EntityType.risk, EntityType.recommendation]:
                    message = await run_chatgpt_chat_open_rec_risk_pipeline(conf_message)
                else:
                    message = "**Я — ВIОМE, ваш персональный гид к здоровью и долголетию.**\n\nВместе мы сделаем вашу жизнь ярче, здоровее и насыщеннее!\n\nЧтобы предложить вам рекомендации, которые действительно работают, мне нужно лучше вас узнать:\n&#8203;\n\n&#8203;\n\n- ваш образ жизни\n- привычки\n- состояние здоровья\n&#8203;\nВсего 12 вопросов – и мы сможем создать план, идеально подходящий именно вам. Давайте сделаем первый шаг к вашему здоровому будущему!\n&#8203;\n\n&#8203;\n\n**Готовы начать?**"
                    await set_chatting_mode(user_id, "metrics")
                    prev_mode_key = f"user:{user_id}:prev_mode"
                    await r.set(prev_mode_key, "metrics")
                await add_system_message(
                    request.userId,
                    message,
                    request.entityType.value,
                    request.entityId or ''
                )

            return {"message": message}

        except Exception as e:
            if "NoneType" in str(e):
                attempt += 1
                await asyncio.sleep(0.2)
                continue
            raise HTTPException(status_code=500, detail=str(e))
    raise HTTPException(status_code=500, detail="Ошибка при обработке запроса после нескольких попыток")



@app.post("/v2/chat/close")
async def close_chat_v2(request: V2ChatCloseRequest):
    user_id = request.userId
    entity_type = request.entityType.value
    entity_id = request.entityId

    max_attempts = 3
    attempt = 0
    while attempt < max_attempts:
        try:
            await delete_existing_chats_by_document_id(
                user_id=user_id,
                bearer_token=BEARER_TOKEN,
                entity_type=entity_type,
                entity_id=entity_id
            )

            await upload_chat_messages_for_closing(
                user_id=user_id,
                bearer_token=BEARER_TOKEN,
                r=r,
                entity_type=entity_type,
                entity_id=entity_id
            )

            return {"status": "success"}

        except Exception as e:
            if "NoneType" in str(e):
                attempt += 1
                await asyncio.sleep(0.2)
                continue
            raise HTTPException(status_code=500, detail=str(e))
    raise HTTPException(status_code=500, detail="Ошибка при обработке запроса после нескольких попыток")

# Просмотр затрат
@app.get("/cost-stats")
async def get_cost_stats():
    """Получить статистику по затратам из Redis"""
    try:
        import redis
        import json
        from datetime import datetime, timedelta

        import os
        redis_host = os.getenv('REDIS_HOST', 'redis')
        r = redis.Redis(host=redis_host, port=6379, db=0, decode_responses=True)

        
        cost_keys = []
        for key in r.scan_iter("cost_tracking:*"):
            cost_keys.append(key)

        total_cost = 0.0
        total_operations = 0
        operations_by_dag = {}

        for key in cost_keys:  # Обрабатываем ВСЕ операции
            try:
                data = json.loads(r.get(key))
                total_cost += data.get("total_cost", 0)
                total_operations += 1

                dag_id = data.get("dag_id", "unknown")
                if dag_id not in operations_by_dag:
                    operations_by_dag[dag_id] = {"count": 0, "cost": 0.0}

                operations_by_dag[dag_id]["count"] += 1
                operations_by_dag[dag_id]["cost"] += data.get("total_cost", 0)

            except Exception as e:
                continue

        return {
            "total_cost": round(total_cost, 6),
            "total_operations": total_operations,
            "operations_by_dag": operations_by_dag,
            "generated_at": datetime.now().isoformat(),
            "message": "💰 Статистика затрат Biome AI"
        }

    except Exception as e:
        return {
            "error": str(e),
            "message": "Ошибка получения статистики затрат"
        }
