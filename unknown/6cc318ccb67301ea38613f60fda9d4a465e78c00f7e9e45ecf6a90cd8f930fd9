from datetime import datetime
import configparser
import requests
from airflow.decorators import dag, task
import tiktoken
from cost_tracking import track_openai_cost

# Читаем конфиг и извлекаем токен
config = configparser.ConfigParser()
config.read("/opt/airflow/pipe/config.ini")
API_TOKEN_GPT = config.get('TOKENS', 'API_TOKEN_GPT')

default_args = {
    'owner': '<PERSON><PERSON>ov',
    'retries': 0,
}

@dag(dag_id='chatgpt_message_recommendation_pipeline',
     default_args=default_args,
     start_date=datetime.now(),
     catchup=False,
     schedule_interval=None)
def chatgpt():
    @task()
    def generate_openai_response(**kwargs):
        

        conf = kwargs.get('dag_run').conf

        if not conf or "usr_msg" not in conf:
            raise ValueError("Не найдено сообщение пользователя в конфигурации DAG.")
        msg = conf["usr_msg"]

        if not conf or "prompt" not in conf:
            raise ValueError("Не найден промпт в конфигурации DAG.")
        prompt = conf["prompt"]

        history = conf.get("history", [])
        if not history:
            history = [
                {"role": "system", "content": prompt},
                {"role": "user", "content": msg},
            ]

        belgium_proxies = {
            "http": "http://**************:8888",
            "https": "http://**************:8888"
        }
        germany_proxies = {
            "http": "http://**************:8888",
            "https": "http://**************:8888"
        }

        headers = {
            "Authorization": f"Bearer {API_TOKEN_GPT}",
            "Content-Type": "application/json"
        }

        data = {
            "model": "gpt-4o",
            "temperature": 0,
            "messages": history
        }

        url = "https://api.openai.com/v1/chat/completions"
        max_retries = 2
        response = None
        for attempt in range(max_retries):
            try:
                response = requests.post(url, json=data, headers=headers, proxies=germany_proxies, timeout=90)
                response.raise_for_status()
                break
            except requests.exceptions.Timeout:
                if attempt == max_retries - 1:
                    raise
        if response is None:
            raise Exception("No response received after retries")
        # response = requests.post(url, json=data, headers=headers)

        content = response.json()["choices"][0]["message"]["content"]

        # Мониторинг затрат OpenAI
        try:
           
            response_data = response.json()

            cost_analysis = track_openai_cost(
                response_data=response_data,
                context={
                    "dag_id": "chatgpt_message_recommendation_pipeline",
                    "task_id": "generate_openai_response"
                }
            )

            print(f"💰 Стоимость операции: ${cost_analysis.get('total_cost', 0):.6f}")
            print(f"🔢 Токены: {cost_analysis.get('total_tokens', 0)}")

        except Exception as e:
            print(f"⚠️ Ошибка отслеживания затрат: {e}")
        

        return content.strip('"')

    generate_openai_response(provide_context=True)

dag_chatgpt = chatgpt()