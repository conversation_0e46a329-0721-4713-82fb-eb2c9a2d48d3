[2025-07-17T21:34:29.506+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:34:29.507+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:34:29.509+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.509+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:34:29.524+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:34:29.538+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.039 seconds
[2025-07-17T21:35:00.576+0000] {processor.py:186} INFO - Started process (PID=354) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:00.577+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:35:00.580+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:00.592+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:00.608+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.039 seconds
[2025-07-17T21:35:31.386+0000] {processor.py:186} INFO - Started process (PID=490) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:31.387+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:35:31.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:31.398+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:31.411+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.031 seconds
[2025-07-17T21:36:01.716+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:01.717+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:36:01.720+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.719+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:01.727+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:01.743+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:36:32.006+0000] {processor.py:186} INFO - Started process (PID=762) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:32.007+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:36:32.010+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:32.020+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:32.033+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:37:02.435+0000] {processor.py:186} INFO - Started process (PID=898) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:02.435+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:37:02.438+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.437+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:02.446+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:02.459+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.030 seconds
[2025-07-17T21:37:33.076+0000] {processor.py:186} INFO - Started process (PID=1034) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:33.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:37:33.080+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:33.089+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:33.101+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.030 seconds
[2025-07-17T21:38:03.573+0000] {processor.py:186} INFO - Started process (PID=1170) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:03.574+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:38:03.576+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.576+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:03.586+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:03.600+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.033 seconds
[2025-07-17T21:38:34.104+0000] {processor.py:186} INFO - Started process (PID=1306) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:34.105+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:38:34.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.106+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:34.116+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:34.130+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:39:04.977+0000] {processor.py:186} INFO - Started process (PID=1445) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:04.978+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:39:04.981+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.980+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:04.991+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:05.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.034 seconds
[2025-07-17T21:39:35.549+0000] {processor.py:186} INFO - Started process (PID=1581) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:35.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:39:35.552+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:35.561+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:35.574+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:40:06.363+0000] {processor.py:186} INFO - Started process (PID=1714) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:40:06.364+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:40:06.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:40:06.379+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:40:06.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.038 seconds
[2025-07-17T21:40:37.146+0000] {processor.py:186} INFO - Started process (PID=1850) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:40:37.147+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:40:37.149+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:37.149+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:40:37.161+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:40:37.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.036 seconds
[2025-07-17T21:42:56.294+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:42:56.295+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:42:56.298+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:42:56.298+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:42:56.310+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:42:56.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.039 seconds
[2025-07-17T21:43:27.161+0000] {processor.py:186} INFO - Started process (PID=357) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:43:27.161+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:43:27.164+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:27.163+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:43:27.175+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:43:27.189+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.035 seconds
[2025-07-17T21:43:58.502+0000] {processor.py:186} INFO - Started process (PID=495) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:43:58.503+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:43:58.507+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:43:58.506+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:43:58.516+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:43:58.531+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.035 seconds
[2025-07-17T21:44:29.009+0000] {processor.py:186} INFO - Started process (PID=637) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:44:29.010+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:44:29.012+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:29.012+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:44:29.022+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:44:29.036+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.033 seconds
[2025-07-17T21:44:59.431+0000] {processor.py:186} INFO - Started process (PID=771) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:44:59.432+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:44:59.434+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:44:59.434+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:44:59.444+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:44:59.459+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.034 seconds
[2025-07-17T21:55:24.252+0000] {processor.py:186} INFO - Started process (PID=222) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:55:24.253+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:55:24.256+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:24.255+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:55:24.267+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:55:24.278+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:55:54.992+0000] {processor.py:186} INFO - Started process (PID=358) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:55:54.993+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:55:54.995+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:55:54.995+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:55:55.006+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:55:55.018+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:56:25.440+0000] {processor.py:186} INFO - Started process (PID=491) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:56:25.441+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:56:25.443+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:25.442+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:56:25.452+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:56:25.467+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:56:55.747+0000] {processor.py:186} INFO - Started process (PID=628) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:56:55.748+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:56:55.750+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:56:55.750+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:56:55.759+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:56:55.772+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.031 seconds
[2025-07-17T21:57:26.173+0000] {processor.py:186} INFO - Started process (PID=764) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:57:26.174+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:57:26.176+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:26.176+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:57:26.184+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:57:26.196+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.029 seconds
[2025-07-17T21:57:56.598+0000] {processor.py:186} INFO - Started process (PID=900) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:57:56.599+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:57:56.602+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:57:56.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:57:56.611+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:57:56.623+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.031 seconds
[2025-07-17T21:58:27.071+0000] {processor.py:186} INFO - Started process (PID=1042) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:58:27.073+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:58:27.075+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:58:27.075+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:58:27.087+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:58:27.100+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.035 seconds
[2025-07-17T22:00:35.812+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:00:35.813+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:00:35.817+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:00:35.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:00:35.844+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:00:35.864+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.066 seconds
[2025-07-17T22:01:06.221+0000] {processor.py:186} INFO - Started process (PID=352) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:01:06.222+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:01:06.224+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:06.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:01:06.236+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:01:06.252+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.037 seconds
[2025-07-17T22:01:36.405+0000] {processor.py:186} INFO - Started process (PID=483) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:01:36.406+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:01:36.408+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:01:36.408+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:01:36.417+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:01:36.430+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.030 seconds
[2025-07-17T22:02:07.385+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:02:07.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:02:07.388+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:07.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:02:07.396+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:02:07.408+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.028 seconds
[2025-07-17T22:02:37.799+0000] {processor.py:186} INFO - Started process (PID=762) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:02:37.800+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:02:37.802+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:02:37.802+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:02:37.810+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:02:37.823+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.029 seconds
[2025-07-17T22:03:08.126+0000] {processor.py:186} INFO - Started process (PID=898) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:03:08.127+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:03:08.129+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:08.129+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:03:08.137+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:03:08.149+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.029 seconds
[2025-07-17T22:03:38.601+0000] {processor.py:186} INFO - Started process (PID=1034) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:03:38.602+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:03:38.606+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:03:38.605+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:03:38.617+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:03:38.631+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.037 seconds
[2025-07-17T22:04:09.700+0000] {processor.py:186} INFO - Started process (PID=1173) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:04:09.701+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:04:09.705+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:09.704+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:04:09.722+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:04:09.740+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.047 seconds
[2025-07-17T22:04:40.031+0000] {processor.py:186} INFO - Started process (PID=1306) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:04:40.032+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:04:40.035+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:04:40.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:04:40.043+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:04:40.055+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.029 seconds
[2025-07-17T22:05:10.456+0000] {processor.py:186} INFO - Started process (PID=1442) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:05:10.458+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:05:10.460+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:10.459+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:05:10.471+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:05:10.486+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.036 seconds
[2025-07-17T22:05:40.804+0000] {processor.py:186} INFO - Started process (PID=1576) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:05:40.805+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:05:40.807+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:05:40.807+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:05:40.818+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:05:40.830+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.031 seconds
[2025-07-17T22:06:11.195+0000] {processor.py:186} INFO - Started process (PID=1709) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:06:11.196+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:06:11.198+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:06:11.198+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:06:11.207+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:06:11.219+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.030 seconds
[2025-07-17T22:07:27.335+0000] {processor.py:186} INFO - Started process (PID=215) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:07:27.337+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:07:27.340+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:27.339+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:07:27.354+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:07:27.371+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.044 seconds
[2025-07-17T22:07:58.896+0000] {processor.py:186} INFO - Started process (PID=357) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:07:58.896+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:07:58.898+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:07:58.898+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:07:58.908+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:07:58.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.030 seconds
[2025-07-17T22:08:29.519+0000] {processor.py:186} INFO - Started process (PID=490) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:08:29.520+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:08:29.524+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:29.523+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:08:29.537+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:08:29.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.044 seconds
[2025-07-17T22:08:59.967+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:08:59.969+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:08:59.971+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:08:59.971+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:08:59.979+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:08:59.991+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.030 seconds
[2025-07-17T22:09:30.890+0000] {processor.py:186} INFO - Started process (PID=765) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:09:30.890+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:09:30.893+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:09:30.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:09:30.901+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:09:30.914+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.031 seconds
[2025-07-17T22:10:01.196+0000] {processor.py:186} INFO - Started process (PID=896) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:10:01.197+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:10:01.199+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:01.198+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:10:01.208+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:10:01.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.033 seconds
[2025-07-17T22:10:31.757+0000] {processor.py:186} INFO - Started process (PID=1034) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:10:31.757+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:10:31.759+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:10:31.759+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:10:31.767+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:10:31.780+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.029 seconds
[2025-07-17T22:11:02.694+0000] {processor.py:186} INFO - Started process (PID=1175) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:11:02.695+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:11:02.698+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:02.698+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:11:02.707+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:11:02.721+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.033 seconds
[2025-07-17T22:11:33.701+0000] {processor.py:186} INFO - Started process (PID=1309) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:11:33.702+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T22:11:33.704+0000] {logging_mixin.py:190} INFO - [2025-07-17T22:11:33.704+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:11:33.713+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T22:11:33.726+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.031 seconds
