[2025-07-17T21:34:29.506+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:34:29.507+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:34:29.509+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:34:29.509+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:34:29.524+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:34:29.538+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.039 seconds
[2025-07-17T21:35:00.576+0000] {processor.py:186} INFO - Started process (PID=354) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:00.577+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:35:00.580+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:00.580+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:00.592+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:00.608+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.039 seconds
[2025-07-17T21:35:31.386+0000] {processor.py:186} INFO - Started process (PID=490) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:31.387+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:35:31.389+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:35:31.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:31.398+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:35:31.411+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.031 seconds
[2025-07-17T21:36:01.716+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:01.717+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:36:01.720+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:01.719+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:01.727+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:01.743+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:36:32.006+0000] {processor.py:186} INFO - Started process (PID=762) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:32.007+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:36:32.010+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:36:32.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:32.020+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:36:32.033+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:37:02.435+0000] {processor.py:186} INFO - Started process (PID=898) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:02.435+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:37:02.438+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:02.437+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:02.446+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:02.459+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.030 seconds
[2025-07-17T21:37:33.076+0000] {processor.py:186} INFO - Started process (PID=1034) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:33.077+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:37:33.080+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:37:33.079+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:33.089+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:37:33.101+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.030 seconds
[2025-07-17T21:38:03.573+0000] {processor.py:186} INFO - Started process (PID=1170) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:03.574+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:38:03.576+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:03.576+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:03.586+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:03.600+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.033 seconds
[2025-07-17T21:38:34.104+0000] {processor.py:186} INFO - Started process (PID=1306) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:34.105+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:38:34.107+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:38:34.106+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:34.116+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:38:34.130+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:39:04.977+0000] {processor.py:186} INFO - Started process (PID=1445) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:04.978+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:39:04.981+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:04.980+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:04.991+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:05.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.034 seconds
[2025-07-17T21:39:35.549+0000] {processor.py:186} INFO - Started process (PID=1581) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:35.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:39:35.552+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:39:35.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:35.561+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:39:35.574+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.032 seconds
[2025-07-17T21:40:06.363+0000] {processor.py:186} INFO - Started process (PID=1714) to work on /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:40:06.364+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/enable_cost_tracking.py for tasks to queue
[2025-07-17T21:40:06.368+0000] {logging_mixin.py:190} INFO - [2025-07-17T21:40:06.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:40:06.379+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/enable_cost_tracking.py
[2025-07-17T21:40:06.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/enable_cost_tracking.py took 0.038 seconds
