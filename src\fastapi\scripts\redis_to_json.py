import asyncio
import base64
import j<PERSON>
from typing import Any, Union

import redis.asyncio as aioredis

try:
    import chardet  # optional, for encoding detection
except ImportError:
    chardet = None

# ---------- helpers ----------
def _safe_bytes_to_str(b: Union[bytes, bytearray]) -> str:
    """
    Convert raw bytes to str trying UTF‑8, chardet, cp1251, latin‑1.
    If all fail, return a base64‑encoded representation prefixed with 'base64:'.
    """
    # 1) Fast UTF‑8 path
    try:
        return b.decode("utf-8")
    except UnicodeDecodeError:
        pass

    # 2) chardet guess
    if chardet is not None:
        enc = (chardet.detect(b) or {}).get("encoding")
        if enc:
            try:
                return b.decode(enc)
            except UnicodeDecodeError:
                pass

    # 3) common legacy encodings
    for enc in ("cp1251", "latin-1"):
        try:
            return b.decode(enc)
        except UnicodeDecodeError:
            continue

    # 4) give up – keep content losslessly
    return "base64:" + base64.b64encode(b).decode("ascii")


def decode_value(obj: Any):
    """
    Recursively normalise Redis values:
    * bytes → str via _safe_bytes_to_str
    * str containing '\\uXXXX' → unicode_escape decoded
    * containers processed recursively
    """
    if isinstance(obj, (bytes, bytearray)):
        return _safe_bytes_to_str(obj)
    if isinstance(obj, str):
        if "\\u" in obj:
            try:
                return obj.encode("utf-8").decode("unicode_escape")
            except Exception:
                return obj
        return obj
    if isinstance(obj, dict):
        return {decode_value(k): decode_value(v) for k, v in obj.items()}
    if isinstance(obj, list):
        return [decode_value(i) for i in obj]
    if isinstance(obj, tuple):
        return tuple(decode_value(i) for i in obj)
    if isinstance(obj, set):
        # Convert to list to keep JSON-serialisable and preserve all elements
        return [decode_value(i) for i in obj]
    return obj

def _json_default(o):
    """
    Fallback encoder for json.dumps: convert any residual bytes/bytearray
    that slipped through decode_value into safe string form.
    """
    if isinstance(o, (bytes, bytearray)):
        return _safe_bytes_to_str(o)
    raise TypeError(f"Object of type {o.__class__.__name__} is not JSON serialisable")
# --------------------------------------


async def backup_data(user_id: str, backup_filename: str):
    # Подключаемся к Redis (на локальном хосте)
    r = await aioredis.from_url(
        "redis://redis:6379",
        decode_responses=False  # work with raw bytes; we'll decode manually
    )

    data = {}
    # Определяем паттерны для поиска ключей по user_id
    key_patterns = [f"*:{user_id}:*", f"*:{user_id}"]
    all_keys = set()
    for pattern in key_patterns:
        keys = await r.keys(pattern)
        all_keys.update(keys)

    print(f"Найдено ключей для user_id {user_id}: {len(all_keys)}")

    for raw_key in all_keys:
        key_type = await r.type(raw_key)

        if key_type == "string":
            raw_val = await r.get(raw_key)
        elif key_type == "hash":
            raw_val = await r.hgetall(raw_key)
        elif key_type == "list":
            raw_val = await r.lrange(raw_key, 0, -1)
        elif key_type == "set":
            raw_val = await r.smembers(raw_key)
        elif key_type == "zset":
            raw_val = await r.zrange(raw_key, 0, -1, withscores=True)
        else:
            raw_val = None

        value = decode_value(raw_val)
        key = decode_value(raw_key)
        data[key] = {"type": key_type, "value": value}

    # Сохраняем данные в JSON-файл
    with open(backup_filename, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2, default=_json_default)

    print(f"Резервная копия сохранена в файл: {backup_filename}")


async def restore_data(backup_filename: str):
    # Подключаемся к Redis (здесь используется другой URL, при необходимости измените его)
    r = await aioredis.from_url(
        "redis://redis:6379",
        decode_responses=False  # work with raw bytes; we'll decode manually
    )

    # Загружаем данные из файла
    with open(backup_filename, "r", encoding="utf-8") as f:
        data = json.load(f)

    # Восстанавливаем данные для каждого ключа
    for key, entry in data.items():
        key_type = entry["type"]
        value = entry["value"]
        if key_type == "string":
            await r.set(key, value)
        elif key_type == "hash":
            await r.hset(key, mapping=value)
        elif key_type == "list":
            await r.delete(key)
            for item in value:
                await r.rpush(key, item)
        elif key_type == "set":
            await r.delete(key)
            for item in value:
                await r.sadd(key, item)
        elif key_type == "zset":
            await r.delete(key)
            for member, score in value:
                await r.zadd(key, {member: score})
        else:
            print(f"Неизвестный тип для ключа {key}, пропуск.")

    print("Восстановление данных завершено.")


async def main():
    mode = input("Введите режим (backup/restore): ").strip().lower()
    if mode == "backup":
        user_id = input("Введите user_id: ").strip()
        backup_filename = f"backup_{user_id}.json"
        await backup_data(user_id, backup_filename)
    elif mode == "restore":
        backup_filename = input("Введите имя файла с резервной копией: ").strip()
        await restore_data(backup_filename)
    else:
        print("Неверный режим. Введите 'backup' или 'restore'.")


if __name__ == "__main__":
    asyncio.run(main())