import re
import sys
import glob
import pandas as pd
from statistics import mode

# шаблон для строк с окончанием этапа
END_RE = re.compile(
    r'^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d+) - '
    r'endpoint=\[(?P<endpoint>.*?)\] '
    r'stage=\[(?P<stage>.*?)\] - END - duration=\[(?P<duration>[\d\.]+)s\]'
)

# минимальная допустимая длительность
MIN_DURATION = 0.01

def parse_files(file_patterns):
    records = []
    for pattern in file_patterns:
        for fname in glob.glob(pattern):
            with open(fname, encoding='utf-8') as f:
                for line in f:
                    m = END_RE.match(line)
                    if m:
                        rec = m.groupdict()
                        rec['duration'] = float(rec['duration'])
                        # пропускаем слишком короткие записи
                        if rec['duration'] < MIN_DURATION:
                            continue
                        rec['file'] = fname
                        records.append(rec)
    return pd.DataFrame(records)

def compute_stats(df):
    stats = df.groupby('stage')['duration'].agg([
        ('count', 'count'),
        ('min', 'min'),
        ('max', 'max'),
        ('mean', 'mean'),
        ('median', 'median'),
        ('std', 'std'),
    ]).reset_index()
    # вычисляем моду (берём первую при нескольких)
    modes = (
        df.groupby('stage')['duration']
          .apply(lambda x: mode(x))
          .reset_index(name='mode')
    )
    return stats.merge(modes, on='stage')

def main():
    if len(sys.argv) < 2:
        print("Usage: python parse_logs.py <log_file_pattern1> [<pattern2> ...]")
        sys.exit(1)

    # парсим и автоматически фильтруем по MIN_DURATION
    df = parse_files(sys.argv[1:])
    if df.empty:
        print("Не найдено записей END с длительностью >= {0:.4f}s.".format(MIN_DURATION))
        sys.exit(1)

    # сохраняем «сырые» данные
    df.to_csv('parsed_log.csv', index=False)
    print("Сохранён parsed_log.csv — детальный разбив по этапам.")

    # считаем и сохраняем статистику
    stats = compute_stats(df)
    stats.to_csv('stage_stats.csv', index=False)
    print("Сохранён stage_stats.csv — статистика по каждому этапу.")

if __name__ == '__main__':
    main()
