"""
Простой скрипт для включения мониторинга затрат в ключевых DAG файлах
Раскомментирует существующие функции analyze_token_usage и добавляет сохранение в Redis
"""

import os
import re

# Список ключевых DAG файлов для обновления
KEY_DAG_FILES = [
    "chatgpt_message_pipeline.py",
    "chatgpt_chat_open_pipeline.py", 
    "chatgpt_message_recommendation_pipeline.py",
    "chatgpt_message_risk_pipeline.py",
    "chatgpt_analyze_food_pipeline.py",
    "chatgpt_image_pipeline.py",
    "chatgpt_generate_challenges_pipeline.py"
]

def enable_cost_tracking_in_file(file_path):
    """
    Включает мониторинг затрат в одном DAG файле
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Проверяем, не включен ли уже мониторинг
        if "💰 Total cost:" in content:
            print(f"✅ {os.path.basename(file_path)} - мониторинг уже включен")
            return True
        
        # Раскомментируем строки с analyze_token_usage
        content = content.replace("# stats = analyze_token_usage(prompt, content)", "stats = analyze_token_usage(prompt, content)")
        content = content.replace("# print(stats)", "print(stats)")
        
        # Добавляем сохранение в Redis в функцию analyze_token_usage
        redis_code = '''
                # Сохраняем в Redis для мониторинга
                try:
                    import redis
                    import json
                    from datetime import datetime
                    
                    r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                    cost_data = {
                        "dag_id": "''' + os.path.basename(file_path).replace('.py', '') + '''",
                        "tokens_input": tokens_input,
                        "tokens_output": tokens_output,
                        "cost_input": cost_input,
                        "cost_output": cost_output,
                        "total_cost": cost_input + cost_output,
                        "timestamp": datetime.now().isoformat(),
                        "model": model
                    }
                    
                    # Сохраняем с TTL 30 дней
                    key = f"cost_tracking:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    r.setex(key, 2592000, json.dumps(cost_data))
                    
                except Exception as redis_error:
                    print(f"Redis сохранение не удалось: {redis_error}")'''
        
        # Находим место для вставки кода Redis (после расчета стоимости)
        pattern = r'(cost_output = \(tokens_output / tokens_unit\) \* cost_rate_output)'
        replacement = r'\1' + redis_code
        content = re.sub(pattern, replacement, content)
        
        # Добавляем вывод общей стоимости
        pattern = r'(print\(f"Cost for input tokens: \$\{cost_input:.4f\}, Cost for output tokens: \$\{cost_output:.4f\}"\))'
        replacement = r'\1\n            print(f"💰 Total cost: ${cost_input + cost_output:.6f}")'
        content = re.sub(pattern, replacement, content)
        
        # Сохраняем обновленный файл
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ {os.path.basename(file_path)} - мониторинг включен")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка в {os.path.basename(file_path)}: {e}")
        return False

def main():
    """
    Включает мониторинг затрат в ключевых DAG файлах
    """
    print("🚀 ВКЛЮЧЕНИЕ МОНИТОРИНГА ЗАТРАТ В КЛЮЧЕВЫХ DAG ФАЙЛАХ")
    print("=" * 60)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    success_count = 0
    
    for filename in KEY_DAG_FILES:
        file_path = os.path.join(current_dir, filename)
        
        if os.path.exists(file_path):
            if enable_cost_tracking_in_file(file_path):
                success_count += 1
        else:
            print(f"⚠️ {filename} не найден")
    
    print(f"\n📊 РЕЗУЛЬТАТ: {success_count}/{len(KEY_DAG_FILES)} файлов обновлено")
    
    if success_count > 0:
        print("\n🎉 МОНИТОРИНГ ЗАТРАТ ВКЛЮЧЕН!")
        print("\nЧто дальше:")
        print("1. Перезапустите Airflow")
        print("2. Запустите любой DAG для тестирования")
        print("3. Проверьте затраты: GET /cost-stats")
        print("4. Данные сохраняются в Redis автоматически")
    else:
        print("\n⚠️ Не удалось обновить файлы")

if __name__ == "__main__":
    main()
