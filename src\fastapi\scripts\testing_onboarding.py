import asyncio
import json
import redis
r = redis.from_url("redis://redis:6379", decode_responses=True)
import httpx
client = httpx.AsyncClient(timeout=10.0)
from typing import Optional
from enum import Enum
class EntityType(str, Enum):
    general = "general"
    recommendation = "recommendation"
    risk = "risk"
metrics = json.loads("""{
               "personal_information": {
               "name": "",
               "name_field_name": "Имя",
               "name_weight": "null",
               "date_of_birth": "",
               "date_of_birth_field_name": "Дата рождения",
               "date_of_birth_weight": "null",
               "gender": "",
               "gender_field_name": "Пол",
               "gender_weight": "null",
               "height": "",
               "height_field_name": "Рост",
               "height_weight": "null",
               "weight": "",
               "weight_field_name": "Вес",
               "weight_weight": "null"
               },
               "lifestyle": {
               "activity_level": "",
               "activity_level_field_name": "Уровень активности",
               "activity_level_weight": "null",
               "sports": "",
               "sports_field_name": "Занятия спортом",
               "sports_weight": "null",
               "smoking": "",
               "smoking_field_name": "Курение",
               "smoking_weight": "null",
               "alcohol": "",
               "alcohol_field_name": "Алкоголь",
               "alcohol_weight": "null",
               "sleep_recovery": "",
               "sleep_recovery_field_name": "Восстановление после сна",
               "sleep_recovery_weight": "null",
               "sleep_schedule": "",
               "sleep_schedule_field_name": "Режим сна",
               "sleep_schedule_weight": "null",
               "sleep_quality": "",
               "sleep_quality_field_name": "Качество сна",
               "sleep_quality_weight": "null",
               "work_schedule": "",
               "work_schedule_field_name": "Режим работы",
               "work_schedule_weight": "null",
               "stress_level": "",
               "stress_level_field_name": "Уровень стресса",
               "stress_level_weight": "null"
               },
               "health_status": {
               "blood_pressure": "",
               "blood_pressure_field_name": "Давление",
               "blood_pressure_weight": "null",
               "chronic_conditions": [],
               "chronic_conditions_field_name": "Хронические заболевания",
               "chronic_conditions_weight": "null",
               "injuries": "",
               "injuries_field_name": "Травмы",
               "injuries_weight": "null",
               "genetic_conditions": [],
               "genetic_conditions_field_name": "Генетические заболевания",
               "genetic_conditions_weight": "null",
               "regular_medicine": [],
               "regular_medicine_field_name": "Лекарства",
               "regular_medicine_weight": "null",
               "allergies": [],
               "allergies_field_name": "Аллергии",
               "allergies_weight": "null"
               },
               "nutrition": {
               "food_level": "",
               "food_level_field_name": "Уровень питания",
               "food_level_weight": "null",
               "diet_schedule": "",
               "diet_schedule_field_name": "Режим питания",
               "diet_schedule_weight": "null",
               "preferred_dishes": [],
               "preferred_dishes_field_name": "Предпочитаемые блюда",
               "preferred_dishes_weight": "null",
               "diet_balance": "",
               "diet_balance_field_name": "Баланс рациона",
               "diet_balance_weight": "null",
               "food_intolerances": [],
               "food_intolerances_field_name": "Пищевая непереносимость",
               "food_intolerances_weight": "null",
               "calorie_intake": "",
               "calorie_intake_field_name": "Калорийность",
               "calorie_intake_weight": "null",
               "dietary_supplement": "",
               "dietary_supplement_field_name": "Пищевые добавки",
               "dietary_supplement_weight": "null"
               },
               "analysis": {
               "blood_analysis": "",
               "blood_analysis_field_name": "Анализ крови",
               "blood_analysis_weight": "null",
               "biochemical_analysis": "",
               "biochemical_analysis_field_name": "Биохимический анализ",
               "biochemical_analysis_weight": "null",
               "urine_analysis": "",
               "urine_analysis_field_name": "Анализ мочи",
               "urine_analysis_weight": "null",
               "lipid_profile": "",
               "lipid_profile_field_name": "Липидный профиль",
               "lipid_profile_weight": "null",
               "glucose_tolerance_test": "",
               "glucose_tolerance_test_field_name": "Глюкозотолерантный тест",
               "glucose_tolerance_test_weight": "null",
               "thyroid_test": "",
               "thyroid_test_field_name": "Тиреоидный тест",
               "thyroid_test_weight": "null",
               "glycated_hemoglobin": "",
               "glycated_hemoglobin_field_name": "Гликированный гемоглобин",
               "glycated_hemoglobin_weight": "null",
               "coagulogram": "",
               "coagulogram_field_name": "Коагулограмма",
               "coagulogram_weight": "null",
               "inflammatory_markers": "",
               "inflammatory_markers_field_name": "Маркеры воспаления",
               "inflammatory_markers_weight": "null" }
               }""")
# Глобальный клиент
token="501318b45eb38e350e6036b929ca1958bd058d2c3cc8d1c833ba2f9d0438abbf033b9d023edc9470a6bab732e8e7a72ff0d8ccf7ae93ee56af0855d657d0ab7bbe5d8ff78e85b6e0744c3a47ddf16ebff9617b967dd007f21724cdaba73aa29e8c7d8104c755915ab0a8437bfa5837dfdd15c295ae8f5ecc41d1d21e87bc8596"
API_BASE_URL = "https://root.biome-dev-api.work/api"

async def update_health_profile(user_id, bearer_token, metrics):
    url = f"{API_BASE_URL}/users/{user_id}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    data = {"healthProfile": metrics}

    resp = await client.put(url, headers=headers, json=data)
    return resp

async def delete_user_recommendations_from_db(bearer_token, user_id):
    url = f"{API_BASE_URL}/recommendations?filters[user][id][$eq]={user_id}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    try:
        response = await client.get(url, headers=headers)
        if response.status_code == 200:
            recommendations = response.json()["data"]
            for recommendation in recommendations:
                rec_id = recommendation["documentId"]
                delete_url = f"{API_BASE_URL}/recommendations/{rec_id}"
                delete_response = await client.delete(delete_url, headers=headers)
                if delete_response.status_code != 204:
                    print(f"Ошибка удаления рекомендации {rec_id}: {delete_response.text}")
                else:
                    print('Удалено')
        else:
            print(f"Failed to get recommendations for user {user_id}: {response.status_code}")
    except Exception as e:
        print(f"Ошибка при удалении рекомендаций: {e}")

async def delete_user_risks_from_db(bearer_token, user_id):
    url = f"{API_BASE_URL}/risks?filters[user][id][$eq]={user_id}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    try:
        response = await client.get(url, headers=headers)
        if response.status_code == 200:
            risks = response.json()["data"]
            for risk in risks:
                risk_id = risk["documentId"]
                delete_url = f"{API_BASE_URL}/risks/{risk_id}"
                delete_response = await client.delete(delete_url, headers=headers)
                if delete_response.status_code != 204:
                    print(f"Ошибка удаления риска {risk_id}: {delete_response.text}")
                else:
                    print('Удалено')
        else:
            print(f"Failed to get risks for user {user_id}: {response.status_code}")
    except Exception as e:
        print(f"Ошибка при удалении рисков: {e}")

async def update_goals_profile(user_id, bearer_token, goals):
    url = f"{API_BASE_URL}/users/{user_id}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    data = {"goals": goals}

    resp = await client.put(url, headers=headers, json=data)
    return resp

async def delete_existing_chats_by_document_id(user_id: int, bearer_token: str, entity_type: Optional[str] = None, entity_id: Optional[int] = None):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }

    # Формируем параметры для фильтрации существующих чатов
    params = {
        "filters[users_permissions_user][id][$eq]": user_id
    }
    if entity_type == "risk" and entity_id is not None:
        params["filters[risk][id][$eq]"] = entity_id
    elif entity_type == "recommendation" and entity_id is not None:
        params["filters[recommendation][id][$eq]"] = entity_id
    elif entity_type == "general":
        pass  # Для общего чата дополнительных фильтров не требуется

    try:
        # Получаем существующие чаты
        fetch_response = await client.get(f"{API_BASE_URL}/chats", headers=headers, params=params)
        if fetch_response.status_code == 200:
            existing_chats = fetch_response.json().get("data", [])
            for chat in existing_chats:
                document_id = chat.get("documentId")  # Получаем documentId
                if not document_id:
                    print("Document ID отсутствует в данных чата. Пропускаем.")
                    continue

                delete_url = f"{API_BASE_URL}/chats/{document_id}"
                delete_response = await client.delete(delete_url, headers=headers)
                if delete_response.status_code == 200:  # Успешное удаление
                    print(f"Чат с documentId {document_id} успешно удалён.")
                else:
                    print(f"Ошибка при удалении чата с documentId {document_id}: {delete_response.status_code}, {delete_response.text}")
        else:
            print(f"Не удалось получить существующие чаты: {fetch_response.status_code}, {fetch_response.text}")
    except Exception as e:
        print(f"Ошибка при удалении существующих чатов: {e}")

async def upload_risk(user_id, risk_data, bearer_token):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    url = f"{API_BASE_URL}/risks"
    payload = {
        "data": {
            "title": risk_data["title"],
            "shortDescription": risk_data["shortDescription"],
            "content": risk_data["content"],
            "user": user_id,
            "assessment": risk_data["assessment"],
        }
    }
    response = await client.post(url, headers=headers, json=payload)
    if response.status_code == 201:
        print(f"Risk uploaded successfully: {response.json()}")
    else:
        print(f"Failed to upload risk: {response.status_code}, {response.text}")

user_id = 0
# delete_existing_chats_by_document_id(user_id, token)
# update_goals_profile(user_id, token, None)
# update_health_profile(user_id, token, metrics)
# delete_user_risks_from_db(token, user_id)
# delete_user_recommendations_from_db(token, user_id)
# name = metrics['personal_information']['name']
# print(name)

risks = json.loads("""{
                   "risk 0": { "title": "Сердечно-сосудистые заболевания", "shortDescription": "Болезни сердца и сосудов, повышающие риск инфаркта, инсульта и гипертонии.", "content": "", "assessment": 0.0},
                   "risk 1": { "title": "Диабет", "shortDescription": "Хроническое нарушение сахара в крови, увеличивающее риск слепоты, ампутации и инфаркта.", "content": "", "assessment": 0.0},
                   "risk 2": { "title": "Нейродегенеративные заболевания", "shortDescription": "Патологии нервной системы, приводящие к ухудшению памяти, движения и когнитивных функций.", "content": "", "assessment": 0.0}
                   }""")
async def process_user(user_id, bearer_token, metrics, semaphore):

    async with semaphore:
        await delete_existing_chats_by_document_id(user_id, bearer_token)
        await update_health_profile(user_id, bearer_token, None)
        await update_goals_profile(user_id, bearer_token, None)
        await delete_user_risks_from_db(bearer_token, user_id)
        await delete_user_recommendations_from_db(bearer_token, user_id)

        for _, risk in risks.items():
            await upload_risk(user_id, risk, bearer_token)

# async def main():
#     semaphore = asyncio.Semaphore(10)  # Ограничение до 10 одновременных задач
#     tasks = []
#     for user_id in range(500):
#         print(user_id)
#         tasks.append(process_user(user_id, token, metrics, semaphore))
#     await asyncio.gather(*tasks)
#     await client.aclose()

async def main():
    semaphore = asyncio.Semaphore(10)  # Ограничение до 10 одновременных задач
    tasks = []
    user_id = 70
    print(user_id)
    tasks.append(process_user(user_id, token, metrics, semaphore))
    await asyncio.gather(*tasks)
    await client.aclose()

if __name__ == "__main__":
    asyncio.run(main())
