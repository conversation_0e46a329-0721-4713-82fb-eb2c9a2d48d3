import asyncio
import redis.asyncio as aioredis
import json
import csv
import base64
from collections import defaultdict

# ---------- утилита безопасного декодирования ----------
def _safe_decode(val):
    """
    Преобразует bytes → str.
    - UTF‑8 декодируется напрямую.
    - Если строка содержит не‑UTF‑8 байты, возвращается строка вида 'base64:<...>'.
    """
    if val is None:
        return None
    if isinstance(val, bytes):
        try:
            return val.decode("utf-8")
        except UnicodeDecodeError:
            return "base64:" + base64.b64encode(val).decode("ascii")
    return val


async def backup_data(user_id: str, backup_filename: str):
    """
    Формирует "широкий" CSV: строки = user_id, колонки = фичи (ключи без user_id),
    значения — содержимое ключей (множ. знач. через запятую).
    """
    r = await aioredis.from_url("redis://redis:6379", decode_responses=False)

    # Собираем все ключи согласно паттернам
    if user_id == "*":
        key_patterns = ["*:*:*", "*:*"]
    else:
        key_patterns = [f"*:{user_id}:*", f"*:{user_id}"]

    all_keys: set[str] = set()
    for pattern in key_patterns:
        async for k in r.scan_iter(match=pattern):
            all_keys.add(k.decode() if isinstance(k, bytes) else k)

    print(f"Всего найдено ключей: {len(all_keys)}")

    # Словарь: {user_id: {feature: str_value}}
    table: dict[str, dict[str, str]] = defaultdict(dict)

    # Вспом. функция: объединить значения
    def _merge(old, new):
        if not old:
            return new
        if str(new) in str(old).split(","):
            return old  # не дублируем
        return f"{old},{new}"

    for key in all_keys:
        parts = key.split(":")
        # Определяем uid и feature
        if user_id != "*" and user_id in parts:
            idx = parts.index(user_id)
            uid = user_id
            feature = ":".join(parts[:idx] + parts[idx + 1 :])
        else:
            # ищем первое числовое звено как uid
            idx = next((i for i, p in enumerate(parts) if p.isdigit()), None)
            if idx is None:
                # если не нашли, пропускаем
                continue
            uid = parts[idx]
            feature = ":".join(parts[:idx] + parts[idx + 1 :])

        # Получаем значение
        key_type = await r.type(key)
        if key_type == b"string":
            value = _safe_decode(await r.get(key))
        elif key_type == b"hash":
            raw = await r.hgetall(key)
            value = json.dumps(
                { _safe_decode(k): _safe_decode(v) for k, v in raw.items() },
                ensure_ascii=False,
            )
        elif key_type == b"list":
            raw = await r.lrange(key, 0, -1)
            value = ",".join(_safe_decode(v) for v in raw)
        elif key_type == b"set":
            raw = await r.smembers(key)
            value = ",".join(sorted(_safe_decode(v) for v in raw))
        elif key_type == b"zset":
            raw = await r.zrange(key, 0, -1, withscores=True)
            value = ",".join(
                f"{_safe_decode(member)}:{score}" for member, score in raw
            )
        else:
            value = ""

        # Записываем
        table[uid][feature] = _merge(table[uid].get(feature), value)

    # --- формируем CSV ---
    # Все возможные фичи (отсортированы)
    all_features = sorted({f for feats in table.values() for f in feats})

    with open(backup_filename, "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow(["user_id"] + all_features)

        for uid, feats in sorted(table.items()):
            row = [uid] + [feats.get(feat, "") for feat in all_features]
            writer.writerow(row)

    print(f"“Широкий” CSV сохранён: {backup_filename}")


async def restore_data(backup_filename: str):
    """Восстановить данные из CSV-файла в Redis."""
    r = await aioredis.from_url("redis://redis:6379", decode_responses=False)

    import ast
    # helper to check base64 marker
    def _restore_val(v):
        if isinstance(v, str) and v.startswith("base64:"):
            return base64.b64decode(v[7:])
        return v

    # читаем CSV
    with open(backup_filename, "r", encoding="utf-8") as f:
        reader = csv.reader(f)
        header = next(reader, None)      # пропускаем заголовок
        rows = list(reader)

    # восстанавливаем
    for key, key_type, value_str in rows:
        value = json.loads(value_str)
        if key_type == "string":
            if isinstance(value, str) and value.startswith("base64:"):
                await r.set(key, base64.b64decode(value[7:]))
            else:
                await r.set(key, value)
        elif key_type == "hash":
            # value is dict
            mapping = {k: _restore_val(v) for k, v in value.items()}
            await r.hset(key, mapping=mapping)
        elif key_type == "list":
            await r.delete(key)
            for item in value:
                await r.rpush(key, _restore_val(item))
        elif key_type == "set":
            await r.delete(key)
            for item in value:
                await r.sadd(key, _restore_val(item))
        elif key_type == "zset":
            await r.delete(key)
            for member, score in value:
                await r.zadd(key, {_restore_val(member): score})
        else:
            print(f"Неизвестный тип для ключа {key}, пропуск.")

    print("Восстановление данных завершено.")


async def main():
    mode = input("Введите режим (backup/restore): ").strip().lower()
    if mode == "backup":
        user_id = input("Введите user_id: ").strip()
        backup_filename = f"backup_{user_id}.csv"
        await backup_data(user_id, backup_filename)
    elif mode == "restore":
        backup_filename = input("Введите имя CSV-файла резервной копии: ").strip()
        await restore_data(backup_filename)
    else:
        print("Неверный режим. Введите 'backup' или 'restore'.")


if __name__ == "__main__":
    asyncio.run(main())