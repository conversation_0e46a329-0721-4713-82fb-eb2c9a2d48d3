#!/usr/bin/env python3
"""
Скрипт для добавления тестовых данных мониторинга затрат для всех DAG файлов
Имитирует реальные запросы к OpenAI для проверки системы мониторинга
"""

import redis
import json
import uuid
from datetime import datetime, timedelta
import random

# Подключение к Redis
try:
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    redis_client.ping()
    print("✅ Подключение к Redis успешно")
except Exception as e:
    print(f"❌ Ошибка подключения к Redis: {e}")
    exit(1)

# Список всех DAG файлов с их характеристиками
DAG_CONFIGS = {
    "chatgpt_analyze_food_pipeline": {
        "description": "Анализ еды",
        "avg_input_tokens": 150,
        "avg_output_tokens": 200,
        "operations_per_day": 25
    },
    "chatgpt_calories_achievements_pipeline": {
        "description": "Достижения по калориям",
        "avg_input_tokens": 120,
        "avg_output_tokens": 180,
        "operations_per_day": 15
    },
    "chatgpt_calories_big_trend_pipeline": {
        "description": "Большие тренды калорий",
        "avg_input_tokens": 200,
        "avg_output_tokens": 250,
        "operations_per_day": 8
    },
    "chatgpt_calories_small_trend_pipeline": {
        "description": "Малые тренды калорий",
        "avg_input_tokens": 100,
        "avg_output_tokens": 150,
        "operations_per_day": 12
    },
    "chatgpt_challenge_ask_pipeline": {
        "description": "Вопросы челленджей",
        "avg_input_tokens": 80,
        "avg_output_tokens": 120,
        "operations_per_day": 20
    },
    "chatgpt_challenge_classify_pipeline": {
        "description": "Классификация челленджей",
        "avg_input_tokens": 90,
        "avg_output_tokens": 100,
        "operations_per_day": 18
    },
    "chatgpt_chat_open_pipeline": {
        "description": "Открытый чат",
        "avg_input_tokens": 180,
        "avg_output_tokens": 220,
        "operations_per_day": 35
    },
    "chatgpt_chat_open_rec_risk_pipeline": {
        "description": "Чат с рекомендациями и рисками",
        "avg_input_tokens": 200,
        "avg_output_tokens": 280,
        "operations_per_day": 22
    },
    "chatgpt_clarify_pipeline": {
        "description": "Уточнения",
        "avg_input_tokens": 70,
        "avg_output_tokens": 90,
        "operations_per_day": 30
    },
    "chatgpt_classify_pipeline": {
        "description": "Классификация",
        "avg_input_tokens": 60,
        "avg_output_tokens": 80,
        "operations_per_day": 40
    },
    "chatgpt_describe_challenge_pipeline": {
        "description": "Описание челленджей",
        "avg_input_tokens": 110,
        "avg_output_tokens": 160,
        "operations_per_day": 14
    },
    "chatgpt_generate_challenges_pipeline": {
        "description": "Генерация челленджей",
        "avg_input_tokens": 130,
        "avg_output_tokens": 200,
        "operations_per_day": 10
    },
    "chatgpt_goals_pipeline": {
        "description": "Работа с целями",
        "avg_input_tokens": 140,
        "avg_output_tokens": 190,
        "operations_per_day": 16
    },
    "chatgpt_image_object_pipeline": {
        "description": "Анализ объектов на изображениях",
        "avg_input_tokens": 300,
        "avg_output_tokens": 150,
        "operations_per_day": 12
    },
    "chatgpt_image_pipeline": {
        "description": "Обработка изображений",
        "avg_input_tokens": 280,
        "avg_output_tokens": 140,
        "operations_per_day": 18
    },
    "chatgpt_message_pipeline": {
        "description": "Обычные сообщения",
        "avg_input_tokens": 100,
        "avg_output_tokens": 130,
        "operations_per_day": 50
    },
    "chatgpt_message_recommendation_pipeline": {
        "description": "Сообщения с рекомендациями",
        "avg_input_tokens": 160,
        "avg_output_tokens": 210,
        "operations_per_day": 28
    },
    "chatgpt_message_risk_pipeline": {
        "description": "Сообщения о рисках",
        "avg_input_tokens": 170,
        "avg_output_tokens": 200,
        "operations_per_day": 20
    },
    "chatgpt_metrics_pipeline": {
        "description": "Метрики",
        "avg_input_tokens": 90,
        "avg_output_tokens": 110,
        "operations_per_day": 25
    },
    "chatgpt_pdf_pipeline": {
        "description": "Обработка PDF",
        "avg_input_tokens": 400,
        "avg_output_tokens": 300,
        "operations_per_day": 5
    },
    "chatgpt_recount_calories_pipeline": {
        "description": "Пересчет калорий",
        "avg_input_tokens": 120,
        "avg_output_tokens": 160,
        "operations_per_day": 22
    },
    "chatgpt_unified_question_pipeline": {
        "description": "Унифицированные вопросы",
        "avg_input_tokens": 150,
        "avg_output_tokens": 180,
        "operations_per_day": 32
    },
    "chatgpt_weights_pipeline": {
        "description": "Работа с весом",
        "avg_input_tokens": 110,
        "avg_output_tokens": 140,
        "operations_per_day": 18
    },
    "perplexity_pipeline": {
        "description": "Perplexity запросы",
        "avg_input_tokens": 200,
        "avg_output_tokens": 250,
        "operations_per_day": 15
    }
}

def generate_test_data_for_dag(dag_id, config, days_back=7):
    """Генерирует тестовые данные для одного DAG"""

    operations = []
    total_cost = 0

    # Генерируем данные за последние дни
    for day in range(days_back):
        date = datetime.now() - timedelta(days=day)

        # Количество операций в день с небольшой случайностью
        daily_ops = max(1, int(config["operations_per_day"] * random.uniform(0.7, 1.3)))

        for op in range(daily_ops):
            # Генерируем случайные токены около средних значений
            input_tokens = max(10, int(config["avg_input_tokens"] * random.uniform(0.5, 1.5)))
            output_tokens = max(5, int(config["avg_output_tokens"] * random.uniform(0.5, 1.5)))
            total_tokens = input_tokens + output_tokens

            # Рассчитываем стоимость (цены для gpt-4o-mini)
            input_cost = input_tokens * 0.00015 / 1000
            output_cost = output_tokens * 0.0006 / 1000
            operation_cost = input_cost + output_cost
            total_cost += operation_cost

            # Создаем данные операции
            operation_data = {
                "dag_id": dag_id,
                "task_id": "generate_openai_response",
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": total_tokens,
                "input_cost": round(input_cost, 8),
                "output_cost": round(output_cost, 8),
                "total_cost": round(operation_cost, 8),
                "user_id": random.choice([None, 1001, 1002, 1003, 1004, 1005])
            }

            operations.append(operation_data)

    return operations, total_cost

def save_test_data_using_cost_tracking(operations):
    """Сохраняет тестовые данные используя функцию save_cost_data"""

    saved_count = 0

    for operation in operations:
        try:
            # Используем существующую функцию save_cost_data
            save_cost_data(
                dag_id=operation["dag_id"],
                task_id=operation["task_id"],
                input_tokens=operation["input_tokens"],
                output_tokens=operation["output_tokens"],
                input_cost=operation["input_cost"],
                output_cost=operation["output_cost"],
                total_cost=operation["total_cost"],
                user_id=operation["user_id"]
            )
            saved_count += 1
        except Exception as e:
            print(f"❌ Ошибка сохранения операции для {operation['dag_id']}: {e}")

    return saved_count

def main():
    """Основная функция"""
    
    print("🧪 ГЕНЕРАЦИЯ ТЕСТОВЫХ ДАННЫХ ДЛЯ ВСЕХ DAG ФАЙЛОВ")
    print("=" * 60)
    
    total_operations = 0
    total_cost = 0
    dag_stats = {}
    
    # Генерируем данные для каждого DAG
    for dag_id, config in DAG_CONFIGS.items():
        print(f"🔧 Генерирую данные для {dag_id}...")
        print(f"   📝 {config['description']}")
        
        operations, dag_cost = generate_test_data_for_dag(dag_id, config)
        saved_count = save_test_data_to_redis(operations)
        
        dag_stats[dag_id] = {
            "operations": len(operations),
            "saved": saved_count,
            "cost": dag_cost,
            "description": config['description']
        }
        
        total_operations += len(operations)
        total_cost += dag_cost
        
        print(f"   ✅ Сгенерировано: {len(operations)} операций")
        print(f"   💾 Сохранено: {saved_count} операций")
        print(f"   💰 Стоимость: ${dag_cost:.6f}")
        print()
    
    # Итоговая статистика
    print("=" * 60)
    print("📊 ИТОГОВАЯ СТАТИСТИКА:")
    print(f"🏭 Всего DAG: {len(DAG_CONFIGS)}")
    print(f"⚙️ Всего операций: {total_operations}")
    print(f"💰 Общая стоимость: ${total_cost:.6f}")
    print(f"📅 Период: последние 7 дней")
    
    print(f"\n📈 ТОП-5 DAG ПО СТОИМОСТИ:")
    sorted_dags = sorted(dag_stats.items(), key=lambda x: x[1]['cost'], reverse=True)
    for i, (dag_id, stats) in enumerate(sorted_dags[:5], 1):
        print(f"   {i}. {dag_id}: ${stats['cost']:.6f} ({stats['operations']} операций)")
    
    print(f"\n🎯 ПРОВЕРКА:")
    print("1. Запустите: curl http://localhost:9000/cost-stats")
    print("2. Или откройте: http://localhost:9000/cost-stats в браузере")
    print("3. Проверьте, что все DAG отображаются в статистике")
    
    print(f"\n🧹 ОЧИСТКА (если нужно):")
    print("Для очистки тестовых данных выполните:")
    print("redis-cli FLUSHDB")

if __name__ == "__main__":
    main()
